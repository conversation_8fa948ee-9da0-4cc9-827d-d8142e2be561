# Navigation System Developer Guide

**Version**: 3.0.0  
**Date**: 2025-07-28  
**Author**: Syndicaps Team

## Table of Contents

1. [Quick Start](#quick-start)
2. [API Reference](#api-reference)
3. [Usage Examples](#usage-examples)
4. [Customization Guide](#customization-guide)
5. [Performance Optimization](#performance-optimization)
6. [Accessibility Features](#accessibility-features)
7. [Testing Guide](#testing-guide)
8. [Troubleshooting](#troubleshooting)

## Quick Start

### Installation

The navigation system is built into the Syndicaps application. No additional installation required.

### Basic Usage

```typescript
import { ConsolidatedNavigation } from '@/components/navigation/ConsolidatedNavigation'
import { NavigationProvider } from '@/lib/navigation/NavigationProvider'

function App() {
  return (
    <NavigationProvider profile={userProfile} wishlistItemCount={5}>
      <ConsolidatedNavigation
        profile={userProfile}
        wishlistItemCount={5}
        variant="auto"
        showSearch={true}
        onNavigate={(href) => router.push(href)}
      />
    </NavigationProvider>
  )
}
```

## API Reference

### ConsolidatedNavigation

Main navigation component that replaces all legacy navigation components.

#### Props

```typescript
interface ConsolidatedNavigationProps {
  // Required
  profile: UserProfile | null
  wishlistItemCount: number
  
  // Optional
  loading?: boolean
  variant?: NavigationVariant | 'auto'
  showSearch?: boolean
  showQuickSettings?: boolean
  showBreadcrumbs?: boolean
  showLabels?: boolean
  compact?: boolean
  className?: string
  
  // Callbacks
  onNavigate?: (href: string) => void
  onSearchToggle?: (show: boolean) => void
  onSettingsToggle?: (show: boolean) => void
}
```

#### Variants

- **`auto`** (default): Automatically adapts based on screen size
- **`desktop`**: Full sidebar navigation with all features
- **`tablet`**: Compact sidebar for medium screens
- **`mobile`**: Bottom navigation bar for mobile devices
- **`admin`**: Enhanced navigation for admin interfaces

### Pre-configured Components

#### DesktopNavigation

```typescript
<DesktopNavigation
  profile={profile}
  wishlistItemCount={count}
  showSearch={true}
  showQuickSettings={true}
/>
```

#### MobileNavigation

```typescript
<MobileNavigation
  profile={profile}
  wishlistItemCount={count}
  showLabels={true}
  compact={false}
/>
```

### NavigationProvider

Context provider for centralized navigation state management.

```typescript
interface NavigationProviderProps {
  profile: UserProfile | null
  wishlistItemCount: number
  children: React.ReactNode
}
```

## Usage Examples

### Basic Implementation

```typescript
// Simple desktop navigation
<ConsolidatedNavigation
  profile={user}
  wishlistItemCount={wishlistCount}
  variant="desktop"
  onNavigate={(href) => router.push(href)}
/>
```

### Responsive Navigation

```typescript
// Automatically adapts to screen size
<ConsolidatedNavigation
  profile={user}
  wishlistItemCount={wishlistCount}
  variant="auto"
  showSearch={true}
  showQuickSettings={true}
/>
```

### Mobile-First Implementation

```typescript
// Mobile-optimized navigation
<MobileNavigation
  profile={user}
  wishlistItemCount={wishlistCount}
  showLabels={true}
  onNavigate={(href) => {
    // Mobile-specific navigation logic
    router.push(href)
    closeMobileMenu()
  }}
/>
```

### Advanced Features

```typescript
// Full-featured navigation with all options
<ConsolidatedNavigation
  profile={user}
  wishlistItemCount={wishlistCount}
  variant="desktop"
  showSearch={true}
  showQuickSettings={true}
  showBreadcrumbs={true}
  showLabels={true}
  compact={false}
  className="custom-navigation"
  onNavigate={(href) => {
    // Track navigation
    analytics.track('navigation', { href })
    router.push(href)
  }}
  onSearchToggle={(show) => {
    analytics.track('search_toggle', { show })
  }}
/>
```

## Customization Guide

### Styling Customization

#### CSS Classes

The navigation system uses modular CSS classes that can be customized:

```css
/* Custom navigation container */
.custom-navigation {
  --nav-bg: #1a1a2e;
  --nav-text: #ffffff;
  --nav-accent: #8b5cf6;
}

/* Custom navigation items */
.custom-navigation .nav-item {
  border-radius: 12px;
  padding: 16px;
}

/* Custom hover effects */
.custom-navigation .nav-item:hover {
  background: linear-gradient(135deg, var(--nav-accent), transparent);
}
```

#### Theme Variables

```css
:root {
  --nav-primary-bg: #1f2937;
  --nav-secondary-bg: #374151;
  --nav-text-primary: #f9fafb;
  --nav-text-secondary: #d1d5db;
  --nav-accent: #8b5cf6;
  --nav-border: #4b5563;
}
```

### Component Customization

#### Custom Navigation Items

```typescript
import { NavigationItem } from '@/types/navigation'

const customNavigationItems: NavigationItem[] = [
  {
    id: 'custom-dashboard',
    icon: CustomDashboardIcon,
    label: 'Custom Dashboard',
    href: '/custom-dashboard',
    description: 'Your custom dashboard',
    category: 'custom',
    priority: 1
  }
]

// Use with NavigationProvider
<NavigationProvider 
  profile={profile} 
  wishlistItemCount={count}
  customItems={customNavigationItems}
>
  <ConsolidatedNavigation />
</NavigationProvider>
```

#### Custom Search Engine

```typescript
import { NavigationSearchEngine } from '@/lib/navigation/SearchEngine'

const customSearchEngine = new NavigationSearchEngine({
  fuzzyThreshold: 0.3,
  maxResults: 10,
  enableAnalytics: true,
  customFilters: {
    category: (items, filter) => items.filter(item => item.category === filter),
    priority: (items, filter) => items.filter(item => item.priority >= filter)
  }
})
```

### Accessibility Customization

#### Custom Keyboard Shortcuts

```typescript
import { useNavigationAccessibility } from '@/hooks/useNavigationAccessibility'

const CustomNavigation = () => {
  const accessibility = useNavigationAccessibility({
    customShortcuts: {
      'ctrl+shift+n': () => navigateToNewItem(),
      'alt+h': () => navigateToHome(),
      'ctrl+/': () => showHelpDialog()
    }
  })

  return <ConsolidatedNavigation />
}
```

#### Custom Skip Links

```typescript
import { SkipLinks } from '@/components/navigation/SkipLinks'

const customSkipLinks = [
  {
    id: 'custom-content',
    label: 'Skip to custom content',
    target: '#custom-content',
    shortcut: 'Alt+C'
  }
]

<SkipLinks links={customSkipLinks} />
```

## Performance Optimization

### Lazy Loading

```typescript
import { LazyNavigationComponents } from '@/lib/navigation/PerformanceOptimization'

// Lazy load navigation for better performance
const LazyNavigation = LazyNavigationComponents.ConsolidatedNavigation

<Suspense fallback={<NavigationSkeleton />}>
  <LazyNavigation profile={profile} wishlistItemCount={count} />
</Suspense>
```

### Performance Monitoring

```typescript
import { navigationPerformanceMonitor } from '@/lib/navigation/PerformanceOptimization'

// Monitor navigation performance
useEffect(() => {
  const metrics = navigationPerformanceMonitor.getMetrics()
  console.log('Navigation Performance:', metrics)
  
  const report = navigationPerformanceMonitor.generateReport()
  console.log(report)
}, [])
```

### Bundle Size Optimization

```typescript
// Import only what you need
import { ConsolidatedNavigation } from '@/components/navigation/ConsolidatedNavigation'
// Instead of importing the entire navigation module

// Use tree-shakable utilities
import { NavigationUtils } from '@/lib/navigation/PerformanceOptimization'
const debouncedSearch = NavigationUtils.debounce(searchFunction, 300)
```

## Accessibility Features

### Built-in Accessibility

- **WCAG 2.1 AA Compliance**: Full compliance with accessibility standards
- **Keyboard Navigation**: Complete keyboard-only navigation support
- **Screen Reader Support**: Comprehensive ARIA labels and live regions
- **Focus Management**: Proper focus trapping and restoration
- **High Contrast Mode**: Automatic detection and adaptation
- **Reduced Motion**: Respects user motion preferences

### Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+K` | Toggle search |
| `Ctrl+Alt+S` | Toggle contextual suggestions |
| `Escape` | Close overlays |
| `Arrow Keys` | Navigate through items |
| `Alt+1-9` | Skip links navigation |
| `Tab/Shift+Tab` | Focus navigation |

### Testing Accessibility

```typescript
import { axe } from 'jest-axe'

test('navigation is accessible', async () => {
  const { container } = render(<ConsolidatedNavigation />)
  const results = await axe(container)
  expect(results).toHaveNoViolations()
})
```

## Testing Guide

### Unit Testing

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { ConsolidatedNavigation } from '@/components/navigation/ConsolidatedNavigation'

test('renders navigation correctly', () => {
  render(
    <ConsolidatedNavigation
      profile={mockProfile}
      wishlistItemCount={5}
    />
  )
  
  expect(screen.getByRole('navigation')).toBeInTheDocument()
})
```

### Integration Testing

```typescript
test('navigation integrates with router', async () => {
  const mockPush = jest.fn()
  jest.mock('next/navigation', () => ({
    useRouter: () => ({ push: mockPush })
  }))
  
  render(<ConsolidatedNavigation />)
  
  fireEvent.click(screen.getByText('Account'))
  expect(mockPush).toHaveBeenCalledWith('/profile/account')
})
```

### Accessibility Testing

```typescript
import userEvent from '@testing-library/user-event'

test('supports keyboard navigation', async () => {
  const user = userEvent.setup()
  render(<ConsolidatedNavigation />)
  
  await user.keyboard('{Control>}k{/Control}')
  expect(screen.getByPlaceholderText(/search/i)).toBeInTheDocument()
})
```

## Troubleshooting

### Common Issues

#### Navigation Not Rendering

**Problem**: Navigation component doesn't appear
**Solution**: Ensure NavigationProvider is wrapping your app

```typescript
// ❌ Wrong
<ConsolidatedNavigation profile={profile} />

// ✅ Correct
<NavigationProvider profile={profile}>
  <ConsolidatedNavigation profile={profile} />
</NavigationProvider>
```

#### Keyboard Shortcuts Not Working

**Problem**: Ctrl+K doesn't open search
**Solution**: Check if accessibility features are enabled

```typescript
<ConsolidatedNavigation
  profile={profile}
  showSearch={true} // ✅ Enable search
  // Accessibility is enabled by default
/>
```

#### Styling Issues

**Problem**: Custom styles not applying
**Solution**: Check CSS specificity and import order

```typescript
// Import navigation CSS first
import '@/styles/navigation.css'
// Then your custom styles
import './custom-navigation.css'
```

#### Performance Issues

**Problem**: Navigation is slow to render
**Solution**: Use lazy loading and performance monitoring

```typescript
import { LazyNavigationComponents, navigationPerformanceMonitor } from '@/lib/navigation/PerformanceOptimization'

// Monitor performance
const metrics = navigationPerformanceMonitor.getMetrics()
if (metrics.renderTime > 100) {
  console.warn('Navigation render time exceeds threshold')
}
```

### Debug Mode

Enable debug mode for development:

```typescript
<ConsolidatedNavigation
  profile={profile}
  wishlistItemCount={count}
  // Add debug prop (development only)
  debug={process.env.NODE_ENV === 'development'}
/>
```

### Support

For additional support:
- **Documentation**: `/docs/navigation-system/`
- **Examples**: `/src/components/navigation/examples/`
- **Issues**: GitHub issues with `navigation` label
- **Team**: <EMAIL>

---

**Next Steps**: 
- Review [Migration Guide](./navigation-migration-guide.md) for upgrading from legacy components
- Check [Testing Guide](./navigation-testing-guide.md) for comprehensive testing procedures
- See [Performance Guide](./navigation-performance-guide.md) for optimization strategies
