{"summary": {"total": 25, "passed": 25, "failed": 0, "successRate": 100}, "details": [{"name": "Parameter Validation: Valid User ID", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.832Z"}, {"name": "Parameter Validation: Invalid User ID (SQL Injection)", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Parameter Validation: Invalid User ID (XSS)", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Parameter Validation: Invalid User ID (Path Traversal)", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Parameter Validation: Valid Product ID", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Parameter Validation: Invalid Product ID (Too Long)", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Parameter Validation: Invalid Product ID (Special Characters)", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Authentication: No Authentication Token", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Authentication: Invalid Authentication Token", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Authentication: <PERSON><PERSON>", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Authentication: <PERSON><PERSON>", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Authentication: Regular User Token", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Authorization: Admin accessing user management", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Authorization: Admin accessing settings (superadmin only)", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Authorization: Superadmin accessing settings", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Authorization: <PERSON>min without required permission", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Audit Logging: Event Creation", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Audit Logging: Metrics Calculation", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Audit Logging: Risk Score Calculation", "passed": true, "details": "Risk score: 90", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Rate Limiting: Normal Usage", "passed": true, "details": "Expected 5 allowed requests, got 5", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Rate Limiting: Limit Exceeded", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.833Z"}, {"name": "Security Headers: X-Content-Type-Options", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.834Z"}, {"name": "Security Headers: X-Frame-Options", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.834Z"}, {"name": "Security Headers: X-XSS-Protection", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.834Z"}, {"name": "Security Headers: <PERSON><PERSON><PERSON>-<PERSON>", "passed": true, "details": "", "timestamp": "2025-07-28T06:33:08.834Z"}], "errors": [], "timestamp": "2025-07-28T06:33:08.834Z"}