#!/usr/bin/env node

/**
 * Accessibility Audit Script for Navigation System
 * 
 * Comprehensive accessibility testing including:
 * - WCAG 2.1 AA compliance validation
 * - Screen reader compatibility testing
 * - Keyboard navigation verification
 * - Color contrast analysis
 * - Focus management testing
 * 
 * Usage: node scripts/accessibility-audit.js
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// ===== CONFIGURATION =====

const AUDIT_CONFIG = {
  // WCAG 2.1 AA compliance levels
  wcagLevel: 'AA',
  wcagVersion: '2.1',
  
  // Test components
  components: [
    'ConsolidatedNavigation',
    'DesktopNavigation', 
    'MobileNavigation',
    'NavigationSearch',
    'ContextualSuggestionsPanel',
    'SkipLinks'
  ],
  
  // Accessibility tools
  tools: {
    axe: true,
    lighthouse: true,
    pa11y: true,
    jest: true
  },
  
  // Output configuration
  outputDir: './accessibility-reports',
  generateReport: true,
  verbose: true
}

// ===== UTILITY FUNCTIONS =====

const log = (message, level = 'info') => {
  const timestamp = new Date().toISOString()
  const prefix = {
    info: '📋',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    debug: '🔍'
  }[level] || '📋'
  
  console.log(`${prefix} [${timestamp}] ${message}`)
}

const ensureOutputDir = () => {
  if (!fs.existsSync(AUDIT_CONFIG.outputDir)) {
    fs.mkdirSync(AUDIT_CONFIG.outputDir, { recursive: true })
    log(`Created output directory: ${AUDIT_CONFIG.outputDir}`)
  }
}

const runCommand = (command, description) => {
  try {
    log(`Running: ${description}`)
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' })
    log(`✅ Completed: ${description}`, 'success')
    return { success: true, output }
  } catch (error) {
    log(`❌ Failed: ${description} - ${error.message}`, 'error')
    return { success: false, error: error.message, output: error.stdout }
  }
}

// ===== ACCESSIBILITY TESTS =====

const runJestAccessibilityTests = () => {
  log('Running Jest accessibility tests...', 'info')
  
  const testCommand = `
    npm test -- --testPathPattern="__tests__.*Accessibility" 
    --coverage --coverageDirectory=${AUDIT_CONFIG.outputDir}/coverage
    --testResultsProcessor=jest-sonar-reporter
  `
  
  return runCommand(testCommand, 'Jest accessibility tests')
}

const runAxeAudit = () => {
  log('Running axe-core accessibility audit...', 'info')
  
  // Create axe audit script
  const axeScript = `
const { AxePuppeteer } = require('@axe-core/puppeteer')
const puppeteer = require('puppeteer')

async function runAxeAudit() {
  const browser = await puppeteer.launch({ headless: true })
  const page = await browser.newPage()
  
  // Test navigation components
  const components = ${JSON.stringify(AUDIT_CONFIG.components)}
  const results = {}
  
  for (const component of components) {
    try {
      // Navigate to component test page
      await page.goto(\`http://localhost:3000/test/\${component.toLowerCase()}\`)
      
      // Run axe audit
      const axe = new AxePuppeteer(page)
      const result = await axe
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
        .analyze()
      
      results[component] = {
        violations: result.violations,
        passes: result.passes.length,
        incomplete: result.incomplete.length,
        inapplicable: result.inapplicable.length
      }
      
      console.log(\`✅ \${component}: \${result.violations.length} violations\`)
    } catch (error) {
      console.error(\`❌ \${component}: \${error.message}\`)
      results[component] = { error: error.message }
    }
  }
  
  // Save results
  require('fs').writeFileSync(
    '${AUDIT_CONFIG.outputDir}/axe-results.json',
    JSON.stringify(results, null, 2)
  )
  
  await browser.close()
  return results
}

runAxeAudit().catch(console.error)
  `
  
  fs.writeFileSync('./temp-axe-audit.js', axeScript)
  const result = runCommand('node temp-axe-audit.js', 'axe-core audit')
  fs.unlinkSync('./temp-axe-audit.js')
  
  return result
}

const runLighthouseAudit = () => {
  log('Running Lighthouse accessibility audit...', 'info')
  
  const lighthouseCommand = `
    lighthouse http://localhost:3000/profile/account 
    --only-categories=accessibility 
    --output=json 
    --output-path=${AUDIT_CONFIG.outputDir}/lighthouse-accessibility.json
    --chrome-flags="--headless --no-sandbox"
  `
  
  return runCommand(lighthouseCommand, 'Lighthouse accessibility audit')
}

const runPa11yAudit = () => {
  log('Running Pa11y accessibility audit...', 'info')
  
  const pa11yCommand = `
    pa11y http://localhost:3000/profile/account 
    --standard WCAG2AA 
    --reporter json 
    > ${AUDIT_CONFIG.outputDir}/pa11y-results.json
  `
  
  return runCommand(pa11yCommand, 'Pa11y accessibility audit')
}

const runKeyboardNavigationTest = () => {
  log('Running keyboard navigation tests...', 'info')
  
  const keyboardScript = `
const puppeteer = require('puppeteer')

async function testKeyboardNavigation() {
  const browser = await puppeteer.launch({ headless: true })
  const page = await browser.newPage()
  
  await page.goto('http://localhost:3000/profile/account')
  
  const results = {
    tabNavigation: false,
    keyboardShortcuts: false,
    focusManagement: false,
    skipLinks: false
  }
  
  try {
    // Test tab navigation
    await page.keyboard.press('Tab')
    const activeElement = await page.evaluate(() => document.activeElement.tagName)
    results.tabNavigation = activeElement !== 'BODY'
    
    // Test Ctrl+K shortcut
    await page.keyboard.down('Control')
    await page.keyboard.press('KeyK')
    await page.keyboard.up('Control')
    
    await page.waitForTimeout(500)
    const searchVisible = await page.evaluate(() => 
      document.querySelector('[placeholder*="search" i]') !== null
    )
    results.keyboardShortcuts = searchVisible
    
    // Test Escape key
    await page.keyboard.press('Escape')
    await page.waitForTimeout(500)
    const searchHidden = await page.evaluate(() => 
      document.querySelector('[placeholder*="search" i]') === null
    )
    results.focusManagement = searchHidden
    
    // Test skip links (Alt+1)
    await page.keyboard.down('Alt')
    await page.keyboard.press('Digit1')
    await page.keyboard.up('Alt')
    
    await page.waitForTimeout(500)
    const skipLinksVisible = await page.evaluate(() => 
      document.querySelector('.skip-links') !== null
    )
    results.skipLinks = skipLinksVisible
    
  } catch (error) {
    console.error('Keyboard navigation test error:', error.message)
  }
  
  require('fs').writeFileSync(
    '${AUDIT_CONFIG.outputDir}/keyboard-navigation.json',
    JSON.stringify(results, null, 2)
  )
  
  await browser.close()
  return results
}

testKeyboardNavigation().catch(console.error)
  `
  
  fs.writeFileSync('./temp-keyboard-test.js', keyboardScript)
  const result = runCommand('node temp-keyboard-test.js', 'Keyboard navigation test')
  fs.unlinkSync('./temp-keyboard-test.js')
  
  return result
}

const runColorContrastAudit = () => {
  log('Running color contrast analysis...', 'info')
  
  const contrastScript = `
const puppeteer = require('puppeteer')

async function analyzeColorContrast() {
  const browser = await puppeteer.launch({ headless: true })
  const page = await browser.newPage()
  
  await page.goto('http://localhost:3000/profile/account')
  
  const contrastResults = await page.evaluate(() => {
    const elements = document.querySelectorAll('.nav-item, .nav-item-text, button')
    const results = []
    
    elements.forEach((element, index) => {
      const styles = window.getComputedStyle(element)
      const color = styles.color
      const backgroundColor = styles.backgroundColor
      
      // Simple contrast ratio calculation (simplified)
      const getLuminance = (rgb) => {
        const [r, g, b] = rgb.match(/\\d+/g).map(Number)
        return (0.299 * r + 0.587 * g + 0.114 * b) / 255
      }
      
      try {
        const textLum = getLuminance(color)
        const bgLum = getLuminance(backgroundColor)
        const contrast = (Math.max(textLum, bgLum) + 0.05) / (Math.min(textLum, bgLum) + 0.05)
        
        results.push({
          element: element.tagName + (element.className ? '.' + element.className : ''),
          color,
          backgroundColor,
          contrast: Math.round(contrast * 100) / 100,
          wcagAA: contrast >= 4.5,
          wcagAAA: contrast >= 7
        })
      } catch (error) {
        // Skip elements with invalid colors
      }
    })
    
    return results
  })
  
  require('fs').writeFileSync(
    '${AUDIT_CONFIG.outputDir}/color-contrast.json',
    JSON.stringify(contrastResults, null, 2)
  )
  
  await browser.close()
  return contrastResults
}

analyzeColorContrast().catch(console.error)
  `
  
  fs.writeFileSync('./temp-contrast-test.js', contrastScript)
  const result = runCommand('node temp-contrast-test.js', 'Color contrast analysis')
  fs.unlinkSync('./temp-contrast-test.js')
  
  return result
}

// ===== REPORT GENERATION =====

const generateAccessibilityReport = (results) => {
  log('Generating accessibility report...', 'info')
  
  const reportData = {
    timestamp: new Date().toISOString(),
    wcagLevel: AUDIT_CONFIG.wcagLevel,
    wcagVersion: AUDIT_CONFIG.wcagVersion,
    components: AUDIT_CONFIG.components,
    results: results,
    summary: {
      totalTests: Object.keys(results).length,
      passed: Object.values(results).filter(r => r.success).length,
      failed: Object.values(results).filter(r => !r.success).length
    }
  }
  
  // Generate HTML report
  const htmlReport = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Accessibility Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; }
        .header { background: #8b5cf6; color: white; padding: 20px; border-radius: 8px; }
        .summary { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0; }
        .metric { background: #f3f4f6; padding: 20px; border-radius: 8px; text-align: center; }
        .metric.success { background: #d1fae5; }
        .metric.error { background: #fee2e2; }
        .results { margin-top: 30px; }
        .test-result { margin: 20px 0; padding: 20px; border-left: 4px solid #8b5cf6; background: #f9fafb; }
        .test-result.success { border-color: #10b981; }
        .test-result.error { border-color: #ef4444; }
        pre { background: #1f2937; color: #f9fafb; padding: 15px; border-radius: 6px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Navigation Accessibility Report</h1>
        <p>Generated: ${reportData.timestamp}</p>
        <p>WCAG ${reportData.wcagVersion} ${reportData.wcagLevel} Compliance</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <div style="font-size: 2em; font-weight: bold;">${reportData.summary.totalTests}</div>
        </div>
        <div class="metric success">
            <h3>Passed</h3>
            <div style="font-size: 2em; font-weight: bold;">${reportData.summary.passed}</div>
        </div>
        <div class="metric error">
            <h3>Failed</h3>
            <div style="font-size: 2em; font-weight: bold;">${reportData.summary.failed}</div>
        </div>
    </div>
    
    <div class="results">
        <h2>Test Results</h2>
        ${Object.entries(results).map(([test, result]) => `
            <div class="test-result ${result.success ? 'success' : 'error'}">
                <h3>${result.success ? '✅' : '❌'} ${test}</h3>
                <p><strong>Status:</strong> ${result.success ? 'PASSED' : 'FAILED'}</p>
                ${result.error ? `<p><strong>Error:</strong> ${result.error}</p>` : ''}
                ${result.output ? `<pre>${result.output}</pre>` : ''}
            </div>
        `).join('')}
    </div>
</body>
</html>
  `
  
  // Save reports
  fs.writeFileSync(`${AUDIT_CONFIG.outputDir}/accessibility-report.json`, JSON.stringify(reportData, null, 2))
  fs.writeFileSync(`${AUDIT_CONFIG.outputDir}/accessibility-report.html`, htmlReport)
  
  log(`📊 Report saved to ${AUDIT_CONFIG.outputDir}/accessibility-report.html`, 'success')
  
  return reportData
}

// ===== MAIN EXECUTION =====

async function runAccessibilityAudit() {
  log('🚀 Starting Navigation Accessibility Audit', 'info')
  log(`WCAG ${AUDIT_CONFIG.wcagVersion} ${AUDIT_CONFIG.wcagLevel} Compliance Testing`, 'info')
  
  ensureOutputDir()
  
  const results = {}
  
  // Run all accessibility tests
  if (AUDIT_CONFIG.tools.jest) {
    results.jestTests = runJestAccessibilityTests()
  }
  
  if (AUDIT_CONFIG.tools.axe) {
    results.axeAudit = runAxeAudit()
  }
  
  if (AUDIT_CONFIG.tools.lighthouse) {
    results.lighthouseAudit = runLighthouseAudit()
  }
  
  if (AUDIT_CONFIG.tools.pa11y) {
    results.pa11yAudit = runPa11yAudit()
  }
  
  // Custom tests
  results.keyboardNavigation = runKeyboardNavigationTest()
  results.colorContrast = runColorContrastAudit()
  
  // Generate comprehensive report
  if (AUDIT_CONFIG.generateReport) {
    const report = generateAccessibilityReport(results)
    
    // Summary
    log('📋 Accessibility Audit Summary:', 'info')
    log(`   Total Tests: ${report.summary.totalTests}`, 'info')
    log(`   Passed: ${report.summary.passed}`, 'success')
    log(`   Failed: ${report.summary.failed}`, report.summary.failed > 0 ? 'error' : 'success')
    
    if (report.summary.failed === 0) {
      log('🎉 All accessibility tests passed!', 'success')
    } else {
      log('⚠️  Some accessibility tests failed. Check the report for details.', 'warning')
    }
  }
  
  log('✅ Accessibility audit completed', 'success')
  log(`📊 View report: ${AUDIT_CONFIG.outputDir}/accessibility-report.html`, 'info')
}

// Run the audit
if (require.main === module) {
  runAccessibilityAudit().catch(error => {
    log(`❌ Audit failed: ${error.message}`, 'error')
    process.exit(1)
  })
}

module.exports = { runAccessibilityAudit, AUDIT_CONFIG }
