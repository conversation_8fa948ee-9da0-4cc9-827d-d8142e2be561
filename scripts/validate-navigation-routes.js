#!/usr/bin/env node

/**
 * Navigation Routes Validation Script
 * 
 * Validates that all routes defined in navigation constants have corresponding
 * page.tsx files and identifies any missing or broken routes.
 * 
 * <AUTHOR> Team
 */

const fs = require('fs')
const path = require('path')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// All routes from navigation constants
const NAVIGATION_ROUTES = {
  // Global Navigation
  global: [
    { route: '/', file: 'app/page.tsx', label: 'Home' },
    { route: '/shop', file: 'app/shop/page.tsx', label: 'Shop' },
    { route: '/community', file: 'app/community/page.tsx', label: 'Community' },
    { route: '/wishlist', file: 'app/wishlist/page.tsx', label: 'Wishlist' },
    { route: '/cart', file: 'app/cart/page.tsx', label: 'Cart' }
  ],
  
  // Profile Navigation - Account Category
  profileAccount: [
    { route: '/profile', file: 'app/profile/page.tsx', label: 'Profile Overview' },
    { route: '/profile/account', file: 'app/profile/account/page.tsx', label: 'Personal Information' },
    { route: '/profile/contact', file: 'app/profile/contact/page.tsx', label: 'Contact & Location' },
    { route: '/profile/social', file: 'app/profile/social/page.tsx', label: 'Social Links' }
  ],
  
  // Profile Navigation - Orders Category
  profileOrders: [
    { route: '/profile/orders', file: 'app/profile/orders/page.tsx', label: 'Order History' },
    { route: '/profile/orders/tracking', file: 'app/profile/orders/tracking/page.tsx', label: 'Track Orders' },
    { route: '/profile/activity', file: 'app/profile/activity/page.tsx', label: 'Adventure Log' },
    { route: '/profile/downloads', file: 'app/profile/downloads/page.tsx', label: 'Downloads' }
  ],
  
  // Profile Navigation - Rewards Category
  profileRewards: [
    { route: '/profile/rewards', file: 'app/profile/rewards/page.tsx', label: 'Treasure Vault' },
    { route: '/profile/points', file: 'app/profile/points/page.tsx', label: 'Collaboration Points' },
    { route: '/profile/achievements', file: 'app/profile/achievements/page.tsx', label: 'Shared Achievements' },
    { route: '/profile/challenges', file: 'app/profile/challenges/page.tsx', label: 'Community Challenges' }
  ],
  
  // Profile Navigation - Settings Category
  profileSettings: [
    { route: '/profile/preferences', file: 'app/profile/preferences/page.tsx', label: 'Preferences' },
    { route: '/profile/privacy', file: 'app/profile/privacy/page.tsx', label: 'Privacy Settings' },
    { route: '/profile/notifications', file: 'app/profile/notifications/page.tsx', label: 'Notifications' },
    { route: '/profile/billing', file: 'app/profile/billing/page.tsx', label: 'Billing & Payment' },
    { route: '/support', file: 'app/support/page.tsx', label: 'Help & Support' }
  ]
}

function validateRoutes() {
  log('🔍 Validating Navigation Routes...', 'bright')
  log('=' .repeat(50), 'blue')
  
  const results = {
    total: 0,
    existing: 0,
    missing: 0,
    missingRoutes: []
  }
  
  // Validate each category
  Object.entries(NAVIGATION_ROUTES).forEach(([category, routes]) => {
    log(`\n📂 ${category.toUpperCase()}`, 'cyan')
    log('-'.repeat(30), 'blue')
    
    routes.forEach(({ route, file, label }) => {
      results.total++
      
      if (fs.existsSync(file)) {
        results.existing++
        log(`  ✅ ${route} → ${file}`, 'green')
      } else {
        results.missing++
        results.missingRoutes.push({ route, file, label, category })
        log(`  ❌ ${route} → ${file} (MISSING)`, 'red')
      }
    })
  })
  
  // Summary
  log('\n📊 VALIDATION SUMMARY', 'bright')
  log('=' .repeat(50), 'blue')
  log(`📄 Total routes checked: ${results.total}`, 'blue')
  log(`✅ Existing routes: ${results.existing}`, 'green')
  log(`❌ Missing routes: ${results.missing}`, results.missing > 0 ? 'red' : 'green')
  
  if (results.missing > 0) {
    log('\n🚨 MISSING ROUTES DETAILS', 'bright')
    log('=' .repeat(50), 'red')
    
    results.missingRoutes.forEach(({ route, file, label, category }) => {
      log(`\n❌ Route: ${route}`, 'red')
      log(`   Label: ${label}`, 'yellow')
      log(`   Expected file: ${file}`, 'yellow')
      log(`   Category: ${category}`, 'yellow')
    })
    
    log('\n🔧 RECOMMENDED ACTIONS', 'bright')
    log('=' .repeat(50), 'yellow')
    
    results.missingRoutes.forEach(({ route, file, label }) => {
      const dir = path.dirname(file)
      log(`\n• Create missing route: ${route}`, 'yellow')
      log(`  mkdir -p ${dir}`, 'cyan')
      log(`  touch ${file}`, 'cyan')
    })
  }
  
  // Health score
  const healthScore = Math.round((results.existing / results.total) * 100)
  const healthColor = healthScore >= 95 ? 'green' : healthScore >= 80 ? 'yellow' : 'red'
  
  log(`\n🎯 ROUTE HEALTH SCORE: ${healthScore}%`, healthColor)
  
  if (healthScore < 100) {
    log('\n⚠️  Some routes are missing. Please create the missing files to ensure all navigation links work properly.', 'yellow')
  } else {
    log('\n🎉 All navigation routes are properly implemented!', 'green')
  }
  
  return results
}

// Additional validation for dynamic routes
function validateDynamicRoutes() {
  log('\n🔍 Validating Dynamic Routes...', 'bright')
  log('=' .repeat(50), 'blue')
  
  const dynamicRoutes = [
    { route: '/shop/[id]', file: 'app/shop/[id]/page.tsx', label: 'Product Detail' },
    { route: '/blog/[slug]', file: 'app/blog/[slug]/page.tsx', label: 'Blog Post' },
    { route: '/community/challenges/[id]', file: 'app/community/challenges/[id]/page.tsx', label: 'Challenge Detail' },
    { route: '/profile/orders/[orderId]', file: 'app/profile/orders/[orderId]/page.tsx', label: 'Order Detail' },
    { route: '/admin/products/[id]/edit', file: 'app/admin/products/[id]/edit/page.tsx', label: 'Product Edit' },
    { route: '/admin/users/[userId]', file: 'app/admin/users/[userId]/page.tsx', label: 'User Detail' }
  ]
  
  const dynamicResults = {
    total: 0,
    existing: 0,
    missing: 0,
    missingRoutes: []
  }
  
  dynamicRoutes.forEach(({ route, file, label }) => {
    dynamicResults.total++
    
    if (fs.existsSync(file)) {
      dynamicResults.existing++
      log(`  ✅ ${route} → ${file}`, 'green')
    } else {
      dynamicResults.missing++
      dynamicResults.missingRoutes.push({ route, file, label })
      log(`  ❌ ${route} → ${file} (MISSING)`, 'red')
    }
  })
  
  log(`\n📊 Dynamic Routes: ${dynamicResults.existing}/${dynamicResults.total} existing`, 
      dynamicResults.missing > 0 ? 'yellow' : 'green')
  
  return dynamicResults
}

// Run validation
async function main() {
  try {
    log('🚀 Starting Navigation Routes Validation', 'bright')
    log('=' .repeat(60), 'blue')
    
    const staticResults = validateRoutes()
    const dynamicResults = validateDynamicRoutes()
    
    const totalRoutes = staticResults.total + dynamicResults.total
    const totalExisting = staticResults.existing + dynamicResults.existing
    const totalMissing = staticResults.missing + dynamicResults.missing
    
    log('\n🎯 OVERALL SUMMARY', 'bright')
    log('=' .repeat(50), 'blue')
    log(`📄 Total routes: ${totalRoutes}`, 'blue')
    log(`✅ Existing: ${totalExisting}`, 'green')
    log(`❌ Missing: ${totalMissing}`, totalMissing > 0 ? 'red' : 'green')
    
    const overallHealth = Math.round((totalExisting / totalRoutes) * 100)
    log(`🎯 Overall Health: ${overallHealth}%`, overallHealth >= 95 ? 'green' : 'yellow')
    
    if (totalMissing === 0) {
      log('\n🎉 All navigation routes are properly implemented!', 'green')
      process.exit(0)
    } else {
      log(`\n⚠️  ${totalMissing} routes need to be created for complete functionality.`, 'yellow')
      process.exit(1)
    }
    
  } catch (error) {
    log(`\n❌ Error during validation: ${error.message}`, 'red')
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { validateRoutes, validateDynamicRoutes }
