#!/usr/bin/env node

/**
 * Navigation Migration Verification Script
 * 
 * Verifies that the navigation migration was successful by checking:
 * - All profile pages use consolidated navigation
 * - NavigationProvider is properly integrated
 * - No legacy navigation imports remain
 * - All navigation functionality works correctly
 */

const fs = require('fs')
const path = require('path')
const glob = require('glob')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function checkProfilePages() {
  log('🔍 Checking Profile Pages Migration...', 'bright')
  
  const profilePages = glob.sync('app/profile/**/page.tsx')
  const results = {
    total: 0,
    migrated: 0,
    needsMigration: 0,
    errors: []
  }

  profilePages.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      results.total++

      if (!content.includes('ProfileLayout')) {
        return // Skip files that don't use ProfileLayout
      }

      if (content.includes('navigation="consolidated"')) {
        results.migrated++
        log(`  ✅ ${filePath}`, 'green')
      } else if (content.includes('<ProfileLayout>') || 
                 content.includes('navigation="unified"') ||
                 content.includes('navigation="smart"') ||
                 content.includes('navigation="simple"')) {
        results.needsMigration++
        log(`  ❌ ${filePath} - Still using legacy navigation`, 'red')
        results.errors.push(`${filePath}: Legacy navigation detected`)
      }
    } catch (error) {
      results.errors.push(`${filePath}: ${error.message}`)
      log(`  ❌ ${filePath} - Error: ${error.message}`, 'red')
    }
  })

  log(`\n📊 Profile Pages Summary:`, 'bright')
  log(`  📄 Total pages: ${results.total}`, 'blue')
  log(`  ✅ Migrated: ${results.migrated}`, 'green')
  log(`  ❌ Needs migration: ${results.needsMigration}`, results.needsMigration > 0 ? 'red' : 'green')

  return results
}

function checkNavigationProvider() {
  log('\n🔍 Checking NavigationProvider Integration...', 'bright')
  
  const clientLayoutPath = 'src/components/layout/ClientLayout.tsx'
  const results = {
    hasProvider: false,
    hasImport: false,
    errors: []
  }

  try {
    const content = fs.readFileSync(clientLayoutPath, 'utf8')
    
    if (content.includes('import { NavigationProvider }')) {
      results.hasImport = true
      log(`  ✅ NavigationProvider import found`, 'green')
    } else {
      results.errors.push('NavigationProvider import missing')
      log(`  ❌ NavigationProvider import missing`, 'red')
    }

    if (content.includes('<NavigationProvider')) {
      results.hasProvider = true
      log(`  ✅ NavigationProvider component found`, 'green')
    } else {
      results.errors.push('NavigationProvider component missing')
      log(`  ❌ NavigationProvider component missing`, 'red')
    }

  } catch (error) {
    results.errors.push(`ClientLayout check failed: ${error.message}`)
    log(`  ❌ Error checking ClientLayout: ${error.message}`, 'red')
  }

  return results
}

function checkLegacyImports() {
  log('\n🔍 Checking for Legacy Navigation Imports...', 'bright')
  
  const allFiles = glob.sync('src/**/*.{ts,tsx}')
  const legacyImports = [
    'SmartNavigation',
    'ProfileNavigation', 
    'UnifiedNavigation',
    'ProfileBottomNav',
    'MobileBottomNav'
  ]
  
  const results = {
    filesChecked: 0,
    legacyImportsFound: [],
    cleanFiles: 0
  }

  allFiles.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      results.filesChecked++
      
      let hasLegacyImports = false
      legacyImports.forEach(legacyImport => {
        if (content.includes(`import ${legacyImport}`) || 
            content.includes(`import { ${legacyImport}`) ||
            content.includes(`from './layout/${legacyImport}`)) {
          hasLegacyImports = true
          results.legacyImportsFound.push({
            file: filePath,
            import: legacyImport
          })
        }
      })

      if (!hasLegacyImports) {
        results.cleanFiles++
      }
    } catch (error) {
      // Skip files that can't be read
    }
  })

  if (results.legacyImportsFound.length === 0) {
    log(`  ✅ No legacy imports found in ${results.filesChecked} files`, 'green')
  } else {
    log(`  ❌ Found ${results.legacyImportsFound.length} legacy imports:`, 'red')
    results.legacyImportsFound.forEach(({ file, import: importName }) => {
      log(`    • ${file}: ${importName}`, 'red')
    })
  }

  return results
}

function checkConsolidatedNavigation() {
  log('\n🔍 Checking ConsolidatedNavigation Component...', 'bright')
  
  const consolidatedNavPath = 'src/components/navigation/ConsolidatedNavigation.tsx'
  const results = {
    exists: false,
    hasExports: false,
    errors: []
  }

  try {
    if (fs.existsSync(consolidatedNavPath)) {
      results.exists = true
      log(`  ✅ ConsolidatedNavigation component exists`, 'green')
      
      const content = fs.readFileSync(consolidatedNavPath, 'utf8')
      
      if (content.includes('export') && content.includes('ConsolidatedNavigation')) {
        results.hasExports = true
        log(`  ✅ ConsolidatedNavigation exports found`, 'green')
      } else {
        results.errors.push('ConsolidatedNavigation exports missing')
        log(`  ❌ ConsolidatedNavigation exports missing`, 'red')
      }
    } else {
      results.errors.push('ConsolidatedNavigation component file missing')
      log(`  ❌ ConsolidatedNavigation component file missing`, 'red')
    }
  } catch (error) {
    results.errors.push(`ConsolidatedNavigation check failed: ${error.message}`)
    log(`  ❌ Error checking ConsolidatedNavigation: ${error.message}`, 'red')
  }

  return results
}

function generateReport(profileResults, providerResults, legacyResults, consolidatedResults) {
  log('\n📋 Migration Verification Report', 'bright')
  log('=' .repeat(50), 'cyan')
  
  const allErrors = [
    ...profileResults.errors,
    ...providerResults.errors,
    ...consolidatedResults.errors
  ]

  if (allErrors.length === 0 && 
      profileResults.needsMigration === 0 && 
      legacyResults.legacyImportsFound.length === 0) {
    log('\n🎉 MIGRATION SUCCESSFUL! 🎉', 'green')
    log('All checks passed. Navigation migration is complete.', 'green')
  } else {
    log('\n⚠️  MIGRATION ISSUES DETECTED', 'yellow')
    
    if (profileResults.needsMigration > 0) {
      log(`\n❌ Profile Pages: ${profileResults.needsMigration} pages need migration`, 'red')
    }
    
    if (legacyResults.legacyImportsFound.length > 0) {
      log(`\n❌ Legacy Imports: ${legacyResults.legacyImportsFound.length} legacy imports found`, 'red')
    }
    
    if (allErrors.length > 0) {
      log(`\n❌ Errors: ${allErrors.length} errors detected`, 'red')
      allErrors.forEach(error => log(`  • ${error}`, 'red'))
    }
  }

  log('\n📊 Summary Statistics:', 'bright')
  log(`  Profile Pages Migrated: ${profileResults.migrated}/${profileResults.total}`, 'blue')
  log(`  NavigationProvider: ${providerResults.hasProvider ? '✅' : '❌'}`, 'blue')
  log(`  ConsolidatedNavigation: ${consolidatedResults.exists ? '✅' : '❌'}`, 'blue')
  log(`  Legacy Imports: ${legacyResults.legacyImportsFound.length} found`, 'blue')
  log(`  Files Checked: ${legacyResults.filesChecked}`, 'blue')
}

function main() {
  log('🚀 Navigation Migration Verification', 'bright')
  log('Checking migration status and component integrity...\n', 'cyan')

  const profileResults = checkProfilePages()
  const providerResults = checkNavigationProvider()
  const legacyResults = checkLegacyImports()
  const consolidatedResults = checkConsolidatedNavigation()

  generateReport(profileResults, providerResults, legacyResults, consolidatedResults)
}

// Run the verification
if (require.main === module) {
  main()
}
