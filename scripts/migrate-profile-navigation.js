#!/usr/bin/env node

/**
 * Profile Navigation Migration Script
 * 
 * Automatically migrates ProfileLayout components to use consolidated navigation
 * across all profile pages in the application.
 * 
 * Usage: node scripts/migrate-profile-navigation.js
 */

const fs = require('fs')
const path = require('path')
const glob = require('glob')

// Configuration
const PROFILE_PAGES_PATTERN = 'app/profile/**/page.tsx'
const BACKUP_DIR = 'backups/navigation-migration'
const DRY_RUN = process.argv.includes('--dry-run')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function createBackup(filePath, content) {
  const backupPath = path.join(BACKUP_DIR, filePath)
  const backupDir = path.dirname(backupPath)
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true })
  }
  
  fs.writeFileSync(backupPath, content)
  log(`  📁 Backup created: ${backupPath}`, 'blue')
}

function migrateProfileLayout(content, filePath) {
  let modified = false
  let changes = []

  // Pattern 1: <ProfileLayout> -> <ProfileLayout navigation="consolidated">
  const pattern1 = /<ProfileLayout>/g
  if (pattern1.test(content)) {
    content = content.replace(pattern1, '<ProfileLayout navigation="consolidated">')
    modified = true
    changes.push('Added consolidated navigation to ProfileLayout')
  }

  // Pattern 2: <ProfileLayout navigation="unified"> -> <ProfileLayout navigation="consolidated">
  const pattern2 = /<ProfileLayout\s+navigation="unified">/g
  if (pattern2.test(content)) {
    content = content.replace(pattern2, '<ProfileLayout navigation="consolidated">')
    modified = true
    changes.push('Changed unified navigation to consolidated')
  }

  // Pattern 3: <ProfileLayout navigation="smart"> -> <ProfileLayout navigation="consolidated">
  const pattern3 = /<ProfileLayout\s+navigation="smart">/g
  if (pattern3.test(content)) {
    content = content.replace(pattern3, '<ProfileLayout navigation="consolidated">')
    modified = true
    changes.push('Changed smart navigation to consolidated')
  }

  // Pattern 4: <ProfileLayout navigation="simple"> -> <ProfileLayout navigation="consolidated">
  const pattern4 = /<ProfileLayout\s+navigation="simple">/g
  if (pattern4.test(content)) {
    content = content.replace(pattern4, '<ProfileLayout navigation="consolidated">')
    modified = true
    changes.push('Changed simple navigation to consolidated')
  }

  return { content, modified, changes }
}

function analyzeFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8')
  
  // Check if file uses ProfileLayout
  if (!content.includes('ProfileLayout')) {
    return null
  }

  // Check if already using consolidated navigation
  if (content.includes('navigation="consolidated"')) {
    return { status: 'already_migrated', filePath }
  }

  // Check what type of navigation is being used
  const navigationTypes = []
  if (content.includes('<ProfileLayout>')) navigationTypes.push('default')
  if (content.includes('navigation="unified"')) navigationTypes.push('unified')
  if (content.includes('navigation="smart"')) navigationTypes.push('smart')
  if (content.includes('navigation="simple"')) navigationTypes.push('simple')
  if (content.includes('navigation="mobile"')) navigationTypes.push('mobile')

  return {
    status: 'needs_migration',
    filePath,
    navigationTypes,
    content
  }
}

function main() {
  log('🚀 Starting Profile Navigation Migration', 'bright')
  log(`📁 Pattern: ${PROFILE_PAGES_PATTERN}`, 'cyan')
  log(`🔧 Mode: ${DRY_RUN ? 'DRY RUN' : 'LIVE MIGRATION'}`, DRY_RUN ? 'yellow' : 'green')
  log('')

  // Find all profile page files
  const files = glob.sync(PROFILE_PAGES_PATTERN)
  log(`📄 Found ${files.length} profile page files`, 'blue')
  log('')

  const results = {
    analyzed: 0,
    alreadyMigrated: 0,
    needsMigration: 0,
    migrated: 0,
    errors: 0
  }

  // Analyze each file
  for (const filePath of files) {
    try {
      results.analyzed++
      log(`🔍 Analyzing: ${filePath}`, 'cyan')

      const analysis = analyzeFile(filePath)
      
      if (!analysis) {
        log(`  ⏭️  No ProfileLayout usage found`, 'yellow')
        continue
      }

      if (analysis.status === 'already_migrated') {
        results.alreadyMigrated++
        log(`  ✅ Already using consolidated navigation`, 'green')
        continue
      }

      if (analysis.status === 'needs_migration') {
        results.needsMigration++
        log(`  🔄 Needs migration (${analysis.navigationTypes.join(', ')})`, 'yellow')

        if (!DRY_RUN) {
          // Create backup
          createBackup(filePath, analysis.content)

          // Perform migration
          const migration = migrateProfileLayout(analysis.content, filePath)
          
          if (migration.modified) {
            fs.writeFileSync(filePath, migration.content)
            results.migrated++
            log(`  ✅ Migrated successfully`, 'green')
            migration.changes.forEach(change => {
              log(`    • ${change}`, 'green')
            })
          } else {
            log(`  ⚠️  No changes made`, 'yellow')
          }
        } else {
          log(`  📝 Would migrate: ${analysis.navigationTypes.join(', ')} -> consolidated`, 'blue')
        }
      }

    } catch (error) {
      results.errors++
      log(`  ❌ Error: ${error.message}`, 'red')
    }

    log('')
  }

  // Summary
  log('📊 Migration Summary:', 'bright')
  log(`  📄 Files analyzed: ${results.analyzed}`, 'blue')
  log(`  ✅ Already migrated: ${results.alreadyMigrated}`, 'green')
  log(`  🔄 Needed migration: ${results.needsMigration}`, 'yellow')
  log(`  ✅ Successfully migrated: ${results.migrated}`, 'green')
  log(`  ❌ Errors: ${results.errors}`, results.errors > 0 ? 'red' : 'green')
  log('')

  if (DRY_RUN) {
    log('🔧 This was a dry run. No files were modified.', 'yellow')
    log('   Run without --dry-run to perform actual migration.', 'yellow')
  } else if (results.migrated > 0) {
    log('🎉 Migration completed successfully!', 'green')
    log(`📁 Backups saved to: ${BACKUP_DIR}`, 'blue')
  }
}

// Run the migration
if (require.main === module) {
  main()
}
