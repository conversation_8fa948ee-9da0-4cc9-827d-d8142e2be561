import { NextApiRequest, NextApiResponse } from 'next'
import { adminAuth } from '../../../../src/lib/firebase/admin'

export interface PerformanceData {
  responseTime: number[]
  timestamps: string[]
  cacheHitRate: number
  errorRate: number
  throughput: number
  coreWebVitals: {
    lcp: number // Largest Contentful Paint
    fid: number // First Input Delay
    cls: number // Cumulative Layout Shift
  }
  geographicData: {
    region: string
    responseTime: number
    requests: number
  }[]
  errorBreakdown: {
    type: string
    count: number
    percentage: number
  }[]
}

/**
 * Dashboard Performance Data API
 * Returns detailed performance metrics and analytics
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Verify authentication
    const authHeader = req.headers.authorization
    if (!authHeader?.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }

    const token = authHeader.split('Bearer ')[1]
    const decodedToken = await adminAuth.verifyIdToken(token)
    
    // Verify admin role
    if (!decodedToken.admin) {
      return res.status(403).json({ error: 'Admin access required' })
    }

    const timeRange = req.query.range as string || '1h'
    
    // Collect performance data
    const performanceData = await getPerformanceData(timeRange)

    // Cache response for 30 seconds
    res.setHeader('Cache-Control', 'public, s-maxage=30, stale-while-revalidate=60')
    
    return res.status(200).json(performanceData)

  } catch (error) {
    console.error('Dashboard performance API error:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * Get comprehensive performance data
 */
async function getPerformanceData(timeRange: string): Promise<PerformanceData> {
  try {
    // Generate time series data based on range
    const dataPoints = getDataPointsForRange(timeRange)
    const now = new Date()
    
    // Generate mock response time data with realistic patterns
    const responseTime: number[] = []
    const timestamps: string[] = []
    
    for (let i = 0; i < dataPoints; i++) {
      const timestamp = new Date(now.getTime() - (dataPoints - i - 1) * getIntervalForRange(timeRange))
      timestamps.push(timestamp.toISOString())
      
      // Generate realistic response time with some variance
      const baseResponseTime = 450 + Math.sin(i / 10) * 100 // Base pattern
      const noise = (Math.random() - 0.5) * 200 // Random variance
      responseTime.push(Math.max(100, baseResponseTime + noise))
    }

    // Mock Core Web Vitals data
    const coreWebVitals = {
      lcp: 1800 + Math.random() * 1000, // 1.8-2.8s
      fid: 50 + Math.random() * 100,    // 50-150ms
      cls: 0.05 + Math.random() * 0.1   // 0.05-0.15
    }

    // Mock geographic data
    const geographicData = [
      {
        region: 'North America',
        responseTime: 320 + Math.random() * 100,
        requests: Math.floor(Math.random() * 10000) + 50000
      },
      {
        region: 'Europe',
        responseTime: 280 + Math.random() * 80,
        requests: Math.floor(Math.random() * 8000) + 35000
      },
      {
        region: 'Asia Pacific',
        responseTime: 450 + Math.random() * 150,
        requests: Math.floor(Math.random() * 6000) + 25000
      },
      {
        region: 'South America',
        responseTime: 520 + Math.random() * 120,
        requests: Math.floor(Math.random() * 3000) + 15000
      },
      {
        region: 'Africa',
        responseTime: 680 + Math.random() * 200,
        requests: Math.floor(Math.random() * 2000) + 8000
      }
    ]

    // Mock error breakdown
    const totalErrors = Math.floor(Math.random() * 1000) + 500
    const errorBreakdown = [
      {
        type: '4xx Client Errors',
        count: Math.floor(totalErrors * 0.6),
        percentage: 60
      },
      {
        type: '5xx Server Errors',
        count: Math.floor(totalErrors * 0.25),
        percentage: 25
      },
      {
        type: 'Timeout Errors',
        count: Math.floor(totalErrors * 0.1),
        percentage: 10
      },
      {
        type: 'Network Errors',
        count: Math.floor(totalErrors * 0.05),
        percentage: 5
      }
    ]

    return {
      responseTime,
      timestamps,
      cacheHitRate: 75 + Math.random() * 20, // 75-95%
      errorRate: Math.random() * 3, // 0-3%
      throughput: Math.floor(Math.random() * 500) + 1000, // 1000-1500 req/min
      coreWebVitals,
      geographicData,
      errorBreakdown
    }

  } catch (error) {
    console.error('Failed to get performance data:', error)
    
    // Return empty data structure on error
    return {
      responseTime: [],
      timestamps: [],
      cacheHitRate: 0,
      errorRate: 0,
      throughput: 0,
      coreWebVitals: { lcp: 0, fid: 0, cls: 0 },
      geographicData: [],
      errorBreakdown: []
    }
  }
}

/**
 * Get number of data points based on time range
 */
function getDataPointsForRange(range: string): number {
  switch (range) {
    case '1h': return 60 // 1 point per minute
    case '6h': return 72 // 1 point per 5 minutes
    case '24h': return 96 // 1 point per 15 minutes
    case '7d': return 168 // 1 point per hour
    default: return 60
  }
}

/**
 * Get interval in milliseconds based on time range
 */
function getIntervalForRange(range: string): number {
  switch (range) {
    case '1h': return 60 * 1000 // 1 minute
    case '6h': return 5 * 60 * 1000 // 5 minutes
    case '24h': return 15 * 60 * 1000 // 15 minutes
    case '7d': return 60 * 60 * 1000 // 1 hour
    default: return 60 * 1000
  }
}
