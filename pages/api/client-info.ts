/**
 * Client Information API
 * 
 * Provides client information including IP address, user agent, and geolocation
 * for security monitoring and admin route protection.
 * 
 * Features:
 * - IP address detection with proxy support
 * - User agent parsing
 * - Geolocation information
 * - Security headers validation
 * - Rate limiting protection
 * 
 * <AUTHOR> Team
 */

import { NextApiRequest, NextApiResponse } from 'next'

interface ClientInfo {
  ip: string | null
  userAgent: string | null
  country: string | null
  region: string | null
  city: string | null
  timezone: string | null
  isp: string | null
  isProxy: boolean
  isTor: boolean
  isVpn: boolean
  riskScore: number
  timestamp: string
}

interface ApiResponse {
  success: boolean
  data?: ClientInfo
  error?: string
  timestamp: string
}

/**
 * Get client IP address with proxy detection
 */
function getClientIP(req: NextApiRequest): string | null {
  // Check various headers for real IP (in order of preference)
  const ipHeaders = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip', // Cloudflare
    'x-forwarded',
    'forwarded-for',
    'forwarded'
  ]

  for (const header of ipHeaders) {
    const value = req.headers[header]
    if (value) {
      // Handle comma-separated IPs (take the first one)
      const ip = Array.isArray(value) ? value[0] : value.split(',')[0]
      if (ip && ip.trim()) {
        return ip.trim()
      }
    }
  }

  // Fallback to connection remote address
  return req.socket.remoteAddress || null
}

/**
 * Parse user agent for security analysis
 */
function parseUserAgent(userAgent: string | undefined): {
  browser: string | null
  os: string | null
  device: string | null
  isBot: boolean
  isSuspicious: boolean
} {
  if (!userAgent) {
    return {
      browser: null,
      os: null,
      device: null,
      isBot: false,
      isSuspicious: true
    }
  }

  // Simple user agent parsing (in production, use a proper library)
  const isBot = /bot|crawler|spider|scraper/i.test(userAgent)
  const isSuspicious = userAgent.length < 10 || userAgent.length > 500

  let browser = null
  let os = null
  let device = null

  // Basic browser detection
  if (userAgent.includes('Chrome')) browser = 'Chrome'
  else if (userAgent.includes('Firefox')) browser = 'Firefox'
  else if (userAgent.includes('Safari')) browser = 'Safari'
  else if (userAgent.includes('Edge')) browser = 'Edge'

  // Basic OS detection
  if (userAgent.includes('Windows')) os = 'Windows'
  else if (userAgent.includes('Mac')) os = 'macOS'
  else if (userAgent.includes('Linux')) os = 'Linux'
  else if (userAgent.includes('Android')) os = 'Android'
  else if (userAgent.includes('iOS')) os = 'iOS'

  // Basic device detection
  if (userAgent.includes('Mobile')) device = 'Mobile'
  else if (userAgent.includes('Tablet')) device = 'Tablet'
  else device = 'Desktop'

  return {
    browser,
    os,
    device,
    isBot,
    isSuspicious
  }
}

/**
 * Calculate risk score based on various factors
 */
function calculateRiskScore(
  ip: string | null,
  userAgent: string | undefined,
  isProxy: boolean,
  isTor: boolean,
  isVpn: boolean
): number {
  let score = 0

  // IP-based risk factors
  if (!ip) score += 30
  if (isProxy) score += 20
  if (isTor) score += 40
  if (isVpn) score += 15

  // User agent risk factors
  const uaInfo = parseUserAgent(userAgent)
  if (uaInfo.isBot) score += 25
  if (uaInfo.isSuspicious) score += 20
  if (!uaInfo.browser) score += 15

  // Known suspicious patterns
  if (ip && (
    ip.startsWith('10.') || 
    ip.startsWith('192.168.') || 
    ip.startsWith('172.')
  )) {
    score += 10 // Private IP ranges
  }

  return Math.min(score, 100) // Cap at 100
}

/**
 * Mock geolocation lookup (in production, use a real service)
 */
async function getGeolocation(ip: string): Promise<{
  country: string | null
  region: string | null
  city: string | null
  timezone: string | null
  isp: string | null
  isProxy: boolean
  isTor: boolean
  isVpn: boolean
}> {
  // Mock data for development
  // In production, integrate with services like MaxMind, IPinfo, or similar
  return {
    country: 'Unknown',
    region: 'Unknown',
    city: 'Unknown',
    timezone: 'UTC',
    isp: 'Unknown ISP',
    isProxy: false,
    isTor: false,
    isVpn: false
  }
}

/**
 * Rate limiting check
 */
const requestCounts = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(ip: string): boolean {
  const now = Date.now()
  const windowMs = 60 * 1000 // 1 minute
  const maxRequests = 60 // 60 requests per minute

  const key = `client-info:${ip}`
  const current = requestCounts.get(key)

  if (!current || now > current.resetTime) {
    requestCounts.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (current.count >= maxRequests) {
    return false
  }

  current.count++
  return true
}

/**
 * Main API handler
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
      timestamp: new Date().toISOString()
    })
  }

  try {
    const ip = getClientIP(req)
    const userAgent = req.headers['user-agent']

    // Rate limiting
    if (ip && !checkRateLimit(ip)) {
      return res.status(429).json({
        success: false,
        error: 'Rate limit exceeded',
        timestamp: new Date().toISOString()
      })
    }

    // Get geolocation information
    const geoInfo = ip ? await getGeolocation(ip) : {
      country: null,
      region: null,
      city: null,
      timezone: null,
      isp: null,
      isProxy: false,
      isTor: false,
      isVpn: false
    }

    // Calculate risk score
    const riskScore = calculateRiskScore(
      ip,
      userAgent,
      geoInfo.isProxy,
      geoInfo.isTor,
      geoInfo.isVpn
    )

    const clientInfo: ClientInfo = {
      ip,
      userAgent,
      country: geoInfo.country,
      region: geoInfo.region,
      city: geoInfo.city,
      timezone: geoInfo.timezone,
      isp: geoInfo.isp,
      isProxy: geoInfo.isProxy,
      isTor: geoInfo.isTor,
      isVpn: geoInfo.isVpn,
      riskScore,
      timestamp: new Date().toISOString()
    }

    // Security headers
    res.setHeader('X-Content-Type-Options', 'nosniff')
    res.setHeader('X-Frame-Options', 'DENY')
    res.setHeader('X-XSS-Protection', '1; mode=block')

    return res.status(200).json({
      success: true,
      data: clientInfo,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Client info API error:', error)
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    })
  }
}
