
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Accessibility Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; }
        .header { background: #8b5cf6; color: white; padding: 20px; border-radius: 8px; }
        .summary { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0; }
        .metric { background: #f3f4f6; padding: 20px; border-radius: 8px; text-align: center; }
        .metric.success { background: #d1fae5; }
        .metric.error { background: #fee2e2; }
        .results { margin-top: 30px; }
        .test-result { margin: 20px 0; padding: 20px; border-left: 4px solid #8b5cf6; background: #f9fafb; }
        .test-result.success { border-color: #10b981; }
        .test-result.error { border-color: #ef4444; }
        pre { background: #1f2937; color: #f9fafb; padding: 15px; border-radius: 6px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Navigation Accessibility Report</h1>
        <p>Generated: 2025-07-28T01:25:19.195Z</p>
        <p>WCAG 2.1 AA Compliance</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <div style="font-size: 2em; font-weight: bold;">6</div>
        </div>
        <div class="metric success">
            <h3>Passed</h3>
            <div style="font-size: 2em; font-weight: bold;">1</div>
        </div>
        <div class="metric error">
            <h3>Failed</h3>
            <div style="font-size: 2em; font-weight: bold;">5</div>
        </div>
    </div>
    
    <div class="results">
        <h2>Test Results</h2>
        
            <div class="test-result error">
                <h3>❌ jestTests</h3>
                <p><strong>Status:</strong> FAILED</p>
                <p><strong>Error:</strong> Command failed: 
    npm test -- --testPathPattern="__tests__.*Accessibility" 
    --coverage --coverageDirectory=./accessibility-reports/coverage
    --testResultsProcessor=jest-sonar-reporter
  
testPathPattern:

  Option "testPathPattern" was replaced by "--testPathPatterns". "--testPathPatterns" is only available as a command-line option.
  
  Please update your configuration.

  CLI Options Documentation:
  https://jestjs.io/docs/cli

/bin/sh: line 2: --coverage: command not found
/bin/sh: line 3: --testResultsProcessor=jest-sonar-reporter: command not found
</p>
                <pre>
> inbox-zero-ai@0.1.0 test
> jest --testPathPattern=__tests__.*Accessibility

</pre>
            </div>
        
            <div class="test-result error">
                <h3>❌ axeAudit</h3>
                <p><strong>Status:</strong> FAILED</p>
                <p><strong>Error:</strong> Command failed: node temp-axe-audit.js
node:internal/modules/cjs/loader:1408
  throw err;
  ^

Error: Cannot find module '@axe-core/puppeteer'
Require stack:
- /Users/<USER>/Developer/syndicaps/temp-axe-audit.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Module._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (/Users/<USER>/Developer/syndicaps/temp-axe-audit.js:2:26)
    at Module._compile (node:internal/modules/cjs/loader:1734:14) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [ '/Users/<USER>/Developer/syndicaps/temp-axe-audit.js' ]
}

Node.js v24.1.0
</p>
                
            </div>
        
            <div class="test-result error">
                <h3>❌ lighthouseAudit</h3>
                <p><strong>Status:</strong> FAILED</p>
                <p><strong>Error:</strong> Command failed: 
    lighthouse http://localhost:3000/profile/account 
    --only-categories=accessibility 
    --output=json 
    --output-path=./accessibility-reports/lighthouse-accessibility.json
    --chrome-flags="--headless --no-sandbox"
  
/bin/sh: line 1: lighthouse: command not found
/bin/sh: line 2: --only-categories=accessibility: command not found
/bin/sh: line 3: --output=json: command not found
/bin/sh: line 4: --output-path=./accessibility-reports/lighthouse-accessibility.json: No such file or directory
/bin/sh: line 5: --chrome-flags=--headless --no-sandbox: command not found
</p>
                
            </div>
        
            <div class="test-result success">
                <h3>✅ pa11yAudit</h3>
                <p><strong>Status:</strong> PASSED</p>
                
                
            </div>
        
            <div class="test-result error">
                <h3>❌ keyboardNavigation</h3>
                <p><strong>Status:</strong> FAILED</p>
                <p><strong>Error:</strong> Command failed: node temp-keyboard-test.js
node:internal/modules/cjs/loader:1408
  throw err;
  ^

Error: Cannot find module 'puppeteer'
Require stack:
- /Users/<USER>/Developer/syndicaps/temp-keyboard-test.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Module._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (/Users/<USER>/Developer/syndicaps/temp-keyboard-test.js:2:19)
    at Module._compile (node:internal/modules/cjs/loader:1734:14) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [ '/Users/<USER>/Developer/syndicaps/temp-keyboard-test.js' ]
}

Node.js v24.1.0
</p>
                
            </div>
        
            <div class="test-result error">
                <h3>❌ colorContrast</h3>
                <p><strong>Status:</strong> FAILED</p>
                <p><strong>Error:</strong> Command failed: node temp-contrast-test.js
node:internal/modules/cjs/loader:1408
  throw err;
  ^

Error: Cannot find module 'puppeteer'
Require stack:
- /Users/<USER>/Developer/syndicaps/temp-contrast-test.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Module._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (/Users/<USER>/Developer/syndicaps/temp-contrast-test.js:2:19)
    at Module._compile (node:internal/modules/cjs/loader:1734:14) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [ '/Users/<USER>/Developer/syndicaps/temp-contrast-test.js' ]
}

Node.js v24.1.0
</p>
                
            </div>
        
    </div>
</body>
</html>
  