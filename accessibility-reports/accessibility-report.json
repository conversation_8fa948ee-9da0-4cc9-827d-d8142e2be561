{"timestamp": "2025-07-28T01:25:19.195Z", "wcagLevel": "AA", "wcagVersion": "2.1", "components": ["ConsolidatedNavigation", "DesktopNavigation", "MobileNavigation", "NavigationSearch", "ContextualSuggestionsPanel", "SkipLinks"], "results": {"jestTests": {"success": false, "error": "Command failed: \n    npm test -- --testPathPattern=\"__tests__.*Accessibility\" \n    --coverage --coverageDirectory=./accessibility-reports/coverage\n    --testResultsProcessor=jest-sonar-reporter\n  \ntestPathPattern:\n\n  Option \"testPathPattern\" was replaced by \"--testPathPatterns\". \"--testPathPatterns\" is only available as a command-line option.\n  \n  Please update your configuration.\n\n  CLI Options Documentation:\n  https://jestjs.io/docs/cli\n\n/bin/sh: line 2: --coverage: command not found\n/bin/sh: line 3: --testResultsProcessor=jest-sonar-reporter: command not found\n", "output": "\n> inbox-zero-ai@0.1.0 test\n> jest --testPathPattern=__tests__.*Accessibility\n\n"}, "axeAudit": {"success": false, "error": "Command failed: node temp-axe-audit.js\nnode:internal/modules/cjs/loader:1408\n  throw err;\n  ^\n\nError: Cannot find module '@axe-core/puppeteer'\nRequire stack:\n- /Users/<USER>/Developer/syndicaps/temp-axe-audit.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (/Users/<USER>/Developer/syndicaps/temp-axe-audit.js:2:26)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14) {\n  code: 'MODULE_NOT_FOUND',\n  requireStack: [ '/Users/<USER>/Developer/syndicaps/temp-axe-audit.js' ]\n}\n\nNode.js v24.1.0\n", "output": ""}, "lighthouseAudit": {"success": false, "error": "Command failed: \n    lighthouse http://localhost:3000/profile/account \n    --only-categories=accessibility \n    --output=json \n    --output-path=./accessibility-reports/lighthouse-accessibility.json\n    --chrome-flags=\"--headless --no-sandbox\"\n  \n/bin/sh: line 1: lighthouse: command not found\n/bin/sh: line 2: --only-categories=accessibility: command not found\n/bin/sh: line 3: --output=json: command not found\n/bin/sh: line 4: --output-path=./accessibility-reports/lighthouse-accessibility.json: No such file or directory\n/bin/sh: line 5: --chrome-flags=--headless --no-sandbox: command not found\n", "output": ""}, "pa11yAudit": {"success": true, "output": ""}, "keyboardNavigation": {"success": false, "error": "Command failed: node temp-keyboard-test.js\nnode:internal/modules/cjs/loader:1408\n  throw err;\n  ^\n\nError: Cannot find module 'puppeteer'\nRequire stack:\n- /Users/<USER>/Developer/syndicaps/temp-keyboard-test.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (/Users/<USER>/Developer/syndicaps/temp-keyboard-test.js:2:19)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14) {\n  code: 'MODULE_NOT_FOUND',\n  requireStack: [ '/Users/<USER>/Developer/syndicaps/temp-keyboard-test.js' ]\n}\n\nNode.js v24.1.0\n", "output": ""}, "colorContrast": {"success": false, "error": "Command failed: node temp-contrast-test.js\nnode:internal/modules/cjs/loader:1408\n  throw err;\n  ^\n\nError: Cannot find module 'puppeteer'\nRequire stack:\n- /Users/<USER>/Developer/syndicaps/temp-contrast-test.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (/Users/<USER>/Developer/syndicaps/temp-contrast-test.js:2:19)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14) {\n  code: 'MODULE_NOT_FOUND',\n  requireStack: [ '/Users/<USER>/Developer/syndicaps/temp-contrast-test.js' ]\n}\n\nNode.js v24.1.0\n", "output": ""}}, "summary": {"totalTests": 6, "passed": 1, "failed": 5}}