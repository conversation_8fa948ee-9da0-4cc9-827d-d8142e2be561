/**
 * Individual Challenge Detail Page
 *
 * Displays detailed information about a specific challenge including
 * description, rules, participants, submissions, and participation controls.
 *
 * Features:
 * - Enhanced parameter validation and security
 * - Complete challenge information
 * - Participation management
 * - Submission viewing
 * - Real-time countdown
 * - Social sharing
 * - Mobile-responsive design
 * - Comprehensive error handling
 *
 * <AUTHOR> Team
 */

import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import ChallengeDetailClientComponent from './ChallengeDetailClientComponent'
import { validateChallengeId, logRouteValidationError } from '@/lib/utils/routeValidation'

interface ChallengeDetailPageProps {
  params: Promise<{
    id: string
  }>
}

/**
 * Generate metadata for the challenge detail page
 */
export async function generateMetadata({ params }: ChallengeDetailPageProps) {
  const resolvedParams = await params
  // In a real implementation, you would fetch the challenge data here
  // For now, we'll use a generic metadata structure
  return {
    title: `Challenge Details - Syndicaps`,
    description: 'Join this exciting community challenge and showcase your creativity with fellow keyboard enthusiasts.',
    keywords: 'community challenge, keyboard contest, artisan keycap competition, creative challenge',
    openGraph: {
      title: `Challenge Details - Syndicaps`,
      description: 'Join this exciting community challenge and showcase your creativity.',
      type: 'website',
    },
  }
}

/**
 * Loading component for challenge detail page
 */
const ChallengeDetailLoading = () => (
  <div className="min-h-screen bg-gray-950 pt-24 pb-20">
    <div className="container mx-auto px-4 max-w-7xl">
      {/* Hero Section Skeleton */}
      <div className="mb-8">
        <div className="relative h-64 md:h-80 rounded-lg overflow-hidden bg-gray-800 animate-pulse">
          <div className="absolute bottom-6 left-6 right-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="h-6 bg-gray-700 rounded w-20 animate-pulse" />
                  <div className="h-6 bg-gray-700 rounded w-24 animate-pulse" />
                </div>
                <div className="h-8 bg-gray-700 rounded w-96 mb-2 animate-pulse" />
                <div className="h-4 bg-gray-700 rounded w-64 animate-pulse" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-gray-800 rounded-lg p-6 animate-pulse">
            <div className="h-6 bg-gray-700 rounded w-48 mb-4" />
            <div className="space-y-2">
              <div className="h-4 bg-gray-700 rounded" />
              <div className="h-4 bg-gray-700 rounded" />
              <div className="h-4 bg-gray-700 rounded w-3/4" />
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-6 animate-pulse">
            <div className="h-6 bg-gray-700 rounded w-40 mb-4" />
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-gray-700 rounded-full" />
                  <div className="h-4 bg-gray-700 rounded flex-1" />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          <div className="bg-gray-800 rounded-lg p-6 animate-pulse">
            <div className="h-6 bg-gray-700 rounded w-32 mb-4" />
            <div className="flex items-center space-x-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="text-center">
                  <div className="h-8 bg-gray-700 rounded w-12 mb-1" />
                  <div className="h-3 bg-gray-700 rounded w-8" />
                </div>
              ))}
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-6 animate-pulse">
            <div className="h-6 bg-gray-700 rounded w-28 mb-4" />
            <div className="h-12 bg-gray-700 rounded mb-4" />
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex justify-between">
                  <div className="h-4 bg-gray-700 rounded w-20" />
                  <div className="h-4 bg-gray-700 rounded w-12" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

/**
 * Challenge detail page component
 *
 * @param params - Route parameters containing challenge ID
 * @returns JSX.Element - Rendered challenge detail page
 */
export default async function ChallengeDetailPage({ params }: ChallengeDetailPageProps) {
  const resolvedParams = await params

  // Enhanced parameter validation
  const validationResult = validateChallengeId(resolvedParams.id)

  if (!validationResult.isValid) {
    // Log validation error for monitoring
    logRouteValidationError(validationResult.error!, {
      route: '/community/challenges/[id]',
      value: resolvedParams.id,
      timestamp: new Date()
    })

    // Return 404 for invalid challenge IDs
    notFound()
  }

  return (
    <Suspense fallback={<ChallengeDetailLoading />}>
      <ChallengeDetailClientComponent challengeId={validationResult.sanitizedValue!} />
    </Suspense>
  )
}
