/**
 * Shop Product Detail Page Component
 *
 * Individual product detail page accessible via /shop/[id] route.
 * Displays comprehensive product information, images, pricing, and purchase options.
 * Handles both regular products and raffle entries with appropriate UI states.
 *
 * Features:
 * - Enhanced parameter validation and security
 * - Product image gallery with zoom functionality
 * - Detailed product specifications and descriptions
 * - Add to cart functionality for regular products
 * - Raffle entry interface for raffle products
 * - Related products suggestions
 * - Social sharing capabilities
 * - SEO optimization for individual products
 * - Responsive design for all devices
 * - Comprehensive error handling
 *
 * Route: /shop/[id]
 * Previous Route: /products/[id] (redirected)
 *
 * @param params - Dynamic route parameters
 * @param params.id - Product ID from the URL
 */

import { notFound } from 'next/navigation'
import ProductDetailComponent from '@/components/shop/ProductDetailComponent'
import { validateProductId, logRouteValidationError } from '@/lib/utils/routeValidation'

/**
 * Props interface for the shop product detail page
 */
interface ShopProductDetailPageProps {
  /** Dynamic route parameters */
  params: Promise<{
    /** Product ID from the URL path */
    id: string;
  }>;
}

/**
 * Shop product detail page component
 * Renders individual product information and purchase interface
 *
 * @param props - Component props
 * @param props.params - Route parameters containing product ID
 * @returns JSX.Element - Rendered product detail page
 */
export default async function ShopProductDetailPage({ params }: ShopProductDetailPageProps) {
  const resolvedParams = await params

  // Enhanced parameter validation
  const validationResult = validateProductId(resolvedParams.id)

  if (!validationResult.isValid) {
    // Log validation error for monitoring
    logRouteValidationError(validationResult.error!, {
      route: '/shop/[id]',
      value: resolvedParams.id,
      timestamp: new Date()
    })

    // Return 404 for invalid product IDs
    notFound()
  }

  // Pass validated and sanitized parameters
  const validatedParams = Promise.resolve({
    id: validationResult.sanitizedValue!
  })

  return <ProductDetailComponent params={validatedParams} />
}

/**
 * Generates metadata for individual product pages
 * Improves SEO and social media sharing for specific products
 * 
 * @param props - Component props with route parameters
 * @returns Metadata object for the product page
 */
export async function generateMetadata({ params }: ShopProductDetailPageProps) {
  // In a real implementation, you would fetch product data here
  // const product = await getProduct(params.id)
  
  return {
    title: `Product Details - Syndicaps Shop`,
    description: `View detailed information about this premium artisan keycap including specifications, pricing, and availability.`,
    keywords: 'artisan keycap, product details, mechanical keyboard, custom keycap',
    openGraph: {
      title: `Product - Syndicaps Shop`,
      description: `Premium artisan keycap details and specifications`,
      type: 'website',
    },
  }
}
