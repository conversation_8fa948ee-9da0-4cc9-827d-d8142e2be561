/**
 * Public Blog Post View Page
 *
 * Displays individual blog posts to the public with view tracking,
 * social sharing, and related content suggestions.
 * Uses semantic HTML5 elements for improved accessibility and SEO.
 * Enhanced with comprehensive parameter validation and error handling.
 *
 * @component
 * @returns {JSX.Element} The individual blog post page with semantic HTML structure
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useParams, notFound } from 'next/navigation'
import { motion } from 'framer-motion'
import { Calendar, User, Eye, Heart, Share2, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import Head from 'next/head'
import { getBlogPost, incrementBlogPostViews, BlogPost } from '@/lib/firestore'
import RelatedPosts from '@/components/blog/RelatedPosts'
import Comments from '@/components/blog/Comments'
import { validateBlogSlug, logRouteValidationError } from '@/lib/utils/routeValidation'

export default function BlogPostPage() {
  const params = useParams()

  // Enhanced parameter validation
  if (!params?.slug) {
    return <div className="text-white">Loading...</div>
  }

  const rawSlug = params.slug as string

  // Validate blog slug format
  const validationResult = validateBlogSlug(rawSlug)

  if (!validationResult.isValid) {
    // Log validation error for monitoring
    logRouteValidationError(validationResult.error!, {
      route: '/blog/[slug]',
      value: rawSlug,
      timestamp: new Date()
    })

    // Return 404 for invalid slugs
    notFound()
  }

  const slug = validationResult.sanitizedValue!

  const [post, setPost] = useState<BlogPost | null>(null)
  const [loading, setLoading] = useState(true)
  const [liked, setLiked] = useState(false)

  useEffect(() => {
    const loadPost = async () => {
      if (!slug) return

      try {
        setLoading(true)
        const postData = await getBlogPost(slug)
        
        if (!postData) {
          notFound()
          return
        }

        // Only show published posts to public
        if (!postData.published) {
          notFound()
          return
        }

        setPost(postData)
        
        // Increment view count
        await incrementBlogPostViews(postData.id)
      } catch (error) {
        console.error('Error loading blog post:', error)
        notFound()
      } finally {
        setLoading(false)
      }
    }

    loadPost()
  }, [slug])

  const handleShare = async () => {
    if (navigator.share && post) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: window.location.href,
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert('Link copied to clipboard!')
    }
  }

  const handleLike = () => {
    setLiked(!liked)
    // TODO: Implement like functionality with Firestore
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white">Loading blog post...</div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white">Blog post not found</div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>{post.seoTitle || post.title} | Syndicaps Blog</title>
        <meta name="description" content={post.seoDescription || post.excerpt} />
        <meta name="keywords" content={post.seoKeywords?.join(', ') || post.tags.join(', ')} />
        <meta name="author" content={post.author} />
        <meta name="robots" content="index, follow" />

        {/* Open Graph tags */}
        <meta property="og:title" content={post.seoTitle || post.title} />
        <meta property="og:description" content={post.seoDescription || post.excerpt} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={`${process.env.NEXT_PUBLIC_SITE_URL}/blog/${post.slug}`} />
        {post.featuredImage && <meta property="og:image" content={post.featuredImage} />}
        <meta property="og:site_name" content="Syndicaps" />

        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={post.seoTitle || post.title} />
        <meta name="twitter:description" content={post.seoDescription || post.excerpt} />
        {post.featuredImage && <meta name="twitter:image" content={post.featuredImage} />}

        {/* Article specific tags */}
        <meta property="article:published_time" content={post.createdAt?.toDate().toISOString()} />
        <meta property="article:modified_time" content={post.updatedAt?.toDate().toISOString()} />
        <meta property="article:author" content={post.author} />
        <meta property="article:section" content={post.category} />
        {post.tags.map((tag, index) => (
          <meta key={index} property="article:tag" content={tag} />
        ))}

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BlogPosting",
              "headline": post.title,
              "description": post.excerpt,
              "image": post.featuredImage,
              "author": {
                "@type": "Person",
                "name": post.author
              },
              "publisher": {
                "@type": "Organization",
                "name": "Syndicaps",
                "logo": {
                  "@type": "ImageObject",
                  "url": `${process.env.NEXT_PUBLIC_SITE_URL}/logo.png`
                }
              },
              "datePublished": post.createdAt?.toDate().toISOString(),
              "dateModified": post.updatedAt?.toDate().toISOString(),
              "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": `${process.env.NEXT_PUBLIC_SITE_URL}/blog/${post.slug}`
              },
              "keywords": post.tags.join(', '),
              "articleSection": post.category
            })
          }}
        />
      </Head>

      <div className="min-h-screen bg-gray-900">
      {/* Navigation */}
      <nav className="bg-gray-800 border-b border-gray-700" aria-label="Blog navigation">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <Link
            href="/blog"
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors"
            aria-label="Return to blog listing"
          >
            <ArrowLeft size={20} className="mr-2" aria-hidden="true" />
            Back to Blog
          </Link>
        </div>
      </nav>

      {/* Main Article Content */}
      <main>
        <article className="max-w-4xl mx-auto px-4 py-8" itemScope itemType="https://schema.org/BlogPosting">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {/* Featured Image */}
            {post.featuredImage && (
              <figure className="mb-8">
                <img
                  src={post.featuredImage}
                  alt={`Featured image for ${post.title}`}
                  className="w-full h-64 md:h-96 object-cover rounded-lg"
                  itemProp="image"
                />
              </figure>
            )}

            {/* Article Header */}
            <header className="mb-8">
              <div className="flex items-center space-x-4 text-sm text-gray-400 mb-4">
                <span className="px-3 py-1 bg-accent-600 text-white rounded-full text-xs uppercase font-medium" itemProp="articleSection">
                  {post.category.replace('-', ' ')}
                </span>
                {post.featured && (
                  <span className="px-3 py-1 bg-yellow-600 text-white rounded-full text-xs uppercase font-medium">
                    Featured
                  </span>
                )}
              </div>

              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight" itemProp="headline">
                {post.title}
              </h1>

              <p className="text-xl text-gray-300 mb-6 leading-relaxed" itemProp="description">
                {post.excerpt}
              </p>

              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-400">
                <div className="flex items-center">
                  <User size={16} className="mr-2" aria-hidden="true" />
                  <span>
                    <span className="sr-only">Author:</span>
                    <span itemProp="author" itemScope itemType="https://schema.org/Person">
                      <span itemProp="name">By {post.author}</span>
                    </span>
                  </span>
                </div>
                <time
                  className="flex items-center"
                  dateTime={post.createdAt?.toDate().toISOString()}
                  itemProp="datePublished"
                >
                  <Calendar size={16} className="mr-2" aria-hidden="true" />
                  <span className="sr-only">Published on:</span>
                  {post.createdAt?.toDate().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </time>
                <div className="flex items-center">
                  <Eye size={16} className="mr-2" aria-hidden="true" />
                  <span className="sr-only">Views:</span>
                  <span>{post.views} views</span>
                </div>
              </div>
            </header>

            {/* Article Content */}
            <div
              className="prose prose-invert prose-lg max-w-none mb-8"
              itemProp="articleBody"
              dangerouslySetInnerHTML={{ __html: post.content }}
            />

            {/* Tags Section */}
            {post.tags && post.tags.length > 0 && (
              <aside className="mb-8" aria-labelledby="tags-heading">
                <h3 id="tags-heading" className="text-lg font-semibold text-white mb-3">Tags</h3>
                <div className="flex flex-wrap gap-2" role="list">
                  {post.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded-full hover:bg-gray-600 transition-colors cursor-pointer"
                      role="listitem"
                      itemProp="keywords"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </aside>
            )}

            {/* Article Actions */}
            <footer className="flex items-center justify-between py-6 border-t border-gray-700">
              <div className="flex items-center space-x-4" role="group" aria-label="Article actions">
                <button
                  onClick={handleLike}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                    liked
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                  aria-label={`${liked ? 'Unlike' : 'Like'} this post. Current likes: ${post.likes + (liked ? 1 : 0)}`}
                >
                  <Heart size={20} className={liked ? 'fill-current' : ''} aria-hidden="true" />
                  <span>{post.likes + (liked ? 1 : 0)}</span>
                </button>

                <button
                  onClick={handleShare}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
                  aria-label="Share this post"
                >
                  <Share2 size={20} aria-hidden="true" />
                  <span>Share</span>
                </button>
              </div>

              <div className="text-sm text-gray-400">
                <time dateTime={post.updatedAt?.toDate().toISOString()} itemProp="dateModified">
                  <span className="sr-only">Last updated:</span>
                  Last updated: {post.updatedAt?.toDate().toLocaleDateString()}
                </time>
              </div>
            </footer>
          </motion.div>
        </article>
      </main>

      {/* Comments Section */}
      <div className="max-w-4xl mx-auto px-4 py-8 border-t border-gray-700">
        <Comments postId={post.id} allowComments={true} />
      </div>

      {/* Related Posts Section */}
      <aside className="max-w-4xl mx-auto px-4 py-8 border-t border-gray-700" aria-labelledby="related-posts-heading">
        <header className="mb-6">
          <h2 id="related-posts-heading" className="text-2xl font-bold text-white">Related Posts</h2>
        </header>
        <RelatedPosts
          currentPostId={post.id}
          category={post.category}
          tags={post.tags}
          limit={3}
        />
      </aside>
      </div>
    </>
  )
}
