'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { PenTool, Eye, Save, ArrowLeft, Image } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { createBlogPost } from '@/lib/firestore'
import BackButton from '@/admin/components/common/BackButton'
import RichTextEditor from '@/components/admin/RichTextEditor'
import { Timestamp } from 'firebase/firestore'

export default function CreateBlogPostPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [preview, setPreview] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    featuredImage: '',
    category: 'news',
    tags: '',
    published: false,
    featured: false,
    seoTitle: '',
    seoDescription: '',
    seoKeywords: '',
    scheduledAt: '',
    publishingOption: 'draft' // 'draft', 'publish', 'schedule'
  })

  const categories = [
    { value: 'news', label: 'News & Updates' },
    { value: 'tutorials', label: 'Tutorials' },
    { value: 'reviews', label: 'Product Reviews' },
    { value: 'community', label: 'Community' },
    { value: 'behind-scenes', label: 'Behind the Scenes' }
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    if (name === 'title') {
      // Auto-generate slug from title
      const slug = value.toLowerCase()
        .replace(/[^a-z0-9 -]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
      
      setFormData(prev => ({
        ...prev,
        title: value,
        slug: slug
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const isScheduled = formData.publishingOption === 'schedule' && formData.scheduledAt
      const isPublished = formData.publishingOption === 'publish' || (isScheduled && new Date(formData.scheduledAt) <= new Date())

      const blogPostData = {
        title: formData.title,
        slug: formData.slug,
        excerpt: formData.excerpt,
        content: formData.content,
        featuredImage: formData.featuredImage || undefined,
        category: formData.category,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        published: Boolean(isPublished),
        featured: formData.featured,
        author: 'Admin', // You'd get this from the current user context
        seoTitle: formData.seoTitle || formData.title,
        seoDescription: formData.seoDescription || formData.excerpt,
        seoKeywords: formData.seoKeywords.split(',').map(keyword => keyword.trim()).filter(keyword => keyword),
        ...(isScheduled && { scheduledAt: Timestamp.fromDate(new Date(formData.scheduledAt)) })
      }

      await createBlogPost(blogPostData)

      router.push('/admin/dashboard?success=Blog post created successfully')
    } catch (error) {
      // TODO: Implement proper error logging and user notification
      alert('Failed to create blog post. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6" data-testid="blog-create-page">
      <div className="mb-8">
        <BackButton fallbackUrl="/admin/blog" />
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Create Blog Post</h1>
            <p className="text-gray-400">
              Write and publish a new blog post for your community.
            </p>
          </div>
          <button
            type="button"
            onClick={() => setPreview(!preview)}
            className="flex items-center px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            <Eye size={20} className="mr-2" />
            {preview ? 'Edit' : 'Preview'}
          </button>
        </div>
      </div>

      {!preview ? (
        <div className="bg-gray-800 rounded-lg p-6">
          <form onSubmit={handleSubmit} className="space-y-6" data-testid="blog-create-form">
            {/* Title and Slug */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-300 mb-2">
                  Title *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  required
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                  placeholder="Enter blog post title"
                  data-testid="title-input"
                />
              </div>

              <div>
                <label htmlFor="slug" className="block text-sm font-medium text-gray-300 mb-2">
                  URL Slug *
                </label>
                <input
                  type="text"
                  id="slug"
                  name="slug"
                  required
                  value={formData.slug}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                  placeholder="url-friendly-slug"
                  data-testid="slug-input"
                />
              </div>
            </div>

            {/* Excerpt */}
            <div>
              <label htmlFor="excerpt" className="block text-sm font-medium text-gray-300 mb-2">
                Excerpt *
              </label>
              <textarea
                id="excerpt"
                name="excerpt"
                required
                rows={3}
                value={formData.excerpt}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                placeholder="Brief description of the blog post"
                data-testid="excerpt-input"
              />
            </div>

            {/* Featured Image */}
            <div>
              <label htmlFor="featuredImage" className="block text-sm font-medium text-gray-300 mb-2">
                Featured Image URL
              </label>
              <input
                type="url"
                id="featuredImage"
                name="featuredImage"
                value={formData.featuredImage}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                placeholder="https://example.com/image.jpg"
              />
            </div>

            {/* Category and Tags */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-300 mb-2">
                  Category *
                </label>
                <select
                  id="category"
                  name="category"
                  required
                  value={formData.category}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                  data-testid="category-select"
                >
                  {categories.map(cat => (
                    <option key={cat.value} value={cat.value}>
                      {cat.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="tags" className="block text-sm font-medium text-gray-300 mb-2">
                  Tags (comma-separated)
                </label>
                <input
                  type="text"
                  id="tags"
                  name="tags"
                  value={formData.tags}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                  placeholder="keycaps, artisan, community"
                  data-testid="tags-input"
                />
              </div>
            </div>

            {/* Content */}
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-300 mb-2">
                Content *
              </label>
              <RichTextEditor
                value={formData.content}
                onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                placeholder="Write your blog post content here..."
                className="mb-2"
              />
              <p className="text-gray-500 text-xs mt-1">
                Use the rich text editor to format your content with headings, lists, links, and more.
              </p>
            </div>

            {/* Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="published"
                  name="published"
                  checked={formData.published}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-accent-600 focus:ring-accent-500 border-gray-600 rounded bg-gray-700"
                />
                <label htmlFor="published" className="ml-2 block text-sm text-gray-300">
                  Publish immediately
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featured"
                  name="featured"
                  checked={formData.featured}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-accent-600 focus:ring-accent-500 border-gray-600 rounded bg-gray-700"
                />
                <label htmlFor="featured" className="ml-2 block text-sm text-gray-300">
                  Featured post
                </label>
              </div>
            </div>

            {/* SEO Settings */}
            <div className="border-t border-gray-700 pt-6">
              <h3 className="text-lg font-semibold text-white mb-4">SEO Settings</h3>

              <div className="space-y-4">
                <div>
                  <label htmlFor="seoTitle" className="block text-sm font-medium text-gray-300 mb-2">
                    SEO Title
                  </label>
                  <input
                    type="text"
                    id="seoTitle"
                    name="seoTitle"
                    value={formData.seoTitle}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                    placeholder="Leave empty to use post title"
                    maxLength={60}
                    data-testid="seo-title-input"
                  />
                  <p className="text-gray-500 text-xs mt-1">
                    Recommended: 50-60 characters. Current: {formData.seoTitle.length}/60
                  </p>
                </div>

                <div>
                  <label htmlFor="seoDescription" className="block text-sm font-medium text-gray-300 mb-2">
                    SEO Description
                  </label>
                  <textarea
                    id="seoDescription"
                    name="seoDescription"
                    rows={3}
                    value={formData.seoDescription}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                    placeholder="Leave empty to use post excerpt"
                    maxLength={160}
                    data-testid="seo-description-input"
                  />
                  <p className="text-gray-500 text-xs mt-1">
                    Recommended: 150-160 characters. Current: {formData.seoDescription.length}/160
                  </p>
                </div>

                <div>
                  <label htmlFor="seoKeywords" className="block text-sm font-medium text-gray-300 mb-2">
                    SEO Keywords
                  </label>
                  <input
                    type="text"
                    id="seoKeywords"
                    name="seoKeywords"
                    value={formData.seoKeywords}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                    placeholder="keyword1, keyword2, keyword3"
                    data-testid="seo-keywords-input"
                  />
                  <p className="text-gray-500 text-xs mt-1">
                    Separate keywords with commas. Recommended: 3-5 keywords.
                  </p>
                </div>
              </div>
            </div>

            {/* Publishing Options */}
            <div className="border-t border-gray-700 pt-6">
              <h3 className="text-lg font-semibold text-white mb-4">Publishing Options</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">Publishing Status</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="publishingOption"
                        value="draft"
                        checked={formData.publishingOption === 'draft'}
                        onChange={handleInputChange}
                        className="mr-3 text-accent-600 focus:ring-accent-500 border-gray-600 bg-gray-700"
                        data-testid="draft-radio"
                      />
                      <span className="text-gray-300">Save as Draft</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="publishingOption"
                        value="publish"
                        checked={formData.publishingOption === 'publish'}
                        onChange={handleInputChange}
                        className="mr-3 text-accent-600 focus:ring-accent-500 border-gray-600 bg-gray-700"
                        data-testid="publish-radio"
                      />
                      <span className="text-gray-300">Publish Immediately</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="publishingOption"
                        value="schedule"
                        checked={formData.publishingOption === 'schedule'}
                        onChange={handleInputChange}
                        className="mr-3 text-accent-600 focus:ring-accent-500 border-gray-600 bg-gray-700"
                        data-testid="schedule-radio"
                      />
                      <span className="text-gray-300">Schedule for Later</span>
                    </label>
                  </div>
                </div>

                {formData.publishingOption === 'schedule' && (
                  <div>
                    <label htmlFor="scheduledAt" className="block text-sm font-medium text-gray-300 mb-2">
                      Scheduled Date & Time
                    </label>
                    <input
                      type="datetime-local"
                      id="scheduledAt"
                      name="scheduledAt"
                      value={formData.scheduledAt}
                      onChange={handleInputChange}
                      min={new Date().toISOString().slice(0, 16)}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
                      required={formData.publishingOption === 'schedule'}
                      data-testid="scheduled-date-input"
                    />
                    <p className="text-gray-500 text-xs mt-1">
                      Select when this post should be automatically published.
                    </p>
                  </div>
                )}

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="featured"
                    name="featured"
                    checked={formData.featured}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-accent-600 focus:ring-accent-500 border-gray-600 rounded bg-gray-700"
                    data-testid="featured-checkbox"
                  />
                  <label htmlFor="featured" className="ml-2 block text-sm text-gray-300">
                    Mark as featured post
                  </label>
                </div>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4">
              <Link
                href="/admin/dashboard"
                className="px-6 py-2 border border-gray-600 text-gray-300 rounded-md hover:bg-gray-700 transition-colors"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={loading}
                className={`px-6 py-2 rounded-md font-medium transition-colors flex items-center ${
                  loading
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-accent-600 hover:bg-accent-700 text-white'
                }`}
                data-testid="submit-button"
              >
                <Save size={20} className="mr-2" />
                {loading ? 'Saving...' :
                  formData.publishingOption === 'publish' ? 'Publish Post' :
                  formData.publishingOption === 'schedule' ? 'Schedule Post' :
                  'Save Draft'}
              </button>
            </div>
          </form>
        </div>
      ) : (
        /* Preview Mode */
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="prose prose-invert max-w-none">
            {formData.featuredImage && (
              <img
                src={formData.featuredImage}
                alt={formData.title}
                className="w-full h-64 object-cover rounded-lg mb-6"
              />
            )}
            <h1 className="text-3xl font-bold text-white mb-4">{formData.title || 'Blog Post Title'}</h1>
            <p className="text-gray-400 mb-6">{formData.excerpt || 'Blog post excerpt will appear here...'}</p>
            <div
              className="text-gray-300 prose prose-invert max-w-none"
              dangerouslySetInnerHTML={{
                __html: formData.content || '<p>Blog post content will appear here...</p>'
              }}
            />
            {formData.tags && (
              <div className="mt-6 flex flex-wrap gap-2">
                {formData.tags.split(',').map((tag, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-accent-600 text-white text-sm rounded-full"
                  >
                    {tag.trim()}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
