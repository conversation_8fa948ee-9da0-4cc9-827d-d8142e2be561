/**
 * Edit Product Page
 * 
 * Admin page for editing existing products with full CRUD functionality
 * 
 * <AUTHOR> Team
 */

'use client'

import { useParams, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { doc, getDoc, updateDoc, deleteDoc } from 'firebase/firestore'
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage'
import { db, storage } from '../../../../../src/lib/firebase'
import { toast } from 'react-hot-toast'
import { 
  ArrowLeft, 
  Save, 
  Trash2, 
  Upload, 
  X, 
  Plus,
  Minus,
  AlertTriangle
} from 'lucide-react'
import { validateProductId, logRouteValidationError } from '@/lib/utils/routeValidation'
import { notFound } from 'next/navigation'

interface Product {
  id: string
  name: string
  description: string
  price: number
  category: string
  images: string[]
  inStock: boolean
  stockQuantity: number
  featured: boolean
  specifications: { [key: string]: string }
  tags: string[]
  createdAt: any
  updatedAt: any
}

/**
 * Edit Product Page Component
 */
export default function EditProductPage() {
  const params = useParams<{ id: string }>()
  const router = useRouter()

  // Enhanced parameter validation
  const rawProductId = params?.id as string

  // Validate product ID format
  const validationResult = validateProductId(rawProductId)

  if (!validationResult.isValid) {
    // Log validation error for monitoring
    logRouteValidationError(validationResult.error!, {
      route: '/admin/products/[id]/edit',
      value: rawProductId,
      timestamp: new Date()
    })

    // Return 404 for invalid product IDs
    notFound()
  }

  const productId = validationResult.sanitizedValue!

  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [uploadingImages, setUploadingImages] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: 0,
    category: '',
    inStock: true,
    stockQuantity: 0,
    featured: false,
    specifications: {} as { [key: string]: string },
    tags: [] as string[]
  })
  const [images, setImages] = useState<string[]>([])
  const [newImages, setNewImages] = useState<File[]>([])
  const [newSpecKey, setNewSpecKey] = useState('')
  const [newSpecValue, setNewSpecValue] = useState('')
  const [newTag, setNewTag] = useState('')

  const categories = [
    'Artisan Keycaps',
    'Mechanical Keyboards',
    'Switches',
    'Accessories',
    'Limited Edition',
    'Raffle Items',
    'Giveaway'
  ]

  /**
   * Load product data
   */
  useEffect(() => {
    const loadProduct = async () => {
      try {
        const productDoc = await getDoc(doc(db, 'products', productId))
        if (productDoc.exists()) {
          const productData = { id: productDoc.id, ...productDoc.data() } as Product
          setProduct(productData)
          setFormData({
            name: productData.name,
            description: productData.description,
            price: productData.price,
            category: productData.category,
            inStock: productData.inStock,
            stockQuantity: productData.stockQuantity,
            featured: productData.featured,
            specifications: productData.specifications || {},
            tags: productData.tags || []
          })
          setImages(productData.images || [])
        } else {
          toast.error('Product not found')
          router.push('/admin/products')
        }
      } catch (error) {
        console.error('Error loading product:', error)
        toast.error('Failed to load product')
      } finally {
        setLoading(false)
      }
    }

    if (productId) {
      loadProduct()
    }
  }, [productId, router])

  /**
   * Handle form input changes
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
              type === 'number' ? parseFloat(value) || 0 : value
    }))
  }

  /**
   * Handle image upload
   */
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setNewImages(prev => [...prev, ...files])
  }

  /**
   * Upload images to Firebase Storage
   */
  const uploadImages = async (files: File[]): Promise<string[]> => {
    const uploadPromises = files.map(async (file) => {
      const fileName = `products/${productId}/${Date.now()}_${file.name}`
      const storageRef = ref(storage, fileName)
      await uploadBytes(storageRef, file)
      return getDownloadURL(storageRef)
    })
    return Promise.all(uploadPromises)
  }

  /**
   * Remove image
   */
  const removeImage = async (imageUrl: string, index: number) => {
    try {
      // Remove from Firebase Storage if it's an existing image
      if (imageUrl.includes('firebase')) {
        const imageRef = ref(storage, imageUrl)
        await deleteObject(imageRef)
      }
      
      setImages(prev => prev.filter((_, i) => i !== index))
      toast.success('Image removed')
    } catch (error) {
      console.error('Error removing image:', error)
      toast.error('Failed to remove image')
    }
  }

  /**
   * Add specification
   */
  const addSpecification = () => {
    if (newSpecKey && newSpecValue) {
      setFormData(prev => ({
        ...prev,
        specifications: {
          ...prev.specifications,
          [newSpecKey]: newSpecValue
        }
      }))
      setNewSpecKey('')
      setNewSpecValue('')
    }
  }

  /**
   * Remove specification
   */
  const removeSpecification = (key: string) => {
    setFormData(prev => ({
      ...prev,
      specifications: Object.fromEntries(
        Object.entries(prev.specifications).filter(([k]) => k !== key)
      )
    }))
  }

  /**
   * Add tag
   */
  const addTag = () => {
    if (newTag && !formData.tags.includes(newTag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag]
      }))
      setNewTag('')
    }
  }

  /**
   * Remove tag
   */
  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }))
  }

  /**
   * Save product
   */
  const handleSave = async () => {
    try {
      setSaving(true)

      // Upload new images
      let uploadedImageUrls: string[] = []
      if (newImages.length > 0) {
        setUploadingImages(true)
        uploadedImageUrls = await uploadImages(newImages)
        setUploadingImages(false)
      }

      // Combine existing and new images
      const allImages = [...images, ...uploadedImageUrls]

      // Update product in Firestore
      await updateDoc(doc(db, 'products', productId), {
        ...formData,
        images: allImages,
        updatedAt: new Date()
      })

      setImages(allImages)
      setNewImages([])
      toast.success('Product updated successfully!')
      
    } catch (error) {
      console.error('Error updating product:', error)
      toast.error('Failed to update product')
    } finally {
      setSaving(false)
      setUploadingImages(false)
    }
  }

  /**
   * Delete product
   */
  const handleDelete = async () => {
    try {
      setDeleting(true)

      // Delete images from storage
      for (const imageUrl of images) {
        if (imageUrl.includes('firebase')) {
          try {
            const imageRef = ref(storage, imageUrl)
            await deleteObject(imageRef)
          } catch (error) {
            console.error('Error deleting image:', error)
          }
        }
      }

      // Delete product document
      await deleteDoc(doc(db, 'products', productId))

      toast.success('Product deleted successfully!')
      router.push('/admin/products')
      
    } catch (error) {
      console.error('Error deleting product:', error)
      toast.error('Failed to delete product')
    } finally {
      setDeleting(false)
      setShowDeleteConfirm(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-accent-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Loading product...</p>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-400 text-lg">Product not found</p>
          <button
            onClick={() => router.push('/admin/products')}
            className="mt-4 bg-accent-600 hover:bg-accent-700 text-white px-4 py-2 rounded-lg"
          >
            Back to Products
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push('/admin/products')}
              className="bg-gray-800 hover:bg-gray-700 text-white p-2 rounded-lg transition-colors"
            >
              <ArrowLeft size={20} />
            </button>
            <div>
              <h1 className="text-3xl font-bold">Edit Product</h1>
              <p className="text-gray-400">Update product information and settings</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowDeleteConfirm(true)}
              disabled={deleting}
              className="bg-red-600 hover:bg-red-700 disabled:bg-red-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              <Trash2 size={16} />
              <span>Delete</span>
            </button>
            <button
              onClick={handleSave}
              disabled={saving || uploadingImages}
              className="bg-accent-600 hover:bg-accent-700 disabled:bg-accent-800 text-white px-6 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              <Save size={16} />
              <span>{saving ? 'Saving...' : uploadingImages ? 'Uploading...' : 'Save Changes'}</span>
            </button>
          </div>
        </div>

        {/* Main Form */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Basic Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <div className="bg-gray-900 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Basic Information</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Product Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                    placeholder="Enter product name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Description *
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                    placeholder="Enter product description"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Price ($) *
                    </label>
                    <input
                      type="number"
                      name="price"
                      value={formData.price}
                      onChange={handleInputChange}
                      step="0.01"
                      min="0"
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                      placeholder="0.00"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Category *
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                      required
                    >
                      <option value="">Select category</option>
                      {categories.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Images */}
            <div className="bg-gray-900 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Product Images</h2>

              {/* Existing Images */}
              {images.length > 0 && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-300 mb-2">Current Images</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {images.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={image}
                          alt={`Product ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                        <button
                          onClick={() => removeImage(image, index)}
                          className="absolute top-1 right-1 bg-red-600 hover:bg-red-700 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X size={12} />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* New Images */}
              {newImages.length > 0 && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-300 mb-2">New Images to Upload</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {newImages.map((file, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`New ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                        <button
                          onClick={() => setNewImages(prev => prev.filter((_, i) => i !== index))}
                          className="absolute top-1 right-1 bg-red-600 hover:bg-red-700 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X size={12} />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Upload Button */}
              <div>
                <label className="block">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                  <div className="border-2 border-dashed border-gray-700 rounded-lg p-6 text-center hover:border-accent-500 transition-colors cursor-pointer">
                    <Upload className="mx-auto mb-2 text-gray-400" size={24} />
                    <p className="text-gray-400">Click to upload images</p>
                    <p className="text-xs text-gray-500 mt-1">PNG, JPG, GIF up to 10MB each</p>
                  </div>
                </label>
              </div>
            </div>

            {/* Specifications */}
            <div className="bg-gray-900 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Specifications</h2>

              {/* Existing Specifications */}
              {Object.entries(formData.specifications).length > 0 && (
                <div className="mb-4 space-y-2">
                  {Object.entries(formData.specifications).map(([key, value]) => (
                    <div key={key} className="flex items-center space-x-2 bg-gray-800 p-2 rounded">
                      <span className="text-sm font-medium text-gray-300 flex-1">{key}:</span>
                      <span className="text-sm text-white flex-1">{value}</span>
                      <button
                        onClick={() => removeSpecification(key)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {/* Add New Specification */}
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newSpecKey}
                  onChange={(e) => setNewSpecKey(e.target.value)}
                  placeholder="Specification name"
                  className="flex-1 bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                />
                <input
                  type="text"
                  value={newSpecValue}
                  onChange={(e) => setNewSpecValue(e.target.value)}
                  placeholder="Value"
                  className="flex-1 bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                />
                <button
                  onClick={addSpecification}
                  className="bg-accent-600 hover:bg-accent-700 text-white px-3 py-2 rounded"
                >
                  <Plus size={16} />
                </button>
              </div>
            </div>

            {/* Tags */}
            <div className="bg-gray-900 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Tags</h2>

              {/* Existing Tags */}
              {formData.tags.length > 0 && (
                <div className="mb-4 flex flex-wrap gap-2">
                  {formData.tags.map((tag) => (
                    <span
                      key={tag}
                      className="bg-accent-600 text-white px-3 py-1 rounded-full text-sm flex items-center space-x-1"
                    >
                      <span>{tag}</span>
                      <button
                        onClick={() => removeTag(tag)}
                        className="text-accent-200 hover:text-white"
                      >
                        <X size={12} />
                      </button>
                    </span>
                  ))}
                </div>
              )}

              {/* Add New Tag */}
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add tag"
                  className="flex-1 bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                  onKeyPress={(e) => e.key === 'Enter' && addTag()}
                />
                <button
                  onClick={addTag}
                  className="bg-accent-600 hover:bg-accent-700 text-white px-3 py-2 rounded"
                >
                  <Plus size={16} />
                </button>
              </div>
            </div>
          </div>

          {/* Right Column - Settings */}
          <div className="space-y-6">
            {/* Stock & Availability */}
            <div className="bg-gray-900 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Stock & Availability</h2>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    name="inStock"
                    checked={formData.inStock}
                    onChange={handleInputChange}
                    className="w-4 h-4 text-accent-600 bg-gray-800 border-gray-700 rounded focus:ring-accent-500"
                  />
                  <label className="text-sm font-medium text-gray-300">
                    In Stock
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Stock Quantity
                  </label>
                  <input
                    type="number"
                    name="stockQuantity"
                    value={formData.stockQuantity}
                    onChange={handleInputChange}
                    min="0"
                    className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                    placeholder="0"
                  />
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    name="featured"
                    checked={formData.featured}
                    onChange={handleInputChange}
                    className="w-4 h-4 text-accent-600 bg-gray-800 border-gray-700 rounded focus:ring-accent-500"
                  />
                  <label className="text-sm font-medium text-gray-300">
                    Featured Product
                  </label>
                </div>
              </div>
            </div>

            {/* Product Info */}
            <div className="bg-gray-900 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Product Info</h2>

              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Product ID:</span>
                  <span className="text-white font-mono">{productId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Created:</span>
                  <span className="text-white">
                    {product?.createdAt?.toDate?.()?.toLocaleDateString() || 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Last Updated:</span>
                  <span className="text-white">
                    {product?.updatedAt?.toDate?.()?.toLocaleDateString() || 'N/A'}
                  </span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="bg-gray-900 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Actions</h2>

              <div className="space-y-3">
                <button
                  onClick={handleSave}
                  disabled={saving || uploadingImages}
                  className="w-full bg-accent-600 hover:bg-accent-700 disabled:bg-accent-800 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors"
                >
                  <Save size={16} />
                  <span>{saving ? 'Saving...' : uploadingImages ? 'Uploading...' : 'Save Changes'}</span>
                </button>

                <button
                  onClick={() => router.push('/admin/products')}
                  className="w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  Cancel
                </button>

                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  disabled={deleting}
                  className="w-full bg-red-600 hover:bg-red-700 disabled:bg-red-800 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors"
                >
                  <Trash2 size={16} />
                  <span>{deleting ? 'Deleting...' : 'Delete Product'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-900 rounded-lg p-6 max-w-md mx-4">
              <div className="flex items-center space-x-3 mb-4">
                <AlertTriangle className="text-red-500" size={24} />
                <h3 className="text-xl font-semibold text-white">Delete Product</h3>
              </div>

              <p className="text-gray-300 mb-6">
                Are you sure you want to delete "{product?.name}"? This action cannot be undone.
              </p>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  disabled={deleting}
                  className="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-red-800 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  {deleting ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
