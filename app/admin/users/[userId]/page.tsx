/**
 * Admin User Detail Page
 * 
 * Comprehensive user management interface for administrators to view
 * and manage individual user accounts, including profile information,
 * order history, activity logs, and account actions.
 * 
 * Features:
 * - Complete user profile display
 * - Order history and transaction details
 * - Activity logs and audit trail
 * - Account status management
 * - User permissions and roles
 * - Communication tools
 * - Mobile-responsive admin interface
 * 
 * Route: /admin/users/[userId]
 * Authentication: Required (Admin only)
 * Navigation: Dynamic route from admin user management
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  Shield, 
  ShoppingBag,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Edit,
  MessageSquare,
  Ban,
  UserCheck,
  Crown,
  Star,
  DollarSign,
  Package
} from 'lucide-react'
import { useUser } from '@/lib/useUser'
import { validateUserId, logRouteValidationError } from '@/lib/utils/routeValidation'
import { notFound } from 'next/navigation'

// Types for user details
interface UserProfile {
  id: string
  displayName: string
  email: string
  phone?: string
  avatar?: string
  joinDate: Date
  lastActive: Date
  status: 'active' | 'suspended' | 'banned' | 'pending'
  role: 'user' | 'premium' | 'moderator' | 'admin'
  tier: string
  points: number
  totalSpent: number
  orderCount: number
  isEmailVerified: boolean
  isPhoneVerified: boolean
}

interface UserOrder {
  id: string
  orderNumber: string
  date: Date
  total: number
  status: string
  itemCount: number
}

interface UserActivity {
  id: string
  type: 'login' | 'purchase' | 'profile_update' | 'password_change' | 'support_ticket'
  description: string
  timestamp: Date
  ipAddress?: string
  userAgent?: string
}

/**
 * Admin User Detail Page Component
 */
export default function AdminUserDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { user: currentUser } = useUser()

  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [userOrders, setUserOrders] = useState<UserOrder[]>([])
  const [userActivity, setUserActivity] = useState<UserActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'profile' | 'orders' | 'activity' | 'actions'>('profile')

  // Enhanced parameter validation
  const rawUserId = params.userId as string

  // Validate user ID format (Firebase UID)
  const validationResult = validateUserId(rawUserId)

  if (!validationResult.isValid) {
    // Log validation error for monitoring
    logRouteValidationError(validationResult.error!, {
      route: '/admin/users/[userId]',
      value: rawUserId,
      timestamp: new Date()
    })

    // Return 404 for invalid user IDs
    notFound()
  }

  const userId = validationResult.sanitizedValue!

  // Mock data for demonstration - replace with actual API calls
  useEffect(() => {
    const loadUserDetails = async () => {
      setLoading(true)
      setError(null)
      
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock user profile data
        const mockProfile: UserProfile = {
          id: userId,
          displayName: 'John Doe',
          email: '<EMAIL>',
          phone: '+****************',
          avatar: '/images/avatars/user-avatar.jpg',
          joinDate: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
          lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000),
          status: 'active',
          role: 'premium',
          tier: 'Gold',
          points: 2450,
          totalSpent: 1247.89,
          orderCount: 12,
          isEmailVerified: true,
          isPhoneVerified: false
        }
        
        // Mock user orders
        const mockOrders: UserOrder[] = [
          {
            id: '1',
            orderNumber: 'ORD-001',
            date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            total: 149.99,
            status: 'delivered',
            itemCount: 3
          },
          {
            id: '2',
            orderNumber: 'ORD-002',
            date: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
            total: 89.99,
            status: 'delivered',
            itemCount: 1
          }
        ]
        
        // Mock user activity
        const mockActivity: UserActivity[] = [
          {
            id: '1',
            type: 'login',
            description: 'User logged in',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            ipAddress: '*************',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          },
          {
            id: '2',
            type: 'purchase',
            description: 'Completed order ORD-001',
            timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            ipAddress: '*************'
          },
          {
            id: '3',
            type: 'profile_update',
            description: 'Updated profile information',
            timestamp: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
            ipAddress: '*************'
          }
        ]
        
        setUserProfile(mockProfile)
        setUserOrders(mockOrders)
        setUserActivity(mockActivity)
      } catch (err) {
        setError('Failed to load user details. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    if (currentUser && userId) {
      loadUserDetails()
    }
  }, [currentUser, userId])

  const getStatusIcon = (status: UserProfile['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="text-green-500" size={16} />
      case 'suspended':
        return <AlertTriangle className="text-yellow-500" size={16} />
      case 'banned':
        return <XCircle className="text-red-500" size={16} />
      case 'pending':
        return <Calendar className="text-blue-500" size={16} />
      default:
        return <User className="text-gray-500" size={16} />
    }
  }

  const getStatusColor = (status: UserProfile['status']) => {
    switch (status) {
      case 'active':
        return 'text-green-500 bg-green-500/10'
      case 'suspended':
        return 'text-yellow-500 bg-yellow-500/10'
      case 'banned':
        return 'text-red-500 bg-red-500/10'
      case 'pending':
        return 'text-blue-500 bg-blue-500/10'
      default:
        return 'text-gray-500 bg-gray-500/10'
    }
  }

  const getRoleIcon = (role: UserProfile['role']) => {
    switch (role) {
      case 'admin':
        return <Crown className="text-yellow-500" size={16} />
      case 'moderator':
        return <Shield className="text-purple-500" size={16} />
      case 'premium':
        return <Star className="text-blue-500" size={16} />
      default:
        return <User className="text-gray-500" size={16} />
    }
  }

  const getActivityIcon = (type: UserActivity['type']) => {
    switch (type) {
      case 'login':
        return <UserCheck className="text-green-500" size={16} />
      case 'purchase':
        return <ShoppingBag className="text-blue-500" size={16} />
      case 'profile_update':
        return <Edit className="text-purple-500" size={16} />
      case 'password_change':
        return <Shield className="text-yellow-500" size={16} />
      case 'support_ticket':
        return <MessageSquare className="text-orange-500" size={16} />
      default:
        return <Activity className="text-gray-500" size={16} />
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
      </div>
    )
  }

  if (error || !userProfile) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="mx-auto text-red-500 mb-4" size={48} />
          <h3 className="text-lg font-medium text-gray-300 mb-2">User Not Found</h3>
          <p className="text-gray-500 mb-4">
            {error || 'The user you\'re looking for doesn\'t exist.'}
          </p>
          <button
            onClick={() => router.push('/admin/users')}
            className="flex items-center space-x-2 mx-auto px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors"
          >
            <ArrowLeft size={16} />
            <span>Back to Users</span>
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-950">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push('/admin/users')}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ArrowLeft size={20} />
            </button>
            <div className="flex items-center space-x-4">
              <img
                src={userProfile.avatar || '/images/default-avatar.jpg'}
                alt={userProfile.displayName}
                className="w-12 h-12 rounded-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = '/images/default-avatar.jpg'
                }}
              />
              <div>
                <h1 className="text-2xl font-bold text-white">{userProfile.displayName}</h1>
                <p className="text-gray-400">{userProfile.email}</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {getStatusIcon(userProfile.status)}
            <span className={`px-3 py-1 rounded-full text-sm font-medium capitalize ${getStatusColor(userProfile.status)}`}>
              {userProfile.status}
            </span>
          </div>
        </div>

        {/* User Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Spent</p>
                <p className="text-2xl font-bold text-white">${userProfile.totalSpent.toFixed(2)}</p>
              </div>
              <DollarSign className="text-green-500" size={24} />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Orders</p>
                <p className="text-2xl font-bold text-white">{userProfile.orderCount}</p>
              </div>
              <Package className="text-blue-500" size={24} />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Points</p>
                <p className="text-2xl font-bold text-white">{userProfile.points.toLocaleString()}</p>
              </div>
              <Star className="text-yellow-500" size={24} />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Tier</p>
                <p className="text-2xl font-bold text-white">{userProfile.tier}</p>
              </div>
              <Crown className="text-purple-500" size={24} />
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-800 rounded-lg p-1 mb-8">
          {[
            { id: 'profile', label: 'Profile', icon: User },
            { id: 'orders', label: 'Orders', icon: ShoppingBag },
            { id: 'activity', label: 'Activity', icon: Activity },
            { id: 'actions', label: 'Actions', icon: Shield }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-accent-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <tab.icon size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h3 className="font-medium text-white mb-4">Personal Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="text-sm text-gray-400">Display Name</label>
                  <p className="text-white">{userProfile.displayName}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-400">Email</label>
                  <div className="flex items-center space-x-2">
                    <p className="text-white">{userProfile.email}</p>
                    {userProfile.isEmailVerified ? (
                      <CheckCircle className="text-green-500" size={16} />
                    ) : (
                      <XCircle className="text-red-500" size={16} />
                    )}
                  </div>
                </div>
                {userProfile.phone && (
                  <div>
                    <label className="text-sm text-gray-400">Phone</label>
                    <div className="flex items-center space-x-2">
                      <p className="text-white">{userProfile.phone}</p>
                      {userProfile.isPhoneVerified ? (
                        <CheckCircle className="text-green-500" size={16} />
                      ) : (
                        <XCircle className="text-red-500" size={16} />
                      )}
                    </div>
                  </div>
                )}
                <div>
                  <label className="text-sm text-gray-400">Role</label>
                  <div className="flex items-center space-x-2">
                    {getRoleIcon(userProfile.role)}
                    <p className="text-white capitalize">{userProfile.role}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h3 className="font-medium text-white mb-4">Account Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="text-sm text-gray-400">Join Date</label>
                  <p className="text-white">{userProfile.joinDate.toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-400">Last Active</label>
                  <p className="text-white">{userProfile.lastActive.toLocaleString()}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-400">Account Status</label>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(userProfile.status)}
                    <p className="text-white capitalize">{userProfile.status}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Orders Tab */}
        {activeTab === 'orders' && (
          <div className="space-y-4">
            {userOrders.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingBag className="mx-auto text-gray-600 mb-4" size={48} />
                <h3 className="text-lg font-medium text-gray-300 mb-2">No Orders</h3>
                <p className="text-gray-500">This user hasn't placed any orders yet.</p>
              </div>
            ) : (
              userOrders.map((order) => (
                <motion.div
                  key={order.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gray-800 rounded-lg p-6 border border-gray-700"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-white">{order.orderNumber}</h4>
                      <p className="text-sm text-gray-400">
                        {order.date.toLocaleDateString()} • {order.itemCount} items
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-white">${order.total.toFixed(2)}</p>
                      <p className="text-sm text-gray-400 capitalize">{order.status}</p>
                    </div>
                  </div>
                </motion.div>
              ))
            )}
          </div>
        )}

        {/* Activity Tab */}
        {activeTab === 'activity' && (
          <div className="space-y-4">
            {userActivity.map((activity) => (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 rounded-lg p-6 border border-gray-700"
              >
                <div className="flex items-start space-x-3">
                  {getActivityIcon(activity.type)}
                  <div className="flex-1">
                    <p className="text-white">{activity.description}</p>
                    <p className="text-sm text-gray-400">{activity.timestamp.toLocaleString()}</p>
                    {activity.ipAddress && (
                      <p className="text-xs text-gray-500">IP: {activity.ipAddress}</p>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Actions Tab */}
        {activeTab === 'actions' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-medium text-white">Account Actions</h3>
              <button className="w-full flex items-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                <MessageSquare size={16} />
                <span>Send Message</span>
              </button>
              <button className="w-full flex items-center space-x-2 px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                <UserCheck size={16} />
                <span>Verify Account</span>
              </button>
              <button className="w-full flex items-center space-x-2 px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                <Crown size={16} />
                <span>Change Role</span>
              </button>
            </div>
            
            <div className="space-y-4">
              <h3 className="font-medium text-white">Moderation Actions</h3>
              <button className="w-full flex items-center space-x-2 px-4 py-3 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors">
                <AlertTriangle size={16} />
                <span>Suspend Account</span>
              </button>
              <button className="w-full flex items-center space-x-2 px-4 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                <Ban size={16} />
                <span>Ban Account</span>
              </button>
              <button className="w-full flex items-center space-x-2 px-4 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                <XCircle size={16} />
                <span>Delete Account</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
