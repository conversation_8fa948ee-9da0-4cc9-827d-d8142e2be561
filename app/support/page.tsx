/**
 * Help & Support Page
 * 
 * Provides comprehensive support resources including FAQ, contact options,
 * knowledge base, and ticket system for Syndicaps users.
 * 
 * Features:
 * - FAQ sections with search
 * - Contact form and support tickets
 * - Knowledge base articles
 * - Live chat integration
 * - Community support links
 * - Mobile-responsive design
 * 
 * Route: /support
 * Authentication: Optional (enhanced features for logged-in users)
 * Navigation: Referenced in PROFILE_NAVIGATION.settings.support
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  HelpCircle, 
  Search, 
  MessageCircle, 
  Mail, 
  Phone, 
  Book, 
  Users, 
  ChevronDown,
  ChevronRight,
  ExternalLink,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { useUser } from '@/lib/useUser'

// Types for support
interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
  isExpanded?: boolean
}

interface SupportTicket {
  id: string
  subject: string
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  createdAt: Date
  lastUpdate: Date
}

/**
 * Help & Support Page Component
 */
export default function SupportPage() {
  const { user } = useUser()
  
  const [activeTab, setActiveTab] = useState<'faq' | 'contact' | 'tickets' | 'resources'>('faq')
  const [searchQuery, setSearchQuery] = useState('')
  const [faqItems, setFaqItems] = useState<FAQItem[]>([])
  const [tickets, setTickets] = useState<SupportTicket[]>([])
  const [loading, setLoading] = useState(true)

  // Mock data for demonstration
  useEffect(() => {
    const loadSupportData = async () => {
      setLoading(true)
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock FAQ data
      const mockFAQ: FAQItem[] = [
        {
          id: '1',
          question: 'How do I track my order?',
          answer: 'You can track your order by visiting the Order Tracking page in your profile. You\'ll need your order number or tracking number to get real-time updates on your shipment status.',
          category: 'Orders & Shipping'
        },
        {
          id: '2',
          question: 'What payment methods do you accept?',
          answer: 'We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers. All payments are processed securely through our encrypted payment system.',
          category: 'Payment & Billing'
        },
        {
          id: '3',
          question: 'How do I earn and redeem points?',
          answer: 'You earn 5 points for every $1 spent, plus bonus points for community activities. Points can be redeemed in our Rewards Shop for exclusive keycaps, discounts, and digital content.',
          category: 'Rewards & Points'
        },
        {
          id: '4',
          question: 'Can I return or exchange my keycaps?',
          answer: 'Yes! We offer a 30-day return policy for unused items in original packaging. Custom or personalized items may have different return conditions. Contact support for assistance.',
          category: 'Returns & Exchanges'
        },
        {
          id: '5',
          question: 'How do I participate in community challenges?',
          answer: 'Visit the Community Challenges page in your profile to see active challenges. Simply click on a challenge to join and start tracking your progress toward earning rewards.',
          category: 'Community & Challenges'
        }
      ]
      
      // Mock tickets for logged-in users
      const mockTickets: SupportTicket[] = user ? [
        {
          id: 'TKT-001',
          subject: 'Issue with Galaxy Keycap Set delivery',
          status: 'in_progress',
          priority: 'medium',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          lastUpdate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
        },
        {
          id: 'TKT-002',
          subject: 'Points not credited for recent purchase',
          status: 'resolved',
          priority: 'low',
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          lastUpdate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
        }
      ] : []
      
      setFaqItems(mockFAQ)
      setTickets(mockTickets)
      setLoading(false)
    }

    loadSupportData()
  }, [user])

  const toggleFAQ = (id: string) => {
    setFaqItems(prev => prev.map(item => 
      item.id === id ? { ...item, isExpanded: !item.isExpanded } : item
    ))
  }

  const filteredFAQ = faqItems.filter(item =>
    item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.category.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getStatusIcon = (status: SupportTicket['status']) => {
    switch (status) {
      case 'open':
        return <AlertCircle className="text-yellow-500" size={16} />
      case 'in_progress':
        return <Clock className="text-blue-500" size={16} />
      case 'resolved':
      case 'closed':
        return <CheckCircle className="text-green-500" size={16} />
      default:
        return <HelpCircle className="text-gray-500" size={16} />
    }
  }

  const getStatusColor = (status: SupportTicket['status']) => {
    switch (status) {
      case 'open':
        return 'text-yellow-500'
      case 'in_progress':
        return 'text-blue-500'
      case 'resolved':
      case 'closed':
        return 'text-green-500'
      default:
        return 'text-gray-500'
    }
  }

  const getPriorityColor = (priority: SupportTicket['priority']) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-500 bg-red-500/10'
      case 'high':
        return 'text-orange-500 bg-orange-500/10'
      case 'medium':
        return 'text-yellow-500 bg-yellow-500/10'
      case 'low':
        return 'text-green-500 bg-green-500/10'
      default:
        return 'text-gray-500 bg-gray-500/10'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-950">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-4">Help & Support</h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Get help with your Syndicaps experience. Find answers to common questions, 
            contact our support team, or browse our knowledge base.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <button className="flex items-center space-x-3 p-4 bg-gray-800 hover:bg-gray-700 rounded-lg border border-gray-700 transition-colors">
            <MessageCircle className="text-accent-500" size={24} />
            <div className="text-left">
              <p className="font-medium text-white">Live Chat</p>
              <p className="text-sm text-gray-400">Get instant help</p>
            </div>
          </button>
          
          <button className="flex items-center space-x-3 p-4 bg-gray-800 hover:bg-gray-700 rounded-lg border border-gray-700 transition-colors">
            <Mail className="text-blue-500" size={24} />
            <div className="text-left">
              <p className="font-medium text-white">Email Support</p>
              <p className="text-sm text-gray-400"><EMAIL></p>
            </div>
          </button>
          
          <button className="flex items-center space-x-3 p-4 bg-gray-800 hover:bg-gray-700 rounded-lg border border-gray-700 transition-colors">
            <Users className="text-green-500" size={24} />
            <div className="text-left">
              <p className="font-medium text-white">Community</p>
              <p className="text-sm text-gray-400">Join discussions</p>
            </div>
          </button>
          
          <button className="flex items-center space-x-3 p-4 bg-gray-800 hover:bg-gray-700 rounded-lg border border-gray-700 transition-colors">
            <Book className="text-purple-500" size={24} />
            <div className="text-left">
              <p className="font-medium text-white">Knowledge Base</p>
              <p className="text-sm text-gray-400">Browse articles</p>
            </div>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-800 rounded-lg p-1 mb-8">
          {[
            { id: 'faq', label: 'FAQ', icon: HelpCircle },
            { id: 'contact', label: 'Contact Us', icon: Mail },
            ...(user ? [{ id: 'tickets', label: 'My Tickets', icon: MessageCircle }] : []),
            { id: 'resources', label: 'Resources', icon: Book }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-accent-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <tab.icon size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* FAQ Tab */}
        {activeTab === 'faq' && (
          <div className="space-y-6">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search frequently asked questions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
              />
            </div>

            {/* FAQ Items */}
            <div className="space-y-4">
              {filteredFAQ.map((item) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden"
                >
                  <button
                    onClick={() => toggleFAQ(item.id)}
                    className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex-1">
                      <h3 className="font-medium text-white mb-1">{item.question}</h3>
                      <p className="text-sm text-accent-500">{item.category}</p>
                    </div>
                    {item.isExpanded ? (
                      <ChevronDown className="text-gray-400" size={20} />
                    ) : (
                      <ChevronRight className="text-gray-400" size={20} />
                    )}
                  </button>
                  
                  {item.isExpanded && (
                    <div className="px-6 pb-4 border-t border-gray-700">
                      <p className="text-gray-300 leading-relaxed pt-4">{item.answer}</p>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Contact Tab */}
        {activeTab === 'contact' && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-xl font-semibold text-white mb-6">Contact Support</h2>
              
              <form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Name
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                      placeholder="Your name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Subject
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                    placeholder="Brief description of your issue"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Category
                  </label>
                  <select className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent">
                    <option>Orders & Shipping</option>
                    <option>Payment & Billing</option>
                    <option>Returns & Exchanges</option>
                    <option>Technical Issues</option>
                    <option>Account & Profile</option>
                    <option>Other</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Message
                  </label>
                  <textarea
                    rows={6}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                    placeholder="Please provide as much detail as possible about your issue..."
                  />
                </div>
                
                <button
                  type="submit"
                  className="w-full px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors"
                >
                  Send Message
                </button>
              </form>
            </div>
          </div>
        )}

        {/* Tickets Tab (for logged-in users) */}
        {activeTab === 'tickets' && user && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-white">My Support Tickets</h2>
              <button className="px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors">
                Create New Ticket
              </button>
            </div>

            {tickets.length === 0 ? (
              <div className="text-center py-12">
                <MessageCircle className="mx-auto text-gray-600 mb-4" size={48} />
                <h3 className="text-lg font-medium text-gray-300 mb-2">No Support Tickets</h3>
                <p className="text-gray-500">You haven't created any support tickets yet</p>
              </div>
            ) : (
              <div className="space-y-4">
                {tickets.map((ticket) => (
                  <motion.div
                    key={ticket.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gray-800 rounded-lg p-6 border border-gray-700"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          {getStatusIcon(ticket.status)}
                          <h3 className="font-medium text-white">{ticket.subject}</h3>
                          <span className="text-sm text-gray-400">#{ticket.id}</span>
                        </div>
                        <div className="flex flex-wrap gap-4 text-sm text-gray-400">
                          <span className={`capitalize ${getStatusColor(ticket.status)}`}>
                            {ticket.status.replace('_', ' ')}
                          </span>
                          <span>•</span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getPriorityColor(ticket.priority)}`}>
                            {ticket.priority} priority
                          </span>
                          <span>•</span>
                          <span>Created {ticket.createdAt.toLocaleDateString()}</span>
                        </div>
                      </div>
                      
                      <button className="flex items-center space-x-1 px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
                        <ExternalLink size={16} />
                        <span className="text-sm">View Details</span>
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Resources Tab */}
        {activeTab === 'resources' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                title: 'Getting Started Guide',
                description: 'Learn the basics of using Syndicaps',
                icon: Book,
                color: 'text-blue-500'
              },
              {
                title: 'Keycap Care Guide',
                description: 'How to maintain your keycaps',
                icon: HelpCircle,
                color: 'text-green-500'
              },
              {
                title: 'Community Guidelines',
                description: 'Rules and best practices',
                icon: Users,
                color: 'text-purple-500'
              },
              {
                title: 'Shipping Information',
                description: 'Delivery times and policies',
                icon: Clock,
                color: 'text-yellow-500'
              },
              {
                title: 'Return Policy',
                description: 'How to return or exchange items',
                icon: CheckCircle,
                color: 'text-red-500'
              },
              {
                title: 'API Documentation',
                description: 'For developers and integrations',
                icon: Book,
                color: 'text-orange-500'
              }
            ].map((resource, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-colors cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-3">
                  <resource.icon className={resource.color} size={24} />
                  <h3 className="font-medium text-white">{resource.title}</h3>
                </div>
                <p className="text-gray-400 text-sm mb-4">{resource.description}</p>
                <div className="flex items-center text-accent-500 text-sm">
                  <span>Read more</span>
                  <ExternalLink size={14} className="ml-1" />
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
