/**
 * Billing & Payment Page
 * 
 * Allows users to manage their payment methods, view billing history,
 * and update billing information for their Syndicaps account.
 * 
 * Features:
 * - Payment methods management
 * - Billing history and invoices
 * - Subscription management
 * - Tax information
 * - Payment preferences
 * - Mobile-responsive design
 * 
 * Route: /profile/billing
 * Authentication: Required
 * Navigation: Referenced in PROFILE_NAVIGATION.settings.billing
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  CreditCard, 
  Calendar, 
  Download, 
  Plus, 
  Edit, 
  Trash2, 
  Shield, 
  Receipt,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'

// Types for billing
interface PaymentMethod {
  id: string
  type: 'card' | 'paypal' | 'bank'
  brand?: string
  last4?: string
  expiryMonth?: number
  expiryYear?: number
  isDefault: boolean
  email?: string // for PayPal
  bankName?: string // for bank transfers
}

interface BillingTransaction {
  id: string
  date: Date
  description: string
  amount: number
  status: 'completed' | 'pending' | 'failed' | 'refunded'
  paymentMethod: string
  invoiceUrl?: string
  refundAmount?: number
}

/**
 * Billing & Payment Page Component
 */
export default function BillingPage() {
  const { user, profile, loading: profileLoading } = useUser()
  
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [transactions, setTransactions] = useState<BillingTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'methods' | 'history' | 'settings'>('methods')

  // Mock data for demonstration - replace with actual API calls
  useEffect(() => {
    const loadBillingData = async () => {
      setLoading(true)
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock payment methods
      const mockPaymentMethods: PaymentMethod[] = [
        {
          id: '1',
          type: 'card',
          brand: 'Visa',
          last4: '4242',
          expiryMonth: 12,
          expiryYear: 2025,
          isDefault: true
        },
        {
          id: '2',
          type: 'paypal',
          email: '<EMAIL>',
          isDefault: false
        }
      ]
      
      // Mock transactions
      const mockTransactions: BillingTransaction[] = [
        {
          id: '1',
          date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          description: 'Galaxy Keycap Set - Premium Edition',
          amount: 149.99,
          status: 'completed',
          paymentMethod: 'Visa ****4242',
          invoiceUrl: '/api/invoices/inv_001'
        },
        {
          id: '2',
          date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          description: 'Artisan Keycap - Dragon Design',
          amount: 89.99,
          status: 'completed',
          paymentMethod: 'PayPal',
          invoiceUrl: '/api/invoices/inv_002'
        },
        {
          id: '3',
          date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
          description: 'Custom Cable - Purple Coiled',
          amount: 45.00,
          status: 'refunded',
          paymentMethod: 'Visa ****4242',
          refundAmount: 45.00
        }
      ]
      
      setPaymentMethods(mockPaymentMethods)
      setTransactions(mockTransactions)
      setLoading(false)
    }

    if (user) {
      loadBillingData()
    }
  }, [user])

  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method.type) {
      case 'card':
        return <CreditCard className="text-blue-500" size={20} />
      case 'paypal':
        return <div className="w-5 h-5 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold">P</div>
      case 'bank':
        return <DollarSign className="text-green-500" size={20} />
      default:
        return <CreditCard className="text-gray-500" size={20} />
    }
  }

  const getStatusIcon = (status: BillingTransaction['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="text-green-500" size={16} />
      case 'pending':
        return <Clock className="text-yellow-500" size={16} />
      case 'failed':
        return <AlertCircle className="text-red-500" size={16} />
      case 'refunded':
        return <Receipt className="text-blue-500" size={16} />
      default:
        return <Clock className="text-gray-500" size={16} />
    }
  }

  const getStatusColor = (status: BillingTransaction['status']) => {
    switch (status) {
      case 'completed':
        return 'text-green-500'
      case 'pending':
        return 'text-yellow-500'
      case 'failed':
        return 'text-red-500'
      case 'refunded':
        return 'text-blue-500'
      default:
        return 'text-gray-500'
    }
  }

  if (profileLoading || loading) {
    return (
      <ProfileLayout 
        navigation="consolidated"
      >
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout 
      navigation="consolidated"
    >
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-white">Billing & Payment</h1>
          <p className="text-gray-400">Manage your payment methods and billing information</p>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
          {[
            { id: 'methods', label: 'Payment Methods', icon: CreditCard },
            { id: 'history', label: 'Billing History', icon: Receipt },
            { id: 'settings', label: 'Settings', icon: Shield }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-accent-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <tab.icon size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Payment Methods Tab */}
        {activeTab === 'methods' && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-semibold text-white">Payment Methods</h2>
              <button className="flex items-center space-x-2 px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors">
                <Plus size={16} />
                <span>Add Payment Method</span>
              </button>
            </div>

            {paymentMethods.length === 0 ? (
              <div className="text-center py-12">
                <CreditCard className="mx-auto text-gray-600 mb-4" size={48} />
                <h3 className="text-lg font-medium text-gray-300 mb-2">No Payment Methods</h3>
                <p className="text-gray-500">Add a payment method to make purchases</p>
              </div>
            ) : (
              <div className="space-y-4">
                {paymentMethods.map((method) => (
                  <motion.div
                    key={method.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gray-800 rounded-lg p-6 border border-gray-700"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        {getPaymentMethodIcon(method)}
                        <div>
                          <div className="flex items-center space-x-2">
                            <h3 className="font-medium text-white">
                              {method.type === 'card' && `${method.brand} ****${method.last4}`}
                              {method.type === 'paypal' && `PayPal (${method.email})`}
                              {method.type === 'bank' && `${method.bankName} Bank Transfer`}
                            </h3>
                            {method.isDefault && (
                              <span className="px-2 py-1 bg-accent-600 text-white text-xs rounded-full">
                                Default
                              </span>
                            )}
                          </div>
                          {method.type === 'card' && (
                            <p className="text-sm text-gray-400">
                              Expires {method.expiryMonth?.toString().padStart(2, '0')}/{method.expiryYear}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
                          <Edit size={16} />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded-lg transition-colors">
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Billing History Tab */}
        {activeTab === 'history' && (
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-white">Billing History</h2>

            {transactions.length === 0 ? (
              <div className="text-center py-12">
                <Receipt className="mx-auto text-gray-600 mb-4" size={48} />
                <h3 className="text-lg font-medium text-gray-300 mb-2">No Transactions</h3>
                <p className="text-gray-500">Your billing history will appear here</p>
              </div>
            ) : (
              <div className="space-y-4">
                {transactions.map((transaction) => (
                  <motion.div
                    key={transaction.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gray-800 rounded-lg p-6 border border-gray-700"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          {getStatusIcon(transaction.status)}
                          <h3 className="font-medium text-white">{transaction.description}</h3>
                        </div>
                        <div className="flex flex-wrap gap-4 text-sm text-gray-400">
                          <div className="flex items-center space-x-1">
                            <Calendar size={14} />
                            <span>{transaction.date.toLocaleDateString()}</span>
                          </div>
                          <span>•</span>
                          <span>{transaction.paymentMethod}</span>
                          <span>•</span>
                          <span className={`capitalize ${getStatusColor(transaction.status)}`}>
                            {transaction.status}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="font-semibold text-white">
                            ${transaction.amount.toFixed(2)}
                          </p>
                          {transaction.refundAmount && (
                            <p className="text-sm text-blue-400">
                              Refunded: ${transaction.refundAmount.toFixed(2)}
                            </p>
                          )}
                        </div>
                        
                        {transaction.invoiceUrl && (
                          <button className="flex items-center space-x-1 px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
                            <Download size={16} />
                            <span className="text-sm">Invoice</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-white">Billing Settings</h2>
            
            {/* Billing Address */}
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h3 className="font-medium text-white mb-4">Billing Address</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                    placeholder="Enter your full name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Company (Optional)
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                    placeholder="Company name"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Address
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                    placeholder="Street address"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    City
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                    placeholder="City"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    ZIP Code
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                    placeholder="ZIP code"
                  />
                </div>
              </div>
              
              <button className="mt-4 px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors">
                Save Billing Address
              </button>
            </div>

            {/* Tax Information */}
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h3 className="font-medium text-white mb-4">Tax Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Tax ID (Optional)
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                    placeholder="Enter your tax ID"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="tax-exempt"
                    className="w-4 h-4 text-accent-600 bg-gray-700 border-gray-600 rounded focus:ring-accent-500"
                  />
                  <label htmlFor="tax-exempt" className="text-sm text-gray-300">
                    I am tax exempt
                  </label>
                </div>
              </div>
              
              <button className="mt-4 px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors">
                Save Tax Information
              </button>
            </div>
          </div>
        )}
      </div>
    </ProfileLayout>
  )
}
