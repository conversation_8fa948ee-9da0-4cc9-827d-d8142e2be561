/**
 * Downloads Page
 * 
 * Allows users to access and download their digital purchases including
 * keycap design files, wallpapers, and other digital content.
 * 
 * Features:
 * - Digital purchase history
 * - Download links with expiration tracking
 * - File format information
 * - Download progress tracking
 * - Re-download capabilities
 * - Mobile-responsive design
 * 
 * Route: /profile/downloads
 * Authentication: Required
 * Navigation: Referenced in PROFILE_NAVIGATION.orders.downloads
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Download, 
  File, 
  Image, 
  Archive, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Search,
  Filter,
  Calendar,
  FileText,
  Palette
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'

// Types for downloads
interface DigitalDownload {
  id: string
  name: string
  type: 'design' | 'wallpaper' | 'document' | 'software'
  fileFormat: string
  fileSize: string
  purchaseDate: Date
  downloadCount: number
  maxDownloads: number
  expiryDate?: Date
  downloadUrl: string
  thumbnailUrl?: string
  description: string
  isExpired: boolean
}

/**
 * Downloads Page Component
 */
export default function DownloadsPage() {
  const { user, profile, loading: profileLoading } = useUser()
  
  const [downloads, setDownloads] = useState<DigitalDownload[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [downloadingIds, setDownloadingIds] = useState<Set<string>>(new Set())

  // Mock data for demonstration - replace with actual API calls
  useEffect(() => {
    const loadDownloads = async () => {
      setLoading(true)
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock downloads data
      const mockDownloads: DigitalDownload[] = [
        {
          id: '1',
          name: 'Galaxy Keycap Design Files',
          type: 'design',
          fileFormat: 'ZIP (AI, PSD, SVG)',
          fileSize: '45.2 MB',
          purchaseDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          downloadCount: 2,
          maxDownloads: 5,
          expiryDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000),
          downloadUrl: '/api/downloads/galaxy-design',
          thumbnailUrl: '/images/downloads/galaxy-thumb.jpg',
          description: 'Complete design files for Galaxy keycap set including source files',
          isExpired: false
        },
        {
          id: '2',
          name: 'Syndicaps Wallpaper Collection',
          type: 'wallpaper',
          fileFormat: 'ZIP (PNG, JPG)',
          fileSize: '128.5 MB',
          purchaseDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
          downloadCount: 1,
          maxDownloads: 3,
          downloadUrl: '/api/downloads/wallpaper-collection',
          description: '4K wallpapers featuring keycap designs and community art',
          isExpired: false
        },
        {
          id: '3',
          name: 'Keycap Design Guide PDF',
          type: 'document',
          fileFormat: 'PDF',
          fileSize: '12.8 MB',
          purchaseDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          downloadCount: 5,
          maxDownloads: 5,
          expiryDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
          downloadUrl: '/api/downloads/design-guide',
          description: 'Comprehensive guide to keycap design principles and techniques',
          isExpired: true
        }
      ]
      
      setDownloads(mockDownloads)
      setLoading(false)
    }

    if (user) {
      loadDownloads()
    }
  }, [user])

  const handleDownload = async (download: DigitalDownload) => {
    if (download.isExpired || download.downloadCount >= download.maxDownloads) {
      return
    }

    setDownloadingIds(prev => new Set(prev).add(download.id))
    
    try {
      // Simulate download process
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Update download count
      setDownloads(prev => prev.map(d => 
        d.id === download.id 
          ? { ...d, downloadCount: d.downloadCount + 1 }
          : d
      ))
      
      // In real implementation, trigger actual download
      // window.open(download.downloadUrl, '_blank')
      
    } catch (error) {
      console.error('Download failed:', error)
    } finally {
      setDownloadingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(download.id)
        return newSet
      })
    }
  }

  const getTypeIcon = (type: DigitalDownload['type']) => {
    switch (type) {
      case 'design':
        return <Palette className="text-purple-500" size={20} />
      case 'wallpaper':
        return <Image className="text-blue-500" size={20} />
      case 'document':
        return <FileText className="text-green-500" size={20} />
      case 'software':
        return <Archive className="text-orange-500" size={20} />
      default:
        return <File className="text-gray-500" size={20} />
    }
  }

  const getTypeColor = (type: DigitalDownload['type']) => {
    switch (type) {
      case 'design':
        return 'text-purple-500 bg-purple-500/10'
      case 'wallpaper':
        return 'text-blue-500 bg-blue-500/10'
      case 'document':
        return 'text-green-500 bg-green-500/10'
      case 'software':
        return 'text-orange-500 bg-orange-500/10'
      default:
        return 'text-gray-500 bg-gray-500/10'
    }
  }

  const filteredDownloads = downloads.filter(download => {
    const matchesSearch = download.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         download.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFilter = filterType === 'all' || download.type === filterType
    return matchesSearch && matchesFilter
  })

  if (profileLoading || loading) {
    return (
      <ProfileLayout 
        
        wishlistItemCount={0}
        navigation="consolidated"
      >
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout 
      
      wishlistItemCount={0}
      navigation="consolidated"
    >
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-white">Downloads</h1>
          <p className="text-gray-400">Access your digital purchases and downloads</p>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search downloads..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
            />
          </div>
          
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="pl-10 pr-8 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent appearance-none"
            >
              <option value="all">All Types</option>
              <option value="design">Design Files</option>
              <option value="wallpaper">Wallpapers</option>
              <option value="document">Documents</option>
              <option value="software">Software</option>
            </select>
          </div>
        </div>

        {/* Downloads List */}
        {filteredDownloads.length === 0 ? (
          <div className="text-center py-12">
            <Download className="mx-auto text-gray-600 mb-4" size={48} />
            <h3 className="text-lg font-medium text-gray-300 mb-2">No Downloads Available</h3>
            <p className="text-gray-500">
              {searchQuery || filterType !== 'all' 
                ? 'No downloads match your search criteria.' 
                : 'You haven\'t purchased any digital content yet.'}
            </p>
          </div>
        ) : (
          <div className="grid gap-6">
            {filteredDownloads.map((download) => (
              <motion.div
                key={download.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`bg-gray-800 rounded-lg p-6 border border-gray-700 ${
                  download.isExpired ? 'opacity-60' : ''
                }`}
              >
                <div className="flex flex-col sm:flex-row sm:items-start gap-4">
                  {/* Thumbnail */}
                  <div className="flex-shrink-0">
                    {download.thumbnailUrl ? (
                      <img
                        src={download.thumbnailUrl}
                        alt={download.name}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-gray-700 rounded-lg flex items-center justify-center">
                        {getTypeIcon(download.type)}
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-white">{download.name}</h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getTypeColor(download.type)}`}>
                            {download.type}
                          </span>
                        </div>
                        
                        <p className="text-sm text-gray-400 mb-3">{download.description}</p>
                        
                        <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <File size={14} />
                            <span>{download.fileFormat}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Archive size={14} />
                            <span>{download.fileSize}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar size={14} />
                            <span>Purchased {download.purchaseDate.toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>

                      {/* Download Info & Button */}
                      <div className="flex flex-col items-end gap-3">
                        <div className="text-right text-sm">
                          <div className="flex items-center gap-1 text-gray-400">
                            <Download size={14} />
                            <span>{download.downloadCount}/{download.maxDownloads} downloads</span>
                          </div>
                          {download.expiryDate && (
                            <div className={`flex items-center gap-1 mt-1 ${
                              download.isExpired ? 'text-red-400' : 'text-yellow-400'
                            }`}>
                              <Clock size={14} />
                              <span>
                                {download.isExpired 
                                  ? 'Expired' 
                                  : `Expires ${download.expiryDate.toLocaleDateString()}`}
                              </span>
                            </div>
                          )}
                        </div>

                        <button
                          onClick={() => handleDownload(download)}
                          disabled={download.isExpired || download.downloadCount >= download.maxDownloads || downloadingIds.has(download.id)}
                          className="flex items-center space-x-2 px-4 py-2 bg-accent-600 hover:bg-accent-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                        >
                          {downloadingIds.has(download.id) ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                              <span>Downloading...</span>
                            </>
                          ) : download.isExpired ? (
                            <>
                              <AlertCircle size={16} />
                              <span>Expired</span>
                            </>
                          ) : download.downloadCount >= download.maxDownloads ? (
                            <>
                              <CheckCircle size={16} />
                              <span>Downloaded</span>
                            </>
                          ) : (
                            <>
                              <Download size={16} />
                              <span>Download</span>
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </ProfileLayout>
  )
}
