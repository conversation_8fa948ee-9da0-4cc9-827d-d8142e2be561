'use client'

/**
 * Privacy Settings Page
 *
 * Comprehensive privacy settings interface for users including:
 * - Profile visibility controls
 * - Data sharing preferences
 * - Communication settings
 * - Security and privacy options
 * - Activity visibility controls
 * - Data retention settings
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Shield,
  Eye,
  Users,
  Globe,
  Lock,
  Bell,
  Database,
  Settings,
  Save,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  ArrowLeft
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import ProfileLayout from '@/components/profile/ProfileLayout'
import PrivacySection, { PrivacySectionGroup } from '@/components/ui/PrivacySection'
import PrivacyToggle from '@/components/ui/PrivacyToggle'
import PrivacyRadioGroup from '@/components/ui/PrivacyRadioGroup'
import PrivacyLevelPresets from '@/components/privacy/PrivacyLevelPresets'
import PrivacySetupWizard from '@/components/privacy/PrivacySetupWizard'
import { usePrivacySettings } from '@/lib/privacy/usePrivacySettings'
import { useUser } from '@/lib/useUser'
import toast from 'react-hot-toast'

/**
 * Privacy Settings Page Component
 */
export default function PrivacySettingsPage() {
  const router = useRouter()
  const { user, loading: userLoading } = useUser()
  const {
    settings,
    loading,
    saving,
    error,
    validation,
    isDirty,
    updateSetting,
    saveSettings,
    resetSettings
  } = usePrivacySettings()

  const [showValidationDetails, setShowValidationDetails] = useState(false)
  const [showPresets, setShowPresets] = useState(true)
  const [showWizard, setShowWizard] = useState(false)

  /**
   * Handle back navigation
   */
  const handleBack = () => {
    if (isDirty) {
      if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
        router.back()
      }
    } else {
      router.back()
    }
  }

  /**
   * Handle save settings
   */
  const handleSave = async () => {
    try {
      await saveSettings()
    } catch (error) {
      console.error('Error saving settings:', error)
    }
  }

  /**
   * Handle privacy preset selection
   */
  const handlePresetSelect = async (preset: any) => {
    try {
      // Apply preset settings
      Object.entries(preset.settings).forEach(([key, value]) => {
        updateSetting(key as any, value)
      })

      toast.success(`Applied ${preset.name} privacy settings`)
      setShowPresets(false)
    } catch (error) {
      console.error('Error applying preset:', error)
      toast.error('Failed to apply privacy preset')
    }
  }

  /**
   * Handle wizard completion
   */
  const handleWizardComplete = async (wizardSettings: any) => {
    try {
      // Apply wizard settings
      Object.entries(wizardSettings).forEach(([key, value]) => {
        updateSetting(key as any, value)
      })

      toast.success('Privacy settings configured successfully!')
      setShowWizard(false)
      setShowPresets(false)
    } catch (error) {
      console.error('Error applying wizard settings:', error)
      toast.error('Failed to apply privacy settings')
    }
  }

  /**
   * Handle reset settings
   */
  const handleReset = () => {
    if (confirm('Are you sure you want to reset all changes?')) {
      resetSettings()
    }
  }

  // Authentication check
  if (!userLoading && !user) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <Shield className="mx-auto text-gray-400 mb-4" size={48} />
            <h2 className="text-xl font-bold text-white mb-4">Authentication Required</h2>
            <p className="text-gray-400 mb-6">Please log in to manage your privacy settings.</p>
            <button
              onClick={() => window.location.href = '/auth?redirect=/profile/privacy'}
              className="bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-lg transition-colors"
            >
              Login to Continue
            </button>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  // Loading state
  if (userLoading || loading) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="space-y-6">
          {/* Loading skeleton */}
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-gray-800 rounded-lg p-6 animate-pulse">
              <div className="h-6 bg-gray-700 rounded w-1/3 mb-4"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-700 rounded w-full"></div>
                <div className="h-4 bg-gray-700 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </ProfileLayout>
    )
  }

  // Error state
  if (error) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="bg-red-900/20 border border-red-700 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="text-red-400" size={24} />
            <div>
              <h3 className="text-lg font-semibold text-red-400">Error Loading Privacy Settings</h3>
              <p className="text-red-300 mt-1">{error}</p>
            </div>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  if (!settings) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="text-center py-12">
          <Shield className="mx-auto text-gray-400 mb-4" size={48} />
          <h3 className="text-lg font-semibold text-gray-400">No Privacy Settings Found</h3>
          <p className="text-gray-500 mt-2">Unable to load your privacy settings.</p>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout navigation="consolidated">
      <div className="space-y-8">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleBack}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors"
              aria-label="Go back"
            >
              <ArrowLeft size={20} />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-white">Privacy Settings</h1>
              <p className="text-gray-400 mt-1">
                Control how your information is shared and displayed
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-3">
            {isDirty && (
              <button
                onClick={handleReset}
                disabled={saving}
                className="px-4 py-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors flex items-center space-x-2"
              >
                <RotateCcw size={16} />
                <span>Reset</span>
              </button>
            )}
            
            <button
              onClick={handleSave}
              disabled={saving || !isDirty || (validation !== null && !validation.isValid)}
              className="px-6 py-2 bg-accent-500 hover:bg-accent-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center space-x-2"
            >
              <Save size={16} />
              <span>{saving ? 'Saving...' : 'Save Changes'}</span>
            </button>
          </div>
        </div>

        {/* Validation Messages */}
        {validation && (validation.errors.length > 0 || validation.warnings.length > 0) && (
          <div className="space-y-3">
            {/* Errors */}
            {validation.errors.length > 0 && (
              <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="text-red-400" size={16} />
                  <span className="text-red-400 font-medium">Validation Errors</span>
                </div>
                <ul className="text-red-300 text-sm space-y-1">
                  {validation.errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Warnings */}
            {validation.warnings.length > 0 && (
              <div className="bg-yellow-900/20 border border-yellow-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="text-yellow-400" size={16} />
                  <span className="text-yellow-400 font-medium">Privacy Warnings</span>
                </div>
                <ul className="text-yellow-300 text-sm space-y-1">
                  {validation.warnings.map((warning, index) => (
                    <li key={index}>• {warning}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Privacy Level Presets */}
        {showPresets && settings && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <PrivacyLevelPresets
              currentSettings={settings}
              onPresetSelect={handlePresetSelect}
              onCustomize={() => setShowPresets(false)}
            />
          </motion.div>
        )}

        {/* Quick Actions */}
        {!showPresets && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6"
          >
            <div className="flex flex-wrap gap-3">
              <button
                onClick={() => setShowPresets(true)}
                className="px-4 py-2 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors text-sm"
              >
                Privacy Presets
              </button>
              <button
                onClick={() => setShowWizard(true)}
                className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors text-sm"
              >
                Setup Wizard
              </button>
            </div>
          </motion.div>
        )}

        {/* Privacy Settings Sections */}
        {!showPresets && (
          <>
            <PrivacySectionGroup
              title="Privacy & Visibility"
              description="Control who can see your profile and personal information"
            >
          {/* Profile Visibility Section */}
          <PrivacySection
            title="Profile Visibility"
            description="Control who can view your profile and personal information"
            icon={Eye}
          >
            <div className="space-y-6">
              {/* Profile Visibility Level */}
              <PrivacyRadioGroup
                name="profileVisibility"
                label="Profile Visibility"
                description="Choose who can view your profile"
                value={settings.profileVisibility}
                onChange={(value) => updateSetting('profileVisibility', value as any)}
                options={[
                  {
                    value: 'public',
                    label: 'Public',
                    description: 'Anyone can view your profile',
                    icon: Globe
                  },
                  {
                    value: 'friends',
                    label: 'Friends Only',
                    description: 'Only your friends can view your profile',
                    icon: Users
                  },
                  {
                    value: 'private',
                    label: 'Private',
                    description: 'Only you can view your profile',
                    icon: Lock
                  }
                ]}
              />

              {/* Personal Information Visibility */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-white">Personal Information</h4>
                
                <PrivacyToggle
                  id="showEmail"
                  label="Show Email Address"
                  description="Display your email address on your profile"
                  checked={settings.showEmail}
                  onChange={(checked) => updateSetting('showEmail', checked)}
                  variant="visibility"
                />

                <PrivacyToggle
                  id="showPhone"
                  label="Show Phone Number"
                  description="Display your phone number on your profile"
                  checked={settings.showPhone}
                  onChange={(checked) => updateSetting('showPhone', checked)}
                  variant="visibility"
                />

                <PrivacyToggle
                  id="showLocation"
                  label="Show Location"
                  description="Display your location on your profile"
                  checked={settings.showLocation}
                  onChange={(checked) => updateSetting('showLocation', checked)}
                  variant="visibility"
                />

                <PrivacyToggle
                  id="showBio"
                  label="Show Bio"
                  description="Display your bio on your profile"
                  checked={settings.showBio}
                  onChange={(checked) => updateSetting('showBio', checked)}
                  variant="visibility"
                />

                <PrivacyToggle
                  id="showSocialLinks"
                  label="Show Social Links"
                  description="Display your social media links on your profile"
                  checked={settings.showSocialLinks}
                  onChange={(checked) => updateSetting('showSocialLinks', checked)}
                  variant="visibility"
                />

                <PrivacyToggle
                  id="showWebsite"
                  label="Show Website"
                  description="Display your website on your profile"
                  checked={settings.showWebsite}
                  onChange={(checked) => updateSetting('showWebsite', checked)}
                  variant="visibility"
                />
              </div>
            </div>
          </PrivacySection>

          {/* Activity & Social Section */}
          <PrivacySection
            title="Activity & Social"
            description="Control what activity information is visible to others"
            icon={Users}
          >
            <div className="space-y-4">
              <PrivacyToggle
                id="showActivityStatus"
                label="Show Activity Status"
                description="Let others see when you're active on the platform"
                checked={settings.showActivityStatus}
                onChange={(checked) => updateSetting('showActivityStatus', checked)}
                variant="visibility"
              />

              <PrivacyToggle
                id="showLastSeen"
                label="Show Last Seen"
                description="Display when you were last online"
                checked={settings.showLastSeen}
                onChange={(checked) => updateSetting('showLastSeen', checked)}
                variant="visibility"
              />

              <PrivacyToggle
                id="showOnlineStatus"
                label="Show Online Status"
                description="Display whether you're currently online"
                checked={settings.showOnlineStatus}
                onChange={(checked) => updateSetting('showOnlineStatus', checked)}
                variant="visibility"
              />

              <PrivacyToggle
                id="showAchievements"
                label="Show Achievements"
                description="Display your achievements and badges"
                checked={settings.showAchievements}
                onChange={(checked) => updateSetting('showAchievements', checked)}
                variant="visibility"
              />

              <PrivacyToggle
                id="showPoints"
                label="Show Points"
                description="Display your points and tier status"
                checked={settings.showPoints}
                onChange={(checked) => updateSetting('showPoints', checked)}
                variant="visibility"
              />

              <PrivacyToggle
                id="showOrderHistory"
                label="Show Order History"
                description="Allow others to see your purchase history"
                checked={settings.showOrderHistory}
                onChange={(checked) => updateSetting('showOrderHistory', checked)}
                variant="visibility"
              />

              <PrivacyToggle
                id="showWishlist"
                label="Show Wishlist"
                description="Make your wishlist visible to others"
                checked={settings.showWishlist}
                onChange={(checked) => updateSetting('showWishlist', checked)}
                variant="visibility"
              />
            </div>
          </PrivacySection>
        </PrivacySectionGroup>

        {/* Communication Settings */}
        <PrivacySectionGroup
          title="Communication & Interactions"
          description="Control how others can communicate with you"
        >
          <PrivacySection
            title="Communication Preferences"
            description="Manage who can contact you and how"
            icon={Bell}
          >
            <div className="space-y-6">
              {/* Direct Messages */}
              <PrivacyRadioGroup
                name="allowDirectMessages"
                label="Direct Messages"
                description="Choose who can send you direct messages"
                value={settings.allowDirectMessages}
                onChange={(value) => updateSetting('allowDirectMessages', value as any)}
                options={[
                  {
                    value: 'everyone',
                    label: 'Everyone',
                    description: 'Anyone can send you messages',
                    icon: Globe
                  },
                  {
                    value: 'friends',
                    label: 'Friends Only',
                    description: 'Only your friends can message you',
                    icon: Users
                  },
                  {
                    value: 'nobody',
                    label: 'Nobody',
                    description: 'Disable direct messages',
                    icon: Lock
                  }
                ]}
              />

              {/* Other Communication Settings */}
              <div className="space-y-4">
                <PrivacyToggle
                  id="allowFriendRequests"
                  label="Allow Friend Requests"
                  description="Let others send you friend requests"
                  checked={settings.allowFriendRequests}
                  onChange={(checked) => updateSetting('allowFriendRequests', checked)}
                  variant="default"
                />

                <PrivacyToggle
                  id="allowMentions"
                  label="Allow Mentions"
                  description="Let others mention you in posts and comments"
                  checked={settings.allowMentions}
                  onChange={(checked) => updateSetting('allowMentions', checked)}
                  variant="default"
                />

                <PrivacyToggle
                  id="allowTagging"
                  label="Allow Tagging"
                  description="Let others tag you in photos and posts"
                  checked={settings.allowTagging}
                  onChange={(checked) => updateSetting('allowTagging', checked)}
                  variant="default"
                />
              </div>
            </div>
          </PrivacySection>
        </PrivacySectionGroup>

        {/* Data & Analytics Settings */}
        <PrivacySectionGroup
          title="Data & Analytics"
          description="Control how your data is used for analytics and personalization"
        >
          <PrivacySection
            title="Data Sharing & Analytics"
            description="Manage how your data is used and shared"
            icon={Database}
          >
            <div className="space-y-4">
              <PrivacyToggle
                id="allowDataAnalytics"
                label="Allow Data Analytics"
                description="Help us improve our services by analyzing usage patterns"
                checked={settings.allowDataAnalytics}
                onChange={(checked) => updateSetting('allowDataAnalytics', checked)}
                variant="sharing"
              />

              <PrivacyToggle
                id="allowPersonalization"
                label="Allow Personalization"
                description="Use your data to personalize your experience"
                checked={settings.allowPersonalization}
                onChange={(checked) => updateSetting('allowPersonalization', checked)}
                variant="sharing"
              />

              <PrivacyToggle
                id="allowMarketingAnalytics"
                label="Allow Marketing Analytics"
                description="Use your data for marketing insights and campaigns"
                checked={settings.allowMarketingAnalytics}
                onChange={(checked) => updateSetting('allowMarketingAnalytics', checked)}
                variant="sharing"
              />

              <PrivacyToggle
                id="allowThirdPartySharing"
                label="Allow Third-Party Sharing"
                description="Share anonymized data with trusted partners"
                checked={settings.allowThirdPartySharing}
                onChange={(checked) => updateSetting('allowThirdPartySharing', checked)}
                variant="sharing"
              />

              <PrivacyToggle
                id="allowCookieTracking"
                label="Allow Cookie Tracking"
                description="Use cookies to track your preferences and activity"
                checked={settings.allowCookieTracking}
                onChange={(checked) => updateSetting('allowCookieTracking', checked)}
                variant="sharing"
              />
            </div>
          </PrivacySection>

          {/* Search & Discovery Section */}
          <PrivacySection
            title="Search & Discovery"
            description="Control how others can find and discover your profile"
            icon={Globe}
          >
            <div className="space-y-4">
              <PrivacyToggle
                id="allowSearchEngineIndexing"
                label="Allow Search Engine Indexing"
                description="Let search engines index your public profile"
                checked={settings.allowSearchEngineIndexing}
                onChange={(checked) => updateSetting('allowSearchEngineIndexing', checked)}
                variant="visibility"
              />

              <PrivacyToggle
                id="allowProfileDiscovery"
                label="Allow Profile Discovery"
                description="Let others discover your profile through recommendations"
                checked={settings.allowProfileDiscovery}
                onChange={(checked) => updateSetting('allowProfileDiscovery', checked)}
                variant="visibility"
              />

              <PrivacyToggle
                id="showInMemberDirectory"
                label="Show in Member Directory"
                description="Include your profile in the member directory"
                checked={settings.showInMemberDirectory}
                onChange={(checked) => updateSetting('showInMemberDirectory', checked)}
                variant="visibility"
              />

              <PrivacyToggle
                id="showInLeaderboards"
                label="Show in Leaderboards"
                description="Display your name and stats in leaderboards"
                checked={settings.showInLeaderboards}
                onChange={(checked) => updateSetting('showInLeaderboards', checked)}
                variant="visibility"
              />
            </div>
          </PrivacySection>
        </PrivacySectionGroup>

        {/* Security Settings */}
        <PrivacySectionGroup
          title="Security & Account"
          description="Advanced security and account management settings"
        >
          <PrivacySection
            title="Account Security"
            description="Enhanced security settings for your account"
            icon={Lock}
          >
            <div className="space-y-4">
              <PrivacyToggle
                id="requireTwoFactorForSensitiveActions"
                label="Require 2FA for Sensitive Actions"
                description="Require two-factor authentication for sensitive account changes"
                checked={settings.requireTwoFactorForSensitiveActions}
                onChange={(checked) => updateSetting('requireTwoFactorForSensitiveActions', checked)}
                variant="security"
              />

              <PrivacyToggle
                id="allowRememberDevice"
                label="Allow Remember Device"
                description="Allow the system to remember trusted devices"
                checked={settings.allowRememberDevice}
                onChange={(checked) => updateSetting('allowRememberDevice', checked)}
                variant="security"
              />

              <PrivacyToggle
                id="allowMultipleDeviceLogin"
                label="Allow Multiple Device Login"
                description="Allow logging in from multiple devices simultaneously"
                checked={settings.allowMultipleDeviceLogin}
                onChange={(checked) => updateSetting('allowMultipleDeviceLogin', checked)}
                variant="security"
              />
            </div>
          </PrivacySection>
        </PrivacySectionGroup>
          </>
        )}

        {/* Privacy Setup Wizard */}
        <PrivacySetupWizard
          isOpen={showWizard}
          onClose={() => setShowWizard(false)}
          onComplete={handleWizardComplete}
          onSkip={() => {
            setShowWizard(false)
            setShowPresets(false)
          }}
          currentSettings={settings}
        />
      </div>
    </ProfileLayout>
  )
}
