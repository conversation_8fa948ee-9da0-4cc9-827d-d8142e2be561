'use client'

/**
 * Profile Addresses Page - DEPRECATED
 *
 * This page has been merged with personal information into a unified
 * Contact Information page. This component now redirects users
 * to the new unified interface.
 *
 * @deprecated Use /profile/contact instead
 * @returns JSX.Element - Redirect component
 */

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import ProfileLayout from '@/components/profile/ProfileLayout'

export default function AddressesPageRedirect() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the new unified contact page
    router.replace('/profile/contact')
  }, [router])

  return (
    <ProfileLayout navigation="consolidated">
      <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
        <div className="text-center">
          <h2 className="text-xl font-bold text-white mb-4">Redirecting...</h2>
          <p className="text-gray-400 mb-4">
            Address management has been moved to the unified Contact Information page.
          </p>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500 mx-auto"></div>
        </div>
      </div>
    </ProfileLayout>
  )
}


