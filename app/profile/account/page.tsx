'use client'

import React, { useState, useEffect, useMemo } from 'react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import TabContainer, { TabPanel } from '@/components/ui/TabContainer'
import OverviewTab from '@/components/profile/tabs/OverviewTab'
import ProfileCompletionTab from '@/components/profile/tabs/ProfileCompletionTab'
import MembershipTab from '@/components/profile/tabs/MembershipTab'
import SettingsTab from '@/components/profile/tabs/SettingsTab'
import { useUser } from '@/lib/useUser'
import { ProfileCompletionManager } from '@/lib/profileCompletion'
import {
  Home,
  Target,
  Crown,
  Settings,
  AlertCircle
} from 'lucide-react'

export default function AccountDetailsPage() {
  const { user, profile, loading } = useUser()
  const [activeTab, setActiveTab] = useState('overview')
  const [showOnboarding, setShowOnboarding] = useState(false)

  /**
   * Check if user should see onboarding
   */
  useEffect(() => {
    if (profile) {
      // Check if user should see onboarding wizard
      const shouldShow = ProfileCompletionManager.shouldShowOnboarding(profile)
      setShowOnboarding(shouldShow)

      // If onboarding is needed, switch to completion tab
      if (shouldShow) {
        setActiveTab('completion')
      }
    }
  }, [profile])

  /**
   * Handle onboarding completion
   */
  const handleOnboardingComplete = () => {
    setShowOnboarding(false)
    setActiveTab('overview')
  }

  // Memoize tabs array to prevent recreation on every render
  const tabs = useMemo(() => [
    {
      id: 'overview',
      label: 'Overview',
      icon: Home,
      description: 'Account summary and activity dashboard'
    },
    {
      id: 'completion',
      label: 'Profile Setup',
      icon: Target,
      badge: showOnboarding ? 'New' : undefined,
      description: 'Complete your profile for better experience'
    },
    {
      id: 'membership',
      label: 'Membership',
      icon: Crown,
      description: 'Tier benefits and rewards'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      description: 'Privacy, security, and preferences'
    }
  ], [showOnboarding])

  // Show loading state while authentication is in progress
  if (loading) {
    return (
      <ProfileLayout>
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading account details...</p>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  // Handle unauthenticated users after loading is complete
  if (!user && !loading) {
    // Redirect to auth with current page as redirect target
    if (typeof window !== 'undefined') {
      window.location.href = `/auth?redirect=/profile/account`
    }
    return (
      <ProfileLayout>
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Redirecting to authentication...</p>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  // Show profile loading if user exists but profile is still loading
  if (!profile) {
    return (
      <ProfileLayout>
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading profile information...</p>
          </div>
        </div>
      </ProfileLayout>
    )
  }





  return (
    <ProfileLayout navigation="consolidated">
      <div className="space-y-6">
        {/* Show onboarding alert if needed */}
        {showOnboarding && activeTab !== 'completion' && (
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <AlertCircle size={20} className="text-yellow-400" />
              <div>
                <h4 className="text-yellow-400 font-medium">Complete Your Profile</h4>
                <p className="text-gray-400 text-sm">
                  Finish setting up your profile to unlock all features and earn points.
                </p>
              </div>
              <button
                onClick={() => setActiveTab('completion')}
                className="px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-black rounded-lg transition-colors"
              >
                Complete Now
              </button>
            </div>
          </div>
        )}

        {/* Tabbed Interface */}
        <TabContainer
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          variant="underline"
          size="md"
          persistState={true}
          storageKey="syndicaps-account-tab"
        >
          <TabPanel id="overview">
            <OverviewTab profile={profile} />
          </TabPanel>

          <TabPanel id="completion">
            <ProfileCompletionTab profile={profile} />
          </TabPanel>

          <TabPanel id="membership">
            <MembershipTab profile={profile} />
          </TabPanel>

          <TabPanel id="settings">
            <SettingsTab profile={profile} />
          </TabPanel>
        </TabContainer>
      </div>
    </ProfileLayout>
  )
}
