'use client'

/**
 * Profile Gamification Page
 * 
 * Comprehensive gamification interface showing achievements,
 * progress tracking, level progression, and reward systems.
 * 
 * Features:
 * - Level progression and XP tracking
 * - Achievement system with rarities
 * - Streak tracking and rewards
 * - Profile completion gamification
 * - Interactive reward notifications
 * 
 * <AUTHOR> Team
 */

import React from 'react'
import { motion } from 'framer-motion'
import {
  Trophy,
  Crown,
  Target,
  Star,
  Award,
  Gamepad2
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import GamificationDashboard from '@/components/profile/gamification/GamificationDashboard'
import { useUser } from '@/lib/useUser'

export default function ProfileGamificationPage() {
  const { user, profile } = useUser()

  if (!user || !profile) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <Gamepad2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">Please log in</h3>
            <p className="text-gray-400">You need to be logged in to view your gamification progress.</p>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout navigation="consolidated">
      <div className="space-y-6">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white flex items-center space-x-3">
                <Trophy size={28} />
                <span>Gamification</span>
              </h1>
              <p className="text-gray-400 mt-2">
                Track your progress, unlock achievements, and level up your Syndicaps experience
              </p>
            </div>
            <div className="hidden md:flex items-center space-x-6">
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-yellow-600 rounded-full mb-2">
                  <Crown size={24} className="text-white" />
                </div>
                <div className="text-sm text-gray-400">Level Up</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-purple-600 rounded-full mb-2">
                  <Star size={24} className="text-white" />
                </div>
                <div className="text-sm text-gray-400">Earn XP</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-full mb-2">
                  <Target size={24} className="text-white" />
                </div>
                <div className="text-sm text-gray-400">Complete Goals</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Gamification Features Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6"
        >
          <div className="bg-gradient-to-br from-yellow-600 to-orange-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <Crown size={24} />
              <div className="text-right">
                <div className="text-2xl font-bold">Levels</div>
                <div className="text-yellow-100 text-sm">Progress & Ranking</div>
              </div>
            </div>
            <p className="text-yellow-100 text-sm">
              Gain XP and level up by completing profile sections and engaging with the community.
            </p>
          </div>

          <div className="bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <Trophy size={24} />
              <div className="text-right">
                <div className="text-2xl font-bold">Achievements</div>
                <div className="text-purple-100 text-sm">Unlock & Collect</div>
              </div>
            </div>
            <p className="text-purple-100 text-sm">
              Unlock achievements by completing various tasks and milestones throughout your journey.
            </p>
          </div>

          <div className="bg-gradient-to-br from-blue-600 to-cyan-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <Target size={24} />
              <div className="text-right">
                <div className="text-2xl font-bold">Goals</div>
                <div className="text-blue-100 text-sm">Track & Complete</div>
              </div>
            </div>
            <p className="text-blue-100 text-sm">
              Set and complete profile goals to earn rewards and improve your community standing.
            </p>
          </div>

          <div className="bg-gradient-to-br from-green-600 to-emerald-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <Award size={24} />
              <div className="text-right">
                <div className="text-2xl font-bold">Rewards</div>
                <div className="text-green-100 text-sm">Earn & Redeem</div>
              </div>
            </div>
            <p className="text-green-100 text-sm">
              Earn points and unlock exclusive benefits as you progress through the gamification system.
            </p>
          </div>
        </motion.div>

        {/* Main Gamification Dashboard */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <GamificationDashboard profile={profile} />
        </motion.div>

        {/* How It Works */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <h2 className="text-xl font-semibold text-white mb-6">How Gamification Works</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  1
                </div>
                <h3 className="text-white font-medium">Complete Your Profile</h3>
              </div>
              <p className="text-gray-400 text-sm ml-11">
                Fill out different sections of your profile to earn XP and unlock achievements. 
                Each section completed brings you closer to the next level.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  2
                </div>
                <h3 className="text-white font-medium">Engage with Community</h3>
              </div>
              <p className="text-gray-400 text-sm ml-11">
                Participate in community activities, share content, and connect with other members 
                to earn additional XP and unlock social achievements.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  3
                </div>
                <h3 className="text-white font-medium">Unlock Rewards</h3>
              </div>
              <p className="text-gray-400 text-sm ml-11">
                As you level up and earn achievements, unlock exclusive features, badges, 
                and special recognition within the community.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Achievement Rarities Guide */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <h2 className="text-xl font-semibold text-white mb-6">Achievement Rarities</h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-gray-700 rounded-lg">
              <div className="w-12 h-12 bg-gray-600 rounded-full mx-auto mb-3 flex items-center justify-center">
                <Star size={20} className="text-gray-300" />
              </div>
              <div className="text-gray-300 font-medium mb-1">Common</div>
              <div className="text-xs text-gray-400">Easy to unlock</div>
              <div className="text-xs text-gray-500 mt-1">50-100 XP</div>
            </div>

            <div className="text-center p-4 bg-gray-700 rounded-lg">
              <div className="w-12 h-12 bg-blue-600 rounded-full mx-auto mb-3 flex items-center justify-center">
                <Star size={20} className="text-white" />
              </div>
              <div className="text-blue-400 font-medium mb-1">Rare</div>
              <div className="text-xs text-gray-400">Moderate effort</div>
              <div className="text-xs text-gray-500 mt-1">150-300 XP</div>
            </div>

            <div className="text-center p-4 bg-gray-700 rounded-lg">
              <div className="w-12 h-12 bg-purple-600 rounded-full mx-auto mb-3 flex items-center justify-center">
                <Star size={20} className="text-white" />
              </div>
              <div className="text-purple-400 font-medium mb-1">Epic</div>
              <div className="text-xs text-gray-400">Significant achievement</div>
              <div className="text-xs text-gray-500 mt-1">400-750 XP</div>
            </div>

            <div className="text-center p-4 bg-gray-700 rounded-lg">
              <div className="w-12 h-12 bg-yellow-600 rounded-full mx-auto mb-3 flex items-center justify-center">
                <Crown size={20} className="text-white" />
              </div>
              <div className="text-yellow-400 font-medium mb-1">Legendary</div>
              <div className="text-xs text-gray-400">Elite status</div>
              <div className="text-xs text-gray-500 mt-1">1000+ XP</div>
            </div>
          </div>
        </motion.div>
      </div>
    </ProfileLayout>
  )
}