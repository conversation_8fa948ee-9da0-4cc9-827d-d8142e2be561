'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Settings, Bell, Mail, Smartphone, Globe, Palette, Moon, Sun, Save, Check } from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import SettingsBackupRestore from '@/components/profile/SettingsBackupRestore'
import { useUser } from '@/lib/useUser'
import ModalPreferences from '@/components/profile/settings/ModalPreferences'

export default function PreferencesPage() {
  const { user } = useUser()
  const [saved, setSaved] = useState(false)
  
  // Notification preferences
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    smsNotifications: false,
    raffleNotifications: true,
    orderUpdates: true,
    marketingEmails: false,
    newsletter: true,
    promotionalOffers: true,
    productUpdates: false
  })

  // App preferences
  const [appPreferences, setAppPreferences] = useState({
    language: 'en',
    timezone: 'America/New_York',
    currency: 'USD',
    theme: 'dark',
    emailFrequency: 'weekly',
    autoSave: true
  })

  // Privacy preferences
  const [privacy, setPrivacy] = useState({
    profileVisibility: 'public',
    showPurchaseHistory: false,
    showPointsBalance: true,
    allowDataCollection: true,
    shareWithPartners: false
  })

  if (!user) {
    return (
      <ProfileLayout>
        <div className="text-center py-12">
          <p className="text-gray-400">Please log in to manage your preferences.</p>
        </div>
      </ProfileLayout>
    )
  }

  const handleSave = async () => {
    // Here you would save to Firebase
    setSaved(true)
    setTimeout(() => setSaved(false), 3000)
  }

  const toggleNotification = (key: string) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }))
  }

  const updateAppPreference = (key: string, value: string | boolean) => {
    setAppPreferences(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const updatePrivacy = (key: string, value: string | boolean) => {
    setPrivacy(prev => ({
      ...prev,
      [key]: value
    }))
  }

  return (
    <ProfileLayout navigation="consolidated">
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Settings className="text-accent-400" size={28} />
              <div>
                <h1 className="text-2xl font-bold text-white">Preferences</h1>
                <p className="text-gray-400">Customize your ArtisanCaps experience</p>
              </div>
            </div>
            
            <button
              onClick={handleSave}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-all ${
                saved 
                  ? 'bg-green-600 text-white' 
                  : 'bg-accent-600 hover:bg-accent-700 text-white'
              }`}
            >
              {saved ? <Check size={18} /> : <Save size={18} />}
              <span>{saved ? 'Saved!' : 'Save Changes'}</span>
            </button>
          </div>
        </motion.div>

        {/* Notification Preferences */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6"
        >
          <div className="flex items-center space-x-3 mb-6">
            <Bell className="text-accent-400" size={24} />
            <h2 className="text-xl font-bold text-white">Notification Preferences</h2>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-semibold text-white flex items-center space-x-2">
                <Mail size={18} className="text-accent-400" />
                <span>Email Notifications</span>
              </h3>
              
              {[
                { key: 'emailNotifications', label: 'Email notifications', desc: 'Receive notifications via email' },
                { key: 'orderUpdates', label: 'Order updates', desc: 'Shipping and delivery notifications' },
                { key: 'raffleNotifications', label: 'Raffle notifications', desc: 'Raffle results and announcements' },
                { key: 'newsletter', label: 'Newsletter', desc: 'Weekly newsletter with updates' }
              ].map(({ key, label, desc }) => (
                <div key={key} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                  <div>
                    <p className="text-white font-medium">{label}</p>
                    <p className="text-gray-400 text-sm">{desc}</p>
                  </div>
                  <button
                    onClick={() => toggleNotification(key)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      notifications[key as keyof typeof notifications] ? 'bg-accent-600' : 'bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        notifications[key as keyof typeof notifications] ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-white flex items-center space-x-2">
                <Smartphone size={18} className="text-accent-400" />
                <span>Marketing & Promotions</span>
              </h3>
              
              {[
                { key: 'marketingEmails', label: 'Marketing emails', desc: 'Promotional offers and deals' },
                { key: 'promotionalOffers', label: 'Promotional offers', desc: 'Special discounts and sales' },
                { key: 'productUpdates', label: 'Product updates', desc: 'New product announcements' },
                { key: 'smsNotifications', label: 'SMS notifications', desc: 'Text message notifications' }
              ].map(({ key, label, desc }) => (
                <div key={key} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                  <div>
                    <p className="text-white font-medium">{label}</p>
                    <p className="text-gray-400 text-sm">{desc}</p>
                  </div>
                  <button
                    onClick={() => toggleNotification(key)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      notifications[key as keyof typeof notifications] ? 'bg-accent-600' : 'bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        notifications[key as keyof typeof notifications] ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </motion.section>

        {/* App Preferences */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6"
        >
          <div className="flex items-center space-x-3 mb-6">
            <Globe className="text-accent-400" size={24} />
            <h2 className="text-xl font-bold text-white">App Preferences</h2>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-white font-medium mb-2">Language</label>
                <select
                  value={appPreferences.language}
                  onChange={(e) => updateAppPreference('language', e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                >
                  <option value="en">English</option>
                  <option value="es">Español</option>
                  <option value="fr">Français</option>
                  <option value="de">Deutsch</option>
                  <option value="ja">日本語</option>
                </select>
              </div>

              <div>
                <label className="block text-white font-medium mb-2">Timezone</label>
                <select
                  value={appPreferences.timezone}
                  onChange={(e) => updateAppPreference('timezone', e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                >
                  <option value="America/New_York">Eastern Time (ET)</option>
                  <option value="America/Chicago">Central Time (CT)</option>
                  <option value="America/Denver">Mountain Time (MT)</option>
                  <option value="America/Los_Angeles">Pacific Time (PT)</option>
                  <option value="Europe/London">London (GMT)</option>
                  <option value="Europe/Paris">Paris (CET)</option>
                  <option value="Asia/Tokyo">Tokyo (JST)</option>
                </select>
              </div>

              <div>
                <label className="block text-white font-medium mb-2">Currency</label>
                <select
                  value={appPreferences.currency}
                  onChange={(e) => updateAppPreference('currency', e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                >
                  <option value="USD">USD ($)</option>
                  <option value="EUR">EUR (€)</option>
                  <option value="GBP">GBP (£)</option>
                  <option value="JPY">JPY (¥)</option>
                  <option value="CAD">CAD ($)</option>
                </select>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-white font-medium mb-2">Theme</label>
                <div className="flex space-x-3">
                  <button
                    onClick={() => updateAppPreference('theme', 'dark')}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                      appPreferences.theme === 'dark' 
                        ? 'bg-accent-600 text-white' 
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    <Moon size={16} />
                    <span>Dark</span>
                  </button>
                  <button
                    onClick={() => updateAppPreference('theme', 'light')}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                      appPreferences.theme === 'light' 
                        ? 'bg-accent-600 text-white' 
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    <Sun size={16} />
                    <span>Light</span>
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-white font-medium mb-2">Email Frequency</label>
                <select
                  value={appPreferences.emailFrequency}
                  onChange={(e) => updateAppPreference('emailFrequency', e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500"
                >
                  <option value="immediate">Immediate</option>
                  <option value="daily">Daily digest</option>
                  <option value="weekly">Weekly digest</option>
                  <option value="monthly">Monthly digest</option>
                </select>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                <div>
                  <p className="text-white font-medium">Auto-save preferences</p>
                  <p className="text-gray-400 text-sm">Automatically save changes</p>
                </div>
                <button
                  onClick={() => updateAppPreference('autoSave', !appPreferences.autoSave)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    appPreferences.autoSave ? 'bg-accent-600' : 'bg-gray-600'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      appPreferences.autoSave ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </motion.section>

        {/* Privacy Settings */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6"
        >
          <div className="flex items-center space-x-3 mb-6">
            <Palette className="text-accent-400" size={24} />
            <h2 className="text-xl font-bold text-white">Privacy Settings</h2>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-white font-medium mb-2">Profile Visibility</label>
              <select
                value={privacy.profileVisibility}
                onChange={(e) => updatePrivacy('profileVisibility', e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500"
              >
                <option value="public">Public</option>
                <option value="friends">Friends only</option>
                <option value="private">Private</option>
              </select>
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              {[
                { key: 'showPurchaseHistory', label: 'Show purchase history', desc: 'Allow others to see your purchases' },
                { key: 'showPointsBalance', label: 'Show points balance', desc: 'Display your points on profile' },
                { key: 'allowDataCollection', label: 'Allow data collection', desc: 'Help improve our services' },
                { key: 'shareWithPartners', label: 'Share with partners', desc: 'Share data with trusted partners' }
              ].map(({ key, label, desc }) => (
                <div key={key} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                  <div>
                    <p className="text-white font-medium">{label}</p>
                    <p className="text-gray-400 text-sm">{desc}</p>
                  </div>
                  <button
                    onClick={() => updatePrivacy(key, !privacy[key as keyof typeof privacy])}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      privacy[key as keyof typeof privacy] ? 'bg-accent-600' : 'bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        privacy[key as keyof typeof privacy] ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </motion.section>

        {/* Modal Preferences */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <ModalPreferences />
        </motion.section>

        {/* Settings Backup & Restore */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <SettingsBackupRestore
            profile={user}
            onBackup={async (settings) => {
              // Implement backup functionality
              console.log('Backing up settings:', settings)
              // In a real app, this would save to a backend service
              localStorage.setItem('syndicaps-backup', JSON.stringify({
                timestamp: new Date().toISOString(),
                settings
              }))
            }}
            onRestore={async (settings) => {
              // Implement restore functionality
              console.log('Restoring settings:', settings)

              // Apply settings to current state
              if (settings.privacy) {
                setPrivacy(prev => ({ ...prev, ...settings.privacy }))
              }
              if (settings.preferences) {
                if (settings.preferences.notifications) {
                  setNotifications(prev => ({ ...prev, ...settings.preferences.notifications }))
                }
                // Note: Interface and accessibility settings would be handled by their respective state managers
                console.log('Additional preferences restored:', settings.preferences)
              }

              // Show success message
              setSaved(true)
              setTimeout(() => setSaved(false), 3000)
            }}
          />
        </motion.section>
      </div>
    </ProfileLayout>
  )
}
