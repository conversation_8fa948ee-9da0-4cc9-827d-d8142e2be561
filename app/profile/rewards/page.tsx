/**
 * User Reward Purchases Page (App Router)
 * 
 * Displays user's reward purchase history and status tracking.
 * Shows purchased rewards, delivery status, and fulfillment information.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { Gift, Package, Clock, CheckCircle, XCircle, Truck, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { useUser } from '@/lib/useUser'
import { getUserRewardPurchases, RewardPurchase } from '@/lib/api/rewards'
import { formatDistanceToNow } from 'date-fns'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { PageHeader } from '@/components/navigation/EnhancedBreadcrumbs'

/**
 * User Reward Purchases Page Component
 */
export default function RewardPurchasesPage() {
  const router = useRouter()
  const { user, loading: userLoading } = useUser()
  const [purchases, setPurchases] = useState<RewardPurchase[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!userLoading && !user) {
      router.push('/auth?redirect=/profile/rewards')
    }
  }, [user, userLoading, router])

  // Fetch user's reward purchases
  useEffect(() => {
    const fetchPurchases = async () => {
      if (!user) return
      
      setLoading(true)
      try {
        const userPurchases = await getUserRewardPurchases(user.uid)
        setPurchases(userPurchases)
      } catch (error) {
        console.error('Error fetching reward purchases:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchPurchases()
  }, [user])

  const statusOptions = [
    { id: 'all', name: 'All', icon: Gift },
    { id: 'pending', name: 'Pending', icon: Clock },
    { id: 'processing', name: 'Processing', icon: Package },
    { id: 'fulfilled', name: 'Fulfilled', icon: CheckCircle },
    { id: 'cancelled', name: 'Cancelled', icon: XCircle }
  ]

  const filteredPurchases = selectedStatus === 'all' 
    ? purchases 
    : purchases.filter(purchase => purchase.status === selectedStatus)

  const getStatusColor = (status: RewardPurchase['status']) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20'
      case 'processing':
        return 'text-blue-400 bg-blue-400/10 border-blue-400/20'
      case 'fulfilled':
        return 'text-green-400 bg-green-400/10 border-green-400/20'
      case 'cancelled':
        return 'text-red-400 bg-red-400/10 border-red-400/20'
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20'
    }
  }

  const getStatusIcon = (status: RewardPurchase['status']) => {
    switch (status) {
      case 'pending':
        return Clock
      case 'processing':
        return Package
      case 'fulfilled':
        return CheckCircle
      case 'cancelled':
        return XCircle
      default:
        return Gift
    }
  }

  if (userLoading || loading) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="text-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your reward purchases...</p>
        </div>
      </ProfileLayout>
    )
  }

  if (!user) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="text-center py-12">
          <p className="text-gray-400">Please log in to view your reward purchases.</p>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout navigation="consolidated">
      <div className="space-y-8">
        {/* Page Header */}
        <PageHeader
          title="Reward Purchases"
          description="Track your reward purchases and delivery status"
          actions={
            <Link
              href="/profile/points?tab=rewards"
              className="btn-neon inline-flex items-center space-x-2"
            >
              <Gift size={16} />
              <span>Browse Rewards</span>
            </Link>
          }
        />

        {/* Status Filter */}
        <div className="flex flex-wrap gap-2">
          {statusOptions.map((status) => (
            <button
              key={status.id}
              onClick={() => setSelectedStatus(status.id)}
              className={`
                flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200
                ${selectedStatus === status.id
                  ? 'bg-purple-500 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }
              `}
            >
              <status.icon size={16} />
              <span>{status.name}</span>
            </button>
          ))}
        </div>

        {/* Purchases List */}
        {filteredPurchases.length > 0 ? (
          <div className="space-y-4">
            {filteredPurchases.map((purchase) => {
              const StatusIcon = getStatusIcon(purchase.status)
              
              return (
                <motion.div
                  key={purchase.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gray-800 rounded-lg p-6"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                        <Gift className="w-6 h-6 text-purple-400" />
                      </div>
                      <div>
                        <h3 className="text-white font-semibold">{purchase.rewardName}</h3>
                        <p className="text-gray-400 text-sm">
                          Purchased {formatDistanceToNow(purchase.purchaseDate.toDate())} ago
                        </p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className={`
                        inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium border
                        ${getStatusColor(purchase.status)}
                      `}>
                        <StatusIcon size={14} />
                        <span className="capitalize">{purchase.status}</span>
                      </div>
                      <p className="text-purple-400 text-sm mt-1">
                        {purchase.pointsCost} points
                      </p>
                    </div>
                  </div>

                  {/* Delivery Information */}
                  {purchase.deliveryInfo && (
                    <div className="bg-gray-800 rounded-lg p-4 mb-4">
                      <h4 className="text-white font-medium mb-2 flex items-center">
                        <Truck className="w-4 h-4 mr-2" />
                        Delivery Information
                      </h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Method:</span>
                          <span className="text-white">{purchase.deliveryInfo.method}</span>
                        </div>
                        {purchase.deliveryInfo.trackingNumber && (
                          <div className="flex justify-between">
                            <span className="text-gray-400">Tracking:</span>
                            <span className="text-white font-mono">
                              {purchase.deliveryInfo.trackingNumber}
                            </span>
                          </div>
                        )}
                        {purchase.deliveryInfo.estimatedDelivery && (
                          <div className="flex justify-between">
                            <span className="text-gray-400">Estimated Delivery:</span>
                            <span className="text-white">
                              {purchase.deliveryInfo.estimatedDelivery}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Fulfillment Date */}
                  {purchase.fulfillmentDate && (
                    <p className="text-green-400 text-sm">
                      ✓ Fulfilled on {purchase.fulfillmentDate.toDate().toLocaleDateString()}
                    </p>
                  )}

                  {/* Purchase ID */}
                  <p className="text-gray-500 text-xs mt-2">
                    Purchase ID: {purchase.id}
                  </p>
                </motion.div>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-16">
            <Gift size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              {selectedStatus === 'all' 
                ? 'No reward purchases yet' 
                : `No ${selectedStatus} purchases`
              }
            </h3>
            <p className="text-gray-400 mb-8">
              {selectedStatus === 'all'
                ? 'Visit the reward shop to spend your points on exclusive items'
                : `You don't have any ${selectedStatus} purchases`
              }
            </p>
            {selectedStatus === 'all' && (
              <Link
                href="/profile/points?tab=rewards"
                className="bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center space-x-2"
              >
                <Gift size={16} />
                <span>Browse Rewards</span>
              </Link>
            )}
          </div>
        )}
      </div>
    </ProfileLayout>
  )
}
