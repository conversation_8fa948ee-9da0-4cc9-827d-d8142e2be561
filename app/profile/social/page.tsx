/**
 * Social Profile Page
 * 
 * Main social profile page showing user's social presence, connections,
 * and community activity within the Syndicaps platform.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import {
  Users,
  Heart,
  MessageCircle,
  Share2,
  Trophy,
  Star,
  Settings,
  Eye,
  EyeOff,
  Link as LinkIcon,
  Calendar,
  Activity
} from 'lucide-react'
import Link from 'next/link'
import { useUser } from '@/lib/useUser'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { PageHeader } from '@/components/navigation/EnhancedBreadcrumbs'
import SocialProfileHeader from '@/components/profile/social/SocialProfileHeader'
import SocialStats from '@/components/profile/social/SocialStats'
import SocialActivityTimeline from '@/components/profile/social/SocialActivityTimeline'
import CommunityConnections from '@/components/profile/social/CommunityConnections'
import SocialSharing from '@/components/profile/social/SocialSharing'
import ProfileVisibilitySettings from '@/components/profile/social/ProfileVisibilitySettings'

/**
 * Social Profile Page Component
 */
export default function SocialProfilePage() {
  const router = useRouter()
  const { user, profile, loading } = useUser()
  const [isPublic, setIsPublic] = useState(true)
  const [showVisibilitySettings, setShowVisibilitySettings] = useState(false)
  const [activeTab, setActiveTab] = useState<'timeline' | 'connections' | 'sharing' | 'settings'>('timeline')
  const [notification, setNotification] = useState<{ message: string; type: 'success' | 'info' | 'error' } | null>(null)
  const [socialStats, setSocialStats] = useState({
    followers: 127,
    following: 89,
    posts: 45,
    likes: 342,
    shares: 23,
    comments: 156
  })

  // Handle authentication state
  useEffect(() => {
    if (!loading && !user) {
      // Don't redirect immediately, just set a flag to show login message
      console.log('User not authenticated for social profile')
    }
  }, [user, loading, router])

  // Load social profile settings
  useEffect(() => {
    if (profile) {
      setIsPublic(profile.privacy?.profileVisibility === 'public')
      // Social stats are already set in state initialization
    }
  }, [profile])

  const showNotification = (message: string, type: 'success' | 'info' | 'error' = 'info') => {
    setNotification({ message, type })
    setTimeout(() => setNotification(null), 3000)
  }

  const toggleVisibility = () => {
    setIsPublic(!isPublic)
    // TODO: Update profile settings in database
    showNotification(
      `Profile is now ${!isPublic ? 'public' : 'private'}`, 
      'success'
    )
  }

  const handleAvatarUpload = async (file: File) => {
    console.log('Avatar upload:', file)
    // TODO: Implement avatar upload to Firebase Storage
    showNotification(`Avatar upload ready: ${file.name}`, 'info')
  }

  const handleEditProfile = () => {
    router.push('/profile/account')
  }

  const handleShare = () => {
    setActiveTab('sharing')
    showNotification('Switched to sharing tab', 'info')
  }

  if (loading) {
    return (
      <ProfileLayout>
        <div className="text-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your social profile...</p>
        </div>
      </ProfileLayout>
    )
  }

  if (!user) {
    return (
      <ProfileLayout>
        <div className="text-center py-12">
          <h2 className="text-xl font-bold text-white mb-4">Authentication Required</h2>
          <p className="text-gray-400 mb-6">Please log in to view your social profile.</p>
          <button
            onClick={() => router.push('/auth?redirect=/profile/social')}
            className="bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Login to Continue
          </button>
        </div>
      </ProfileLayout>
    )
  }

  const tabs = [
    { id: 'timeline', label: 'Timeline', icon: Activity },
    { id: 'connections', label: 'Connections', icon: Users },
    { id: 'sharing', label: 'Sharing', icon: Share2 },
    { id: 'settings', label: 'Privacy', icon: Settings }
  ] as const

  return (
    <ProfileLayout navigation="consolidated">
      <div className="space-y-8">
        {/* Enhanced Social Profile Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <SocialProfileHeader 
            profile={profile} 
            onSettingsClick={() => setShowVisibilitySettings(true)}
            onAvatarUpload={handleAvatarUpload}
            onEditProfile={handleEditProfile}
            onShare={handleShare}
            isPublic={isPublic}
            onToggleVisibility={toggleVisibility}
          />
        </motion.div>

        {/* Social Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <SocialStats stats={socialStats} />
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden"
        >
          <div className="flex flex-wrap border-b border-gray-700">
            {tabs.map((tab) => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center space-x-2 px-4 py-3 text-sm font-medium transition-colors
                    ${activeTab === tab.id
                      ? 'bg-accent-500/20 text-accent-400 border-b-2 border-accent-500'
                      : 'text-gray-400 hover:text-white hover:bg-gray-700'
                    }
                  `}
                >
                  <IconComponent size={16} />
                  <span className="hidden sm:inline">{tab.label}</span>
                </button>
              )
            })}
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'timeline' && (
              <SocialActivityTimeline profile={profile} />
            )}
            {activeTab === 'connections' && (
              <CommunityConnections profile={profile} />
            )}
            {activeTab === 'sharing' && (
              <SocialSharing profile={profile} autoSelectProfile={true} />
            )}
            {activeTab === 'settings' && (
              <ProfileVisibilitySettings profile={profile} />
            )}
          </div>
        </motion.div>

        {/* Visibility Settings Modal */}
        {showVisibilitySettings && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
            onClick={() => setShowVisibilitySettings(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-800 rounded-lg p-6 border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <ProfileVisibilitySettings 
                profile={profile} 
                onClose={() => setShowVisibilitySettings(false)}
              />
            </motion.div>
          </motion.div>
        )}

        {/* Notification Toast */}
        {notification && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed bottom-4 right-4 z-50"
          >
            <div className={`
              px-4 py-3 rounded-lg shadow-lg border max-w-sm
              ${notification.type === 'success' ? 'bg-green-600 border-green-500 text-white' :
                notification.type === 'error' ? 'bg-red-600 border-red-500 text-white' :
                'bg-blue-600 border-blue-500 text-white'
              }
            `}>
              <p className="text-sm font-medium">{notification.message}</p>
            </div>
          </motion.div>
        )}
      </div>
    </ProfileLayout>
  )
}
