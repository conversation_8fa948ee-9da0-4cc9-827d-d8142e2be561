'use client'

/**
 * Advanced Search & Discovery Page
 * 
 * Comprehensive search interface for finding community members
 * with intelligent filtering, discovery recommendations, and analytics.
 * 
 * Features:
 * - Intelligent search with autocomplete
 * - Advanced filtering and sorting
 * - Personalized discovery recommendations
 * - Search analytics and insights
 * - Real-time search results
 * 
 * <AUTHOR> Team
 */

import React, { useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Search,
  Compass,
  Users,
  Target,
  Brain,
  Sparkles
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import AdvancedSearchDashboard from '@/components/profile/search/AdvancedSearchDashboard'
import { useUser } from '@/lib/useUser'
import { advancedSearch } from '@/lib/search/advancedSearch'
import { UserProfile } from '@/types/profile'

export default function AdvancedSearchPage() {
  const { user, profile } = useUser()

  // Initialize search engine with mock data
  useEffect(() => {
    const initializeSearchData = async () => {
      // Mock user profiles for demonstration
      const mockProfiles: UserProfile[] = [
        {
          id: 'user-1',
          firstName: '<PERSON>',
          lastName: '<PERSON>',
          displayName: '<PERSON>',
          email: '<EMAIL>',
          bio: 'Passionate artisan keycap collector and mechanical keyboard enthusiast. Love experimenting with different switch types.',
          location: 'San Francisco',
          points: 1250,
          profileCompletion: { 
            percentage: 85, 
            completedSections: [
              { id: 'avatar', name: 'Avatar', completed: true, pointsAwarded: false },
              { id: 'bio', name: 'Bio', completed: true, pointsAwarded: false },
              { id: 'personal', name: 'Personal', completed: true, pointsAwarded: false }
            ],
            lastUpdated: new Date()
          },
          achievements: [
            { id: 'ach-1', achievementId: 'first-purchase', unlockedAt: new Date('2023-07-01') },
            { id: 'ach-2', achievementId: 'profile-complete', unlockedAt: new Date('2023-07-15') }
          ],
          interests: ['Artisan Keycaps', 'Mechanical Keyboards'],
          createdAt: new Date('2023-06-15'),
          updatedAt: new Date('2024-01-15')
        },
        {
          id: 'user-2',
          firstName: 'Sarah',
          lastName: 'Chen',
          displayName: 'Sarah C.',
          email: '<EMAIL>',
          bio: 'Custom keyboard builder with 5+ years experience. Specializing in 60% layouts and tactile switches.',
          location: 'New York',
          points: 2100,
          profileCompletion: { 
            percentage: 95, 
            completedSections: [
              { id: 'avatar', name: 'Avatar', completed: true, pointsAwarded: true },
              { id: 'bio', name: 'Bio', completed: true, pointsAwarded: true },
              { id: 'personal', name: 'Personal', completed: true, pointsAwarded: true },
              { id: 'social', name: 'Social', completed: true, pointsAwarded: true }
            ],
            lastUpdated: new Date()
          },
          achievements: [
            { id: 'ach-3', achievementId: 'first-purchase', unlockedAt: new Date('2023-03-25') },
            { id: 'ach-4', achievementId: 'profile-complete', unlockedAt: new Date('2023-04-01') },
            { id: 'ach-5', achievementId: 'community-active', unlockedAt: new Date('2023-06-15') }
          ],
          interests: ['Custom Builds', 'Switch Testing'],
          createdAt: new Date('2023-03-20'),
          updatedAt: new Date('2024-01-10')
        },
        {
          id: 'user-3',
          firstName: 'Mike',
          lastName: 'Rodriguez',
          displayName: 'Mike R.',
          email: '<EMAIL>',
          bio: 'Beginner in the keycap world, always eager to learn about new artisan designs and techniques.',
          location: 'Austin',
          points: 450,
          profileCompletion: { 
            percentage: 60, 
            completedSections: [
              { id: 'avatar', name: 'Avatar', completed: true, pointsAwarded: true },
              { id: 'bio', name: 'Bio', completed: true, pointsAwarded: true }
            ],
            lastUpdated: new Date()
          },
          achievements: [
            { id: 'ach-6', achievementId: 'first-profile', unlockedAt: new Date('2023-11-10') }
          ],
          interests: ['Learning', 'Artisan Keycaps'],
          createdAt: new Date('2023-11-10'),
          updatedAt: new Date('2024-01-05')
        },
        {
          id: 'user-4',
          firstName: 'Emily',
          lastName: 'Davis',
          displayName: 'Emily D.',
          email: '<EMAIL>',
          bio: 'Professional keycap artist and designer. Creating unique artisan pieces for the community.',
          location: 'Seattle',
          points: 3200,
          profileCompletion: { 
            percentage: 100, 
            completedSections: [
              { id: 'avatar', name: 'Avatar', completed: true, pointsAwarded: true },
              { id: 'bio', name: 'Bio', completed: true, pointsAwarded: true },
              { id: 'personal', name: 'Personal', completed: true, pointsAwarded: true },
              { id: 'social', name: 'Social', completed: true, pointsAwarded: true },
              { id: 'portfolio', name: 'Portfolio', completed: true, pointsAwarded: true }
            ],
            lastUpdated: new Date()
          },
          achievements: [
            { id: 'ach-7', achievementId: 'first-purchase', unlockedAt: new Date('2022-12-10') },
            { id: 'ach-8', achievementId: 'profile-complete', unlockedAt: new Date('2022-12-15') },
            { id: 'ach-9', achievementId: 'community-active', unlockedAt: new Date('2023-03-01') },
            { id: 'ach-10', achievementId: 'artist-verified', unlockedAt: new Date('2023-05-20') }
          ],
          interests: ['Artisan Design', '3D Printing', 'Resin Casting'],
          createdAt: new Date('2022-12-01'),
          updatedAt: new Date('2024-01-12')
        },
        {
          id: 'user-5',
          firstName: 'David',
          lastName: 'Kim',
          displayName: 'David K.',
          email: '<EMAIL>',
          bio: 'Gaming enthusiast and mechanical keyboard lover. Always testing new switches for the perfect gaming setup.',
          location: 'Los Angeles',
          points: 800,
          profileCompletion: { 
            percentage: 70, 
            completedSections: [
              { id: 'avatar', name: 'Avatar', completed: true, pointsAwarded: true },
              { id: 'bio', name: 'Bio', completed: true, pointsAwarded: true },
              { id: 'personal', name: 'Personal', completed: true, pointsAwarded: true }
            ],
            lastUpdated: new Date()
          },
          achievements: [
            { id: 'ach-11', achievementId: 'first-purchase', unlockedAt: new Date('2023-08-10') },
            { id: 'ach-12', achievementId: 'gamer-verified', unlockedAt: new Date('2023-09-01') }
          ],
          interests: ['Gaming Keyboards', 'Linear Switches'],
          createdAt: new Date('2023-08-05'),
          updatedAt: new Date('2024-01-08')
        },
        {
          id: 'user-6',
          firstName: 'Lisa',
          lastName: 'Wang',
          displayName: 'Lisa W.',
          email: '<EMAIL>',
          bio: 'Vintage keyboard collector and restoration expert. Preserving keyboard history one restoration at a time.',
          location: 'San Francisco',
          points: 1800,
          profileCompletion: { 
            percentage: 90, 
            completedSections: [
              { id: 'avatar', name: 'Avatar', completed: true, pointsAwarded: true },
              { id: 'bio', name: 'Bio', completed: true, pointsAwarded: true },
              { id: 'personal', name: 'Personal', completed: true, pointsAwarded: true },
              { id: 'social', name: 'Social', completed: true, pointsAwarded: true }
            ],
            lastUpdated: new Date()
          },
          achievements: [
            { id: 'ach-13', achievementId: 'first-purchase', unlockedAt: new Date('2023-01-20') },
            { id: 'ach-14', achievementId: 'profile-complete', unlockedAt: new Date('2023-02-01') },
            { id: 'ach-15', achievementId: 'vintage-expert', unlockedAt: new Date('2023-04-15') }
          ],
          interests: ['Vintage Keyboards', 'Restoration', 'History'],
          createdAt: new Date('2023-01-15'),
          updatedAt: new Date('2024-01-14')
        },
        {
          id: 'user-7',
          firstName: 'James',
          lastName: 'Thompson',
          displayName: 'James T.',
          email: '<EMAIL>',
          bio: 'Ergonomic keyboard advocate and developer. Building the future of comfortable typing.',
          location: 'Austin',
          points: 1500,
          profileCompletion: { 
            percentage: 80, 
            completedSections: [
              { id: 'avatar', name: 'Avatar', completed: true, pointsAwarded: true },
              { id: 'bio', name: 'Bio', completed: true, pointsAwarded: true },
              { id: 'personal', name: 'Personal', completed: true, pointsAwarded: true }
            ],
            lastUpdated: new Date()
          },
          achievements: [
            { id: 'ach-16', achievementId: 'first-purchase', unlockedAt: new Date('2023-05-25') },
            { id: 'ach-17', achievementId: 'ergonomics-expert', unlockedAt: new Date('2023-08-10') }
          ],
          interests: ['Ergonomic Design', 'Split Keyboards', 'Programming'],
          createdAt: new Date('2023-05-20'),
          updatedAt: new Date('2024-01-11')
        },
        {
          id: 'user-8',
          firstName: 'Maria',
          lastName: 'Garcia',
          displayName: 'Maria G.',
          email: '<EMAIL>',
          bio: 'Minimalist keyboard designer focusing on clean, functional layouts with premium materials.',
          location: 'New York',
          points: 2800,
          profileCompletion: { 
            percentage: 95, 
            completedSections: [
              { id: 'avatar', name: 'Avatar', completed: true, pointsAwarded: true },
              { id: 'bio', name: 'Bio', completed: true, pointsAwarded: true },
              { id: 'personal', name: 'Personal', completed: true, pointsAwarded: true },
              { id: 'social', name: 'Social', completed: true, pointsAwarded: true },
              { id: 'portfolio', name: 'Portfolio', completed: true, pointsAwarded: true }
            ],
            lastUpdated: new Date()
          },
          achievements: [
            { id: 'ach-18', achievementId: 'first-purchase', unlockedAt: new Date('2022-09-15') },
            { id: 'ach-19', achievementId: 'profile-complete', unlockedAt: new Date('2022-10-01') },
            { id: 'ach-20', achievementId: 'designer-verified', unlockedAt: new Date('2023-02-20') },
            { id: 'ach-21', achievementId: 'minimalist-expert', unlockedAt: new Date('2023-06-30') }
          ],
          interests: ['Minimalist Design', 'Premium Materials', 'Clean Layouts'],
          createdAt: new Date('2022-09-10'),
          updatedAt: new Date('2024-01-13')
        }
      ]

      // Initialize the search engine with mock data
      await advancedSearch.initializeIndex(mockProfiles)
    }

    initializeSearchData()
  }, [])

  if (!user || !profile) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <Search className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">Please log in</h3>
            <p className="text-gray-400">You need to be logged in to search and discover community members.</p>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout navigation="consolidated">
      <div className="space-y-6">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white flex items-center space-x-3">
                <Search size={28} />
                <span>Search & Discovery</span>
              </h1>
              <p className="text-gray-400 mt-2">
                Find and connect with community members who share your interests and passion for mechanical keyboards
              </p>
            </div>
            <div className="hidden md:flex items-center space-x-6">
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-full mb-2">
                  <Search size={24} className="text-white" />
                </div>
                <div className="text-sm text-gray-400">Smart Search</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-purple-600 rounded-full mb-2">
                  <Brain size={24} className="text-white" />
                </div>
                <div className="text-sm text-gray-400">AI Discovery</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-green-600 rounded-full mb-2">
                  <Target size={24} className="text-white" />
                </div>
                <div className="text-sm text-gray-400">Advanced Filters</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Search Features Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
        >
          <div className="bg-gradient-to-br from-blue-600 to-cyan-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <Search size={24} />
              <div className="text-right">
                <div className="text-2xl font-bold">Smart Search</div>
                <div className="text-blue-100 text-sm">Intelligent & Fast</div>
              </div>
            </div>
            <p className="text-blue-100 text-sm">
              Advanced search with fuzzy matching, autocomplete, and intelligent ranking algorithms.
            </p>
          </div>

          <div className="bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <Compass size={24} />
              <div className="text-right">
                <div className="text-2xl font-bold">Discovery</div>
                <div className="text-purple-100 text-sm">Personalized Matches</div>
              </div>
            </div>
            <p className="text-purple-100 text-sm">
              AI-powered recommendations to discover members with similar interests and experience levels.
            </p>
          </div>

          <div className="bg-gradient-to-br from-green-600 to-emerald-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <Target size={24} />
              <div className="text-right">
                <div className="text-2xl font-bold">Filters</div>
                <div className="text-green-100 text-sm">Precise & Flexible</div>
              </div>
            </div>
            <p className="text-green-100 text-sm">
              Advanced filtering by location, experience level, points, achievements, and more.
            </p>
          </div>
        </motion.div>

        {/* Main Search Dashboard */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <AdvancedSearchDashboard profile={profile} />
        </motion.div>

        {/* Search Tips */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <h2 className="text-xl font-semibold text-white mb-6 flex items-center space-x-2">
            <Sparkles size={20} />
            <span>Search Tips & Tricks</span>
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                  1
                </div>
                <h3 className="text-white font-medium">Use Specific Keywords</h3>
              </div>
              <p className="text-gray-400 text-sm ml-8">
                Search for specific interests like "artisan keycaps" or "mechanical switches" 
                to find members with similar hobbies.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                  2
                </div>
                <h3 className="text-white font-medium">Combine Filters</h3>
              </div>
              <p className="text-gray-400 text-sm ml-8">
                Use multiple filters together (location + experience level + points) 
                to narrow down results to exactly what you're looking for.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                  3
                </div>
                <h3 className="text-white font-medium">Try Discovery Mode</h3>
              </div>
              <p className="text-gray-400 text-sm ml-8">
                Switch to the Discovery tab to find personalized recommendations 
                based on your profile and activity patterns.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                  4
                </div>
                <h3 className="text-white font-medium">Sort Results</h3>
              </div>
              <p className="text-gray-400 text-sm ml-8">
                Change sorting options to find the most relevant, highest-rated, 
                or most recently active members first.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-red-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                  5
                </div>
                <h3 className="text-white font-medium">Use Autocomplete</h3>
              </div>
              <p className="text-gray-400 text-sm ml-8">
                Start typing and select from autocomplete suggestions to quickly 
                find popular search terms and member names.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-orange-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                  6
                </div>
                <h3 className="text-white font-medium">Search by Location</h3>
              </div>
              <p className="text-gray-400 text-sm ml-8">
                Find local community members by searching for your city or 
                using the location filter to connect with nearby enthusiasts.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Search Categories */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <h2 className="text-xl font-semibold text-white mb-6">Popular Search Categories</h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { name: 'Artisan Collectors', icon: '🎨', description: 'Keycap art enthusiasts' },
              { name: 'Switch Testers', icon: '⌨️', description: 'Mechanical switch experts' },
              { name: 'Custom Builders', icon: '🔧', description: 'DIY keyboard builders' },
              { name: 'Gaming Setup', icon: '🎮', description: 'Gaming keyboard pros' },
              { name: 'Vintage Lovers', icon: '📻', description: 'Retro keyboard fans' },
              { name: 'Ergonomic Focus', icon: '💪', description: 'Comfort-first users' },
              { name: 'Minimalist Design', icon: '⚡', description: 'Clean & simple layouts' },
              { name: 'Local Community', icon: '📍', description: 'Nearby members' }
            ].map((category, index) => (
              <div key={index} className="text-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors cursor-pointer">
                <div className="text-2xl mb-2">{category.icon}</div>
                <div className="text-white font-medium text-sm mb-1">{category.name}</div>
                <div className="text-gray-400 text-xs">{category.description}</div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </ProfileLayout>
  )
}