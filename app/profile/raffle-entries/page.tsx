'use client'

/**
 * Profile Raffle Entries Page
 * 
 * Dedicated page for users to view their raffle entry history and status.
 * 
 * Features:
 * - Complete raffle entry history
 * - Win/lose status display
 * - Entry details and timestamps
 * - Filter and search functionality
 * - Direct links to raffle products
 * 
 * <AUTHOR> Team
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Trophy,
  Calendar,
  Package,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Filter,
  Search,
  ExternalLink,
  CreditCard
} from 'lucide-react'
import Link from 'next/link'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'
import {
  collection,
  query,
  where,
  orderBy,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  serverTimestamp
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { toast } from 'react-hot-toast'

/**
 * Raffle entry interface
 */
interface RaffleEntry {
  id: string
  raffleId: string
  productId: string
  productName: string
  productImage: string
  variantId?: string
  variantName?: string
  entryDate: Date
  status: 'pending' | 'confirmed' | 'winner' | 'loser' | 'payment-pending' | 'paid'
  raffleStatus: 'upcoming' | 'active' | 'ended'
  raffleEndDate: Date
  winnerAnnounced: boolean
  orderCreated?: boolean
  orderNumber?: string
  paymentDeadline?: Date
  productPrice?: number
  shippingCost?: number
  totalAmount?: number
  paymentId?: string
  paidAt?: Date
  shippingMethod?: string
}

/**
 * Filter options
 */
type FilterType = 'all' | 'active' | 'ended' | 'won' | 'lost'

/**
 * Main raffle entries page component
 */
export default function RaffleEntriesPage() {
  const { user } = useUser()
  
  // State management
  const [entries, setEntries] = useState<RaffleEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<FilterType>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [payingEntries, setPayingEntries] = useState<Set<string>>(new Set())

  /**
   * Load user's raffle entries
   */
  useEffect(() => {
    if (user) {
      loadRaffleEntries()
    }
  }, [user])

  /**
   * Load raffle entries from Firestore
   */
  const loadRaffleEntries = async () => {
    if (!user) return

    setLoading(true)
    try {
      // Get user's raffle entries
      const entriesQuery = query(
        collection(db, 'raffle_entries'),
        where('userId', '==', user.uid),
        orderBy('entryDate', 'desc')
      )
      
      const entriesSnapshot = await getDocs(entriesQuery)
      const entriesData: RaffleEntry[] = []

      // Process each entry and get additional data
      for (const entryDoc of entriesSnapshot.docs) {
        const entryData = entryDoc.data()

        try {
          // Get raffle information
          const raffleDoc = await getDoc(doc(db, 'raffles', entryData.raffleId))
          const raffleData = raffleDoc.data()

          // Handle both old and new data structures
          if (entryData.selectedVariants && Array.isArray(entryData.selectedVariants)) {
            // New format: multiple variants with detailed info
            for (const variant of entryData.selectedVariants) {
              // Calculate shipping cost based on method
              const shippingCost = entryData.shippingMethod === 'express' ? 25 :
                                 entryData.shippingMethod === 'priority' ? 15 : 10 // standard
              const productPrice = variant.price || 50
              const totalAmount = productPrice + shippingCost

              const entry: RaffleEntry = {
                id: `${entryDoc.id}_${variant.id}`,
                raffleId: entryData.raffleId,
                productId: variant.productId,
                productName: variant.productName || 'Unknown Product',
                productImage: variant.image || '/placeholder-product.jpg',
                variantId: variant.id,
                variantName: variant.name,
                entryDate: entryData.entryDate?.toDate() || new Date(),
                status: entryData.status || 'confirmed',
                raffleStatus: raffleData?.status || 'ended',
                raffleEndDate: raffleData?.endDate?.toDate() || new Date(),
                winnerAnnounced: raffleData?.winnerAnnounced || false,
                orderCreated: entryData.orderCreated || false,
                orderNumber: entryData.orderNumber,
                paymentDeadline: entryData.paymentDeadline?.toDate(),
                productPrice: productPrice,
                shippingCost: shippingCost,
                totalAmount: totalAmount,
                shippingMethod: entryData.shippingMethod || 'standard',
                paymentId: entryData.paymentId,
                paidAt: entryData.paidAt?.toDate()
              }

              // Determine if user won or lost
              if (raffleData?.winners && raffleData.winners.includes(user.uid)) {
                entry.status = 'winner'
              } else if (raffleData?.status === 'ended' && raffleData?.winnerAnnounced) {
                entry.status = 'loser'
              }

              entriesData.push(entry)
            }
          } else if (entryData.productIds && Array.isArray(entryData.productIds)) {
            // Old format: array of product IDs
            for (const productId of entryData.productIds) {
              try {
                const productDoc = await getDoc(doc(db, 'products', productId))
                const productData = productDoc.data()

                // Calculate shipping cost based on method
                const shippingCost = entryData.shippingMethod === 'express' ? 25 :
                                   entryData.shippingMethod === 'priority' ? 15 : 10 // standard
                const productPrice = productData?.price || 50
                const totalAmount = productPrice + shippingCost

                const entry: RaffleEntry = {
                  id: `${entryDoc.id}_${productId}`,
                  raffleId: entryData.raffleId,
                  productId: productId,
                  productName: productData?.name || 'Unknown Product',
                  productImage: productData?.images?.[0] || productData?.image || '/placeholder-product.jpg',
                  variantId: entryData.variantId,
                  variantName: entryData.variantName,
                  entryDate: entryData.entryDate?.toDate() || new Date(),
                  status: entryData.status || 'confirmed',
                  raffleStatus: raffleData?.status || 'ended',
                  raffleEndDate: raffleData?.endDate?.toDate() || new Date(),
                  winnerAnnounced: raffleData?.winnerAnnounced || false,
                  orderCreated: entryData.orderCreated || false,
                  orderNumber: entryData.orderNumber,
                  paymentDeadline: entryData.paymentDeadline?.toDate(),
                  productPrice: productPrice,
                  shippingCost: shippingCost,
                  totalAmount: totalAmount,
                  shippingMethod: entryData.shippingMethod || 'standard',
                  paymentId: entryData.paymentId,
                  paidAt: entryData.paidAt?.toDate()
                }

                // Determine if user won or lost
                if (raffleData?.winners && raffleData.winners.includes(user.uid)) {
                  entry.status = 'winner'
                } else if (raffleData?.status === 'ended' && raffleData?.winnerAnnounced) {
                  entry.status = 'loser'
                }

                entriesData.push(entry)
              } catch (productError) {
                console.error('Error fetching product:', productId, productError)
              }
            }
          } else if (entryData.productId) {
            // Legacy format: single product ID
            try {
              const productDoc = await getDoc(doc(db, 'products', entryData.productId))
              const productData = productDoc.data()

              // Calculate shipping cost based on method
              const shippingCost = entryData.shippingMethod === 'express' ? 25 :
                                 entryData.shippingMethod === 'priority' ? 15 : 10 // standard
              const productPrice = productData?.price || 50
              const totalAmount = productPrice + shippingCost

              const entry: RaffleEntry = {
                id: entryDoc.id,
                raffleId: entryData.raffleId,
                productId: entryData.productId,
                productName: productData?.name || 'Unknown Product',
                productImage: productData?.images?.[0] || productData?.image || '/placeholder-product.jpg',
                variantId: entryData.variantId,
                variantName: entryData.variantName,
                entryDate: entryData.entryDate?.toDate() || new Date(),
                status: entryData.status || 'confirmed',
                raffleStatus: raffleData?.status || 'ended',
                raffleEndDate: raffleData?.endDate?.toDate() || new Date(),
                winnerAnnounced: raffleData?.winnerAnnounced || false,
                orderCreated: entryData.orderCreated || false,
                orderNumber: entryData.orderNumber,
                paymentDeadline: entryData.paymentDeadline?.toDate(),
                productPrice: productPrice,
                shippingCost: shippingCost,
                totalAmount: totalAmount,
                shippingMethod: entryData.shippingMethod || 'standard',
                paymentId: entryData.paymentId,
                paidAt: entryData.paidAt?.toDate()
              }

              // Determine if user won or lost
              if (raffleData?.winners && raffleData.winners.includes(user.uid)) {
                entry.status = 'winner'
              } else if (raffleData?.status === 'ended' && raffleData?.winnerAnnounced) {
                entry.status = 'loser'
              }

              entriesData.push(entry)
            } catch (productError) {
              console.error('Error fetching product:', entryData.productId, productError)
            }
          }
        } catch (error) {
          console.error('Error processing entry:', entryDoc.id, error)
        }
      }

      setEntries(entriesData)
    } catch (error) {
      console.error('Error loading raffle entries:', error)
    } finally {
      setLoading(false)
    }
  }

  /**
   * Filter entries based on current filter and search term
   */
  const filteredEntries = entries.filter(entry => {
    // Apply filter
    switch (filter) {
      case 'active':
        return entry.raffleStatus === 'active'
      case 'ended':
        return entry.raffleStatus === 'ended'
      case 'won':
        return entry.status === 'winner'
      case 'lost':
        return entry.status === 'loser'
      default:
        return true
    }
  }).filter(entry => {
    // Apply search
    if (!searchTerm) return true
    return entry.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           entry.variantName?.toLowerCase().includes(searchTerm.toLowerCase())
  })

  /**
   * Get status badge component
   */
  const getStatusBadge = (entry: RaffleEntry) => {
    switch (entry.status) {
      case 'winner':
        return (
          <div className="flex items-center gap-1 bg-green-900/30 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
            <Trophy size={12} />
            Winner - Payment Required
          </div>
        )
      case 'payment-pending':
        return (
          <div className="flex items-center gap-1 bg-yellow-900/30 text-yellow-400 px-2 py-1 rounded-full text-xs font-medium">
            <CreditCard size={12} />
            Payment Pending
          </div>
        )
      case 'paid':
        return (
          <div className="flex items-center gap-1 bg-green-900/30 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
            <CheckCircle size={12} />
            Paid
          </div>
        )
      case 'loser':
        return (
          <div className="flex items-center gap-1 bg-red-900/30 text-red-400 px-2 py-1 rounded-full text-xs font-medium">
            <XCircle size={12} />
            Not Selected
          </div>
        )
      case 'confirmed':
        return entry.raffleStatus === 'active' ? (
          <div className="flex items-center gap-1 bg-blue-900/30 text-blue-400 px-2 py-1 rounded-full text-xs font-medium">
            <Clock size={12} />
            Active
          </div>
        ) : (
          <div className="flex items-center gap-1 bg-yellow-900/30 text-yellow-400 px-2 py-1 rounded-full text-xs font-medium">
            <Clock size={12} />
            Pending Results
          </div>
        )
      default:
        return (
          <div className="flex items-center gap-1 bg-gray-900/30 text-gray-400 px-2 py-1 rounded-full text-xs font-medium">
            <Clock size={12} />
            Pending
          </div>
        )
    }
  }

  /**
   * Get entry statistics
   */
  const getStats = () => {
    const total = entries.length
    const active = entries.filter(e => e.raffleStatus === 'active').length
    const won = entries.filter(e => e.status === 'winner').length
    const lost = entries.filter(e => e.status === 'loser').length

    return { total, active, won, lost }
  }

  const stats = getStats()

  /**
   * Handle PayPal payment for raffle winner
   */
  const handlePayPalPayment = async (entryId: string, paymentId: string) => {
    try {
      setPayingEntries(prev => new Set(prev).add(entryId))

      // Update the raffle entry with payment information
      const entryRef = doc(db, 'raffle_entries', entryId.split('_')[0]) // Get original entry ID
      await updateDoc(entryRef, {
        status: 'paid',
        paymentId: paymentId,
        paidAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      })

      // Update local state
      setEntries(prev => prev.map(entry =>
        entry.id === entryId
          ? { ...entry, status: 'paid' as const, paymentId, paidAt: new Date() }
          : entry
      ))

      // Log payment activity
      try {
        const { logActivity } = await import('@/lib/activitySystem')
        const entry = entries.find(e => e.id === entryId)
        if (entry && user) {
          await logActivity(
            user.uid,
            'raffle_entry',
            'Raffle Payment Completed',
            `Payment completed for raffle win: ${entry.productName}`,
            '127.0.0.1',
            'Unknown',
            'Web Browser',
            true,
            {
              raffleId: entry.raffleId,
              productName: entry.productName,
              paymentId: paymentId,
              totalAmount: entry.totalAmount || ((entry.productPrice || 50) + (entry.shippingCost || 10)),
              paymentMethod: 'paypal'
            }
          )
        }
      } catch (activityError) {
        console.error('Failed to log payment activity:', activityError)
      }

      toast.success('Payment completed successfully!')
    } catch (error) {
      console.error('Error updating payment status:', error)
      toast.error('Payment completed but failed to update status. Please contact support.')
    } finally {
      setPayingEntries(prev => {
        const newSet = new Set(prev)
        newSet.delete(entryId)
        return newSet
      })
    }
  }

  /**
   * Generate PayPal invoice link
   */
  const generatePayPalInvoice = (entry: RaffleEntry) => {
    const totalAmount = entry.totalAmount || (entry.productPrice || 50) + (entry.shippingCost || 10)
    const description = `Raffle Winner Payment - ${entry.productName}${entry.variantName ? ` (${entry.variantName})` : ''}`

    // Create PayPal payment link (this would normally be generated server-side)
    // Use sandbox for development/testing
    const paypalUrl = `https://www.sandbox.paypal.com/invoice/create?` + new URLSearchParams({
      business: '<EMAIL>', // Replace with your PayPal sandbox business email
      item_name: description,
      amount: totalAmount.toFixed(2),
      currency_code: 'USD',
      reference: entry.id,
      return_url: `${window.location.origin}/profile/raffle-entries?payment=success`,
      cancel_url: `${window.location.origin}/profile/raffle-entries?payment=cancelled`
    }).toString()

    return paypalUrl
  }

  /**
   * Handle PayPal invoice payment
   */
  const handlePayPalInvoice = async (entry: RaffleEntry) => {
    try {
      setPayingEntries(prev => new Set(prev).add(entry.id))

      // Update status to payment-pending
      const entryRef = doc(db, 'raffle_entries', entry.id.split('_')[0])
      await updateDoc(entryRef, {
        status: 'payment-pending',
        paymentMethod: 'paypal_invoice',
        invoiceGenerated: true,
        updatedAt: serverTimestamp()
      })

      // Update local state
      setEntries(prev => prev.map(e =>
        e.id === entry.id
          ? { ...e, status: 'payment-pending' as const }
          : e
      ))

      // Generate and open PayPal invoice
      const invoiceUrl = generatePayPalInvoice(entry)
      window.open(invoiceUrl, '_blank')

      // Log PayPal invoice generation activity
      try {
        const { logActivity } = await import('@/lib/activitySystem')
        if (user) {
          await logActivity(
            user.uid,
            'raffle_entry',
            'PayPal Invoice Generated',
            `PayPal invoice generated for raffle win: ${entry.productName}`,
            '127.0.0.1',
            'Unknown',
            'Web Browser',
            true,
            {
              raffleId: entry.raffleId,
              productName: entry.productName,
              totalAmount: entry.totalAmount || ((entry.productPrice || 50) + (entry.shippingCost || 10)),
              paymentMethod: 'paypal_invoice',
              invoiceUrl: invoiceUrl
            }
          )
        }
      } catch (activityError) {
        console.error('Failed to log PayPal invoice activity:', activityError)
      }

      toast.success('PayPal invoice opened. Complete payment in the new tab.')
    } catch (error) {
      console.error('Error generating PayPal invoice:', error)
      toast.error('Failed to generate PayPal invoice. Please try manual payment.')
    } finally {
      setPayingEntries(prev => {
        const newSet = new Set(prev)
        newSet.delete(entry.id)
        return newSet
      })
    }
  }

  /**
   * Handle manual payment (fallback when PayPal fails)
   */
  const handleManualPayment = async (entry: RaffleEntry) => {
    const totalAmount = entry.totalAmount || ((entry.productPrice || 50) + (entry.shippingCost || 10))
    const confirmed = window.confirm(
      `Manual Payment Required\n\n` +
      `Product: ${entry.productName}${entry.variantName ? ` (${entry.variantName})` : ''}\n` +
      `Product Price: $${entry.productPrice || 50}.00\n` +
      `Shipping (${entry.shippingMethod || 'standard'}): $${entry.shippingCost || 10}.00\n` +
      `Total Amount: $${totalAmount.toFixed(2)} USD\n\n` +
      `Please contact support to complete your payment:\n` +
      `Email: <EMAIL>\n` +
      `Reference: ${entry.id}\n\n` +
      `Click OK to mark this as payment pending.`
    )

    if (confirmed) {
      try {
        setPayingEntries(prev => new Set(prev).add(entry.id))

        // Update status to payment-pending for manual processing
        const entryRef = doc(db, 'raffle_entries', entry.id.split('_')[0])
        await updateDoc(entryRef, {
          status: 'payment-pending',
          paymentMethod: 'manual',
          updatedAt: serverTimestamp()
        })

        // Update local state
        setEntries(prev => prev.map(e =>
          e.id === entry.id
            ? { ...e, status: 'payment-pending' as const }
            : e
        ))

        // Log manual payment request activity
        try {
          const { logActivity } = await import('@/lib/activitySystem')
          if (user) {
            await logActivity(
              user.uid,
              'raffle_entry',
              'Manual Payment Requested',
              `Manual payment requested for raffle win: ${entry.productName}`,
              '127.0.0.1',
              'Unknown',
              'Web Browser',
              true,
              {
                raffleId: entry.raffleId,
                productName: entry.productName,
                totalAmount: entry.totalAmount || ((entry.productPrice || 50) + (entry.shippingCost || 10)),
                paymentMethod: 'manual',
                requestedAt: new Date().toISOString()
              }
            )
          }
        } catch (activityError) {
          console.error('Failed to log manual payment activity:', activityError)
        }

        toast.success('Payment request submitted. Support will contact you shortly.')
      } catch (error) {
        console.error('Error updating payment status:', error)
        toast.error('Failed to submit payment request. Please try again.')
      } finally {
        setPayingEntries(prev => {
          const newSet = new Set(prev)
          newSet.delete(entry.id)
          return newSet
        })
      }
    }
  }

  if (!user) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="text-center py-12">
          <p className="text-gray-400">Please log in to view your raffle entries.</p>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout navigation="consolidated">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-white">My Raffle Entries</h1>
          <p className="text-gray-400 mt-1">
            Track your raffle participation and results
          </p>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-800 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">{stats.total}</div>
            <div className="text-sm text-gray-400">Total Entries</div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-400">{stats.active}</div>
            <div className="text-sm text-gray-400">Active Raffles</div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-400">{stats.won}</div>
            <div className="text-sm text-gray-400">Won</div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-red-400">{stats.lost}</div>
            <div className="text-sm text-gray-400">Not Selected</div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Filter Buttons */}
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: 'All' },
              { key: 'active', label: 'Active' },
              { key: 'ended', label: 'Ended' },
              { key: 'won', label: 'Won' },
              { key: 'lost', label: 'Lost' }
            ].map(({ key, label }) => (
              <button
                key={key}
                onClick={() => setFilter(key as FilterType)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  filter === key
                    ? 'bg-accent-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {label}
              </button>
            ))}
          </div>

          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading your raffle entries...</p>
          </div>
        )}

        {/* Empty State */}
        {!loading && entries.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12 bg-gray-800 rounded-lg border border-gray-700"
          >
            <Package size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No raffle entries yet</h3>
            <p className="text-gray-400 mb-6">
              Join your first raffle to start tracking your entries here.
            </p>
            <Link
              href="/shop"
              className="btn-primary"
            >
              Browse Raffles
            </Link>
          </motion.div>
        )}

        {/* Entries List */}
        {!loading && filteredEntries.length > 0 && (
          <div className="space-y-4">
            {filteredEntries.map((entry) => (
              <motion.div
                key={entry.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 rounded-lg border border-gray-700 p-6"
              >
                <div className="flex items-start gap-4">
                  {/* Product Image */}
                  <div className="w-16 h-16 bg-gray-700 rounded-lg overflow-hidden flex-shrink-0">
                    <img
                      src={entry.productImage}
                      alt={entry.productName}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Entry Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="font-semibold text-white truncate">{entry.productName}</h3>
                        {entry.variantName && (
                          <p className="text-sm text-gray-400">{entry.variantName}</p>
                        )}
                      </div>
                      {getStatusBadge(entry)}
                    </div>

                    <div className="flex items-center gap-4 text-sm text-gray-400 mb-3">
                      <div className="flex items-center gap-1">
                        <Calendar size={14} />
                        Entered {(() => {
                          let dateObj = entry.entryDate
                          if (typeof dateObj === 'string') dateObj = new Date(dateObj)
                          if (dateObj && typeof (dateObj as any).toDate === 'function') dateObj = (dateObj as any).toDate()
                          return dateObj instanceof Date && !isNaN(dateObj.getTime())
                            ? dateObj.toLocaleDateString()
                            : 'Invalid date'
                        })()}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock size={14} />
                        Ends {(() => {
                          let dateObj = entry.raffleEndDate
                          if (typeof dateObj === 'string') dateObj = new Date(dateObj)
                          if (dateObj && typeof (dateObj as any).toDate === 'function') dateObj = (dateObj as any).toDate()
                          return dateObj instanceof Date && !isNaN(dateObj.getTime())
                            ? dateObj.toLocaleDateString()
                            : 'Invalid date'
                        })()}
                      </div>
                    </div>

                    {/* Winner Information */}
                    {(entry.status === 'winner' || entry.status === 'payment-pending') && (
                      <div className="bg-green-900/20 border border-green-700 rounded-lg p-4 mb-3">
                        <div className="flex items-center gap-2 text-green-300 mb-3">
                          <Trophy size={16} />
                          <span className="font-medium">Congratulations! You won this raffle!</span>
                        </div>

                        {entry.paymentDeadline && (
                          <p className="text-yellow-300 text-sm mb-3">
                            Payment deadline: {(() => {
                              let dateObj = entry.paymentDeadline
                              if (typeof dateObj === 'string') dateObj = new Date(dateObj)
                              if (dateObj && typeof (dateObj as any).toDate === 'function') dateObj = (dateObj as any).toDate()
                              return dateObj instanceof Date && !isNaN(dateObj.getTime())
                                ? `${dateObj.toLocaleDateString()} at ${dateObj.toLocaleTimeString()}`
                                : 'Invalid date'
                            })()}
                          </p>
                        )}

                        <div className="bg-gray-800 rounded-lg p-4">
                          <h4 className="text-white font-semibold mb-2 flex items-center gap-2">
                            <CreditCard size={16} />
                            Complete Payment
                          </h4>

                          {/* Payment Breakdown */}
                          <div className="bg-gray-700 rounded-lg p-3 mb-4">
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between text-gray-300">
                                <span>Product: {entry.variantName || entry.productName}</span>
                                <span>${entry.productPrice || 50}.00</span>
                              </div>
                              <div className="flex justify-between text-gray-300">
                                <span>Shipping ({entry.shippingMethod || 'standard'})</span>
                                <span>${entry.shippingCost || 10}.00</span>
                              </div>
                              <div className="border-t border-gray-600 pt-2">
                                <div className="flex justify-between text-white font-semibold">
                                  <span>Total Amount</span>
                                  <span>${entry.totalAmount || ((entry.productPrice || 50) + (entry.shippingCost || 10))}.00 USD</span>
                                </div>
                              </div>
                            </div>
                          </div>

                          <p className="text-gray-300 text-sm mb-4">
                            Complete your payment to secure your raffle win. You will receive order confirmation and shipping details after payment.
                          </p>

                          {/* Payment Options */}
                          <div className="space-y-3">
                            {/* PayPal Invoice Option */}
                            <button
                              onClick={() => handlePayPalInvoice(entry)}
                              disabled={payingEntries.has(entry.id)}
                              className="w-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                            >
                              <CreditCard size={16} />
                              {payingEntries.has(entry.id) ? 'Generating Invoice...' : 'Pay with PayPal Invoice'}
                            </button>

                            {/* Manual Payment Option */}
                            <button
                              onClick={() => handleManualPayment(entry)}
                              disabled={payingEntries.has(entry.id)}
                              className="w-full bg-gray-600 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                            >
                              <CreditCard size={16} />
                              {payingEntries.has(entry.id) ? 'Processing...' : 'Contact Support for Other Payment Methods'}
                            </button>
                          </div>

                          <div className="mt-4 text-xs text-gray-400 space-y-1">
                            <p>• PayPal invoice will open in a new tab</p>
                            <p>• Payment deadline: {(() => {
                              let dateObj = entry.paymentDeadline
                              if (!dateObj) return 'Not set'
                              if (typeof dateObj === 'string') dateObj = new Date(dateObj)
                              if (dateObj && typeof (dateObj as any).toDate === 'function') dateObj = (dateObj as any).toDate()
                              return dateObj instanceof Date && !isNaN(dateObj.getTime())
                                ? dateObj.toLocaleDateString()
                                : 'Invalid date'
                            })()}</p>
                            <p>• Reference ID: {entry.id}</p>
                            <p>• Support: <EMAIL></p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Paid Status */}
                    {entry.status === 'paid' && (
                      <div className="bg-green-900/20 border border-green-700 rounded-lg p-3 mb-3">
                        <div className="flex items-center gap-2 text-green-300">
                          <CheckCircle size={16} />
                          <span className="font-medium">Payment completed successfully!</span>
                        </div>
                        {entry.paidAt && (
                          <p className="text-green-200 text-sm mt-1">
                            Paid on {(() => {
                              let dateObj = entry.paidAt
                              if (typeof dateObj === 'string') dateObj = new Date(dateObj)
                              if (dateObj && typeof (dateObj as any).toDate === 'function') dateObj = (dateObj as any).toDate()
                              return dateObj instanceof Date && !isNaN(dateObj.getTime())
                                ? `${dateObj.toLocaleDateString()} at ${dateObj.toLocaleTimeString()}`
                                : 'Invalid date'
                            })()}
                          </p>
                        )}
                        {entry.paymentId && (
                          <p className="text-gray-300 text-xs mt-1">
                            Payment ID: {entry.paymentId}
                          </p>
                        )}
                        <p className="text-green-200 text-sm mt-2">
                          Your order will be processed and you'll receive shipping information via email.
                        </p>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex items-center gap-2">
                      <Link
                        href={`/shop/${entry.productId}`}
                        className="text-accent-400 hover:text-accent-300 text-sm font-medium flex items-center gap-1 transition-colors"
                      >
                        <Eye size={14} />
                        View Product
                        <ExternalLink size={12} />
                      </Link>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* No Results */}
        {!loading && entries.length > 0 && filteredEntries.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-400">No entries match your current filter.</p>
          </div>
        )}
      </div>
    </ProfileLayout>
  )
}
