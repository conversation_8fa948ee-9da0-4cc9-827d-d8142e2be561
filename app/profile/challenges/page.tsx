/**
 * Community Challenges Page
 * 
 * Allows users to participate in community challenges, view progress,
 * and earn rewards for completing various keycap-related activities.
 * 
 * Features:
 * - Active challenges list
 * - Challenge progress tracking
 * - Reward information
 * - Community leaderboards
 * - Challenge history
 * - Mobile-responsive design
 * 
 * Route: /profile/challenges
 * Authentication: Required
 * Navigation: Referenced in PROFILE_NAVIGATION.rewards.challenges
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Target, 
  Trophy, 
  Clock, 
  Users, 
  Star, 
  CheckCircle, 
  Calendar,
  Award,
  TrendingUp,
  Zap,
  Gift,
  Medal
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'

// Types for challenges
interface Challenge {
  id: string
  title: string
  description: string
  type: 'daily' | 'weekly' | 'monthly' | 'special'
  difficulty: 'easy' | 'medium' | 'hard' | 'legendary'
  progress: number
  maxProgress: number
  reward: {
    points: number
    badge?: string
    title?: string
  }
  startDate: Date
  endDate: Date
  isCompleted: boolean
  participants: number
  category: 'shopping' | 'social' | 'design' | 'community'
}

/**
 * Community Challenges Page Component
 */
export default function ChallengesPage() {
  const { user, profile, loading: profileLoading } = useUser()
  
  const [challenges, setChallenges] = useState<Challenge[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'active' | 'completed' | 'upcoming'>('active')

  // Mock data for demonstration - replace with actual API calls
  useEffect(() => {
    const loadChallenges = async () => {
      setLoading(true)
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock challenges data
      const mockChallenges: Challenge[] = [
        {
          id: '1',
          title: 'Keycap Collector',
          description: 'Purchase 3 different keycap sets this month',
          type: 'monthly',
          difficulty: 'medium',
          progress: 1,
          maxProgress: 3,
          reward: {
            points: 500,
            badge: 'Collector',
            title: 'Keycap Enthusiast'
          },
          startDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000),
          isCompleted: false,
          participants: 1247,
          category: 'shopping'
        },
        {
          id: '2',
          title: 'Community Contributor',
          description: 'Share 5 keycap photos in the community gallery',
          type: 'weekly',
          difficulty: 'easy',
          progress: 3,
          maxProgress: 5,
          reward: {
            points: 200,
            badge: 'Contributor'
          },
          startDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000),
          isCompleted: false,
          participants: 892,
          category: 'social'
        },
        {
          id: '3',
          title: 'Design Master',
          description: 'Create and submit a custom keycap design',
          type: 'special',
          difficulty: 'hard',
          progress: 0,
          maxProgress: 1,
          reward: {
            points: 1000,
            badge: 'Design Master',
            title: 'Creative Genius'
          },
          startDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000),
          isCompleted: false,
          participants: 234,
          category: 'design'
        },
        {
          id: '4',
          title: 'Daily Login Streak',
          description: 'Log in for 7 consecutive days',
          type: 'daily',
          difficulty: 'easy',
          progress: 7,
          maxProgress: 7,
          reward: {
            points: 100,
            badge: 'Dedicated'
          },
          startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now()),
          isCompleted: true,
          participants: 3456,
          category: 'community'
        }
      ]
      
      setChallenges(mockChallenges)
      setLoading(false)
    }

    if (user) {
      loadChallenges()
    }
  }, [user])

  const getDifficultyColor = (difficulty: Challenge['difficulty']) => {
    switch (difficulty) {
      case 'easy':
        return 'text-green-500 bg-green-500/10'
      case 'medium':
        return 'text-yellow-500 bg-yellow-500/10'
      case 'hard':
        return 'text-red-500 bg-red-500/10'
      case 'legendary':
        return 'text-purple-500 bg-purple-500/10'
      default:
        return 'text-gray-500 bg-gray-500/10'
    }
  }

  const getTypeIcon = (type: Challenge['type']) => {
    switch (type) {
      case 'daily':
        return <Calendar className="text-blue-500" size={16} />
      case 'weekly':
        return <Clock className="text-green-500" size={16} />
      case 'monthly':
        return <TrendingUp className="text-purple-500" size={16} />
      case 'special':
        return <Zap className="text-yellow-500" size={16} />
      default:
        return <Target className="text-gray-500" size={16} />
    }
  }

  const getCategoryIcon = (category: Challenge['category']) => {
    switch (category) {
      case 'shopping':
        return <Gift className="text-blue-500" size={16} />
      case 'social':
        return <Users className="text-green-500" size={16} />
      case 'design':
        return <Award className="text-purple-500" size={16} />
      case 'community':
        return <Medal className="text-yellow-500" size={16} />
      default:
        return <Target className="text-gray-500" size={16} />
    }
  }

  const filteredChallenges = challenges.filter(challenge => {
    switch (activeTab) {
      case 'active':
        return !challenge.isCompleted && new Date() <= challenge.endDate
      case 'completed':
        return challenge.isCompleted
      case 'upcoming':
        return new Date() < challenge.startDate
      default:
        return true
    }
  })

  const getProgressPercentage = (progress: number, maxProgress: number) => {
    return Math.min((progress / maxProgress) * 100, 100)
  }

  if (profileLoading || loading) {
    return (
      <ProfileLayout
        navigation="consolidated"
      >
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout
      navigation="consolidated"
    >
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-white">Community Challenges</h1>
          <p className="text-gray-400">Participate in challenges and earn rewards</p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Active Challenges</p>
                <p className="text-2xl font-bold text-white">
                  {challenges.filter(c => !c.isCompleted && new Date() <= c.endDate).length}
                </p>
              </div>
              <Target className="text-accent-500" size={24} />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Completed</p>
                <p className="text-2xl font-bold text-white">
                  {challenges.filter(c => c.isCompleted).length}
                </p>
              </div>
              <CheckCircle className="text-green-500" size={24} />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Rewards</p>
                <p className="text-2xl font-bold text-white">
                  {challenges.filter(c => c.isCompleted).reduce((sum, c) => sum + c.reward.points, 0)}
                </p>
              </div>
              <Trophy className="text-yellow-500" size={24} />
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
          {[
            { id: 'active', label: 'Active', count: challenges.filter(c => !c.isCompleted && new Date() <= c.endDate).length },
            { id: 'completed', label: 'Completed', count: challenges.filter(c => c.isCompleted).length },
            { id: 'upcoming', label: 'Upcoming', count: challenges.filter(c => new Date() < c.startDate).length }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-accent-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>

        {/* Challenges List */}
        {filteredChallenges.length === 0 ? (
          <div className="text-center py-12">
            <Target className="mx-auto text-gray-600 mb-4" size={48} />
            <h3 className="text-lg font-medium text-gray-300 mb-2">No Challenges Available</h3>
            <p className="text-gray-500">
              {activeTab === 'active' && 'No active challenges at the moment.'}
              {activeTab === 'completed' && 'You haven\'t completed any challenges yet.'}
              {activeTab === 'upcoming' && 'No upcoming challenges scheduled.'}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredChallenges.map((challenge) => (
              <motion.div
                key={challenge.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`bg-gray-800 rounded-lg p-6 border border-gray-700 ${
                  challenge.isCompleted ? 'opacity-75' : ''
                }`}
              >
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                  <div className="flex-1">
                    {/* Challenge Header */}
                    <div className="flex items-start gap-3 mb-3">
                      <div className="flex-shrink-0 mt-1">
                        {getCategoryIcon(challenge.category)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-white">{challenge.title}</h3>
                          {challenge.isCompleted && (
                            <CheckCircle className="text-green-500" size={16} />
                          )}
                        </div>
                        <p className="text-sm text-gray-400 mb-3">{challenge.description}</p>
                        
                        {/* Challenge Meta */}
                        <div className="flex flex-wrap gap-2 mb-4">
                          <div className="flex items-center gap-1">
                            {getTypeIcon(challenge.type)}
                            <span className="text-xs text-gray-500 capitalize">{challenge.type}</span>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getDifficultyColor(challenge.difficulty)}`}>
                            {challenge.difficulty}
                          </span>
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Users size={12} />
                            <span>{challenge.participants.toLocaleString()} participants</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    {!challenge.isCompleted && (
                      <div className="mb-4">
                        <div className="flex justify-between text-sm mb-2">
                          <span className="text-gray-400">Progress</span>
                          <span className="text-white">{challenge.progress}/{challenge.maxProgress}</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-accent-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${getProgressPercentage(challenge.progress, challenge.maxProgress)}%` }}
                          />
                        </div>
                      </div>
                    )}

                    {/* Time Remaining */}
                    <div className="flex items-center gap-1 text-sm text-gray-500">
                      <Clock size={14} />
                      <span>
                        {challenge.isCompleted 
                          ? `Completed ${challenge.endDate.toLocaleDateString()}`
                          : `Ends ${challenge.endDate.toLocaleDateString()}`}
                      </span>
                    </div>
                  </div>

                  {/* Rewards */}
                  <div className="flex-shrink-0 text-right">
                    <div className="bg-gray-700 rounded-lg p-4 min-w-[120px]">
                      <p className="text-xs text-gray-400 mb-2">Rewards</p>
                      <div className="space-y-2">
                        <div className="flex items-center justify-end gap-1">
                          <Star className="text-yellow-500" size={14} />
                          <span className="text-sm font-medium text-white">{challenge.reward.points} pts</span>
                        </div>
                        {challenge.reward.badge && (
                          <div className="flex items-center justify-end gap-1">
                            <Award className="text-purple-500" size={14} />
                            <span className="text-xs text-gray-300">{challenge.reward.badge}</span>
                          </div>
                        )}
                        {challenge.reward.title && (
                          <div className="flex items-center justify-end gap-1">
                            <Trophy className="text-yellow-500" size={14} />
                            <span className="text-xs text-gray-300">{challenge.reward.title}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </ProfileLayout>
  )
}
