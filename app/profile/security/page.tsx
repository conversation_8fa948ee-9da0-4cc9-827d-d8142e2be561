'use client'

/**
 * Security Settings Page
 *
 * Comprehensive security management interface for users including:
 * - Multi-Factor Authentication setup and management
 * - Trusted device management
 * - Security activity log
 * - Password management
 * - Account security overview
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Shield,
  Smartphone,
  Key,
  Eye,
  Clock,
  AlertTriangle,
  CheckCircle,
  Settings,
  Trash2,
  Plus,
  Download,
  RefreshCw,
  Lock,
  EyeOff,
  Save,
  Monitor,
  MapPin
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import MFASetupModal from '@/components/security/MFASetupModal'
import SecurePasswordInput from '@/components/security/SecurePasswordInput'
import PhoneManagement from '@/components/security/PhoneManagement'
import SecurityScoreDashboard from '@/components/security/SecurityScoreDashboard'
import { SecurityLog } from '@/types/profile'
import { useUser } from '@/lib/useUser'
import { doc, updateDoc, serverTimestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { updatePassword, EmailAuthProvider, reauthenticateWithCredential } from 'firebase/auth'
import { auth } from '@/lib/firebase'
import { TrustedDevice, MFASettings } from '@/types/profile'
import { PasswordSecurityService, EnhancedPasswordValidation } from '@/lib/security/passwordSecurity'
import toast from 'react-hot-toast'

/**
 * Password change form data interface
 */
interface PasswordFormData {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export default function SecurityPage() {
  const { user, profile, loading } = useUser()
  const [showMFASetup, setShowMFASetup] = useState(false)
  const [mfaSettings, setMfaSettings] = useState<MFASettings | null>(null)
  const [trustedDevices, setTrustedDevices] = useState<TrustedDevice[]>([])
  const [loadingAction, setLoadingAction] = useState<string | null>(null)

  // Password form state
  const [passwordForm, setPasswordForm] = useState<PasswordFormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [passwordValidation, setPasswordValidation] = useState<EnhancedPasswordValidation | null>(null)
  const [passwordChangeLoading, setPasswordChangeLoading] = useState(false)
  const [securityLogs] = useState<SecurityLog[]>([])
  
  // Derived state
  const twoFactorEnabled = mfaSettings?.enabled || false
  
  const handleToggle2FA = () => {
    setShowMFASetup(!twoFactorEnabled)
  }

  // Load MFA settings and trusted devices
  useEffect(() => {
    if (profile) {
      setMfaSettings({
        enabled: profile.mfaEnabled || false,
        setupCompleted: profile.mfaSetupCompleted || false,
        backupCodesGenerated: !!(profile.backupCodes && profile.backupCodes.length > 0),
        backupCodesUsed: profile.backupCodes ? 10 - profile.backupCodes.length : 0,
        trustedDevicesCount: profile.trustedDevices?.length || 0,
        requireForAdminActions: false, // Will be implemented later
        requireForSensitiveActions: false // Will be implemented later
      })
      setTrustedDevices(profile.trustedDevices || [])
    }
  }, [profile])

  const handleMFASetupComplete = async () => {
    setShowMFASetup(false)
    toast.success('MFA setup completed successfully!')
    // Refresh user profile to get updated MFA status
    window.location.reload()
  }

  const handleDisableMFA = async () => {
    if (!user || !confirm('Are you sure you want to disable Multi-Factor Authentication? This will make your account less secure.')) {
      return
    }

    setLoadingAction('disable-mfa')
    try {
      const userRef = doc(db, 'profiles', user.uid)
      await updateDoc(userRef, {
        mfaEnabled: false,
        mfaSetupCompleted: false,
        mfaSecret: null,
        backupCodes: [],
        updatedAt: serverTimestamp()
      })

      toast.success('MFA has been disabled')
      window.location.reload()
    } catch (error) {
      console.error('Error disabling MFA:', error)
      toast.error('Failed to disable MFA')
    } finally {
      setLoadingAction(null)
    }
  }

  const handleRemoveTrustedDevice = async (deviceId: string) => {
    if (!user || !confirm('Remove this trusted device? You will need to verify with MFA when signing in from this device.')) {
      return
    }

    setLoadingAction(`remove-device-${deviceId}`)
    try {
      const updatedDevices = trustedDevices.filter(device => device.id !== deviceId)

      const userRef = doc(db, 'profiles', user.uid)
      await updateDoc(userRef, {
        trustedDevices: updatedDevices,
        updatedAt: serverTimestamp()
      })

      setTrustedDevices(updatedDevices)
      toast.success('Trusted device removed')
    } catch (error) {
      console.error('Error removing trusted device:', error)
      toast.error('Failed to remove trusted device')
    } finally {
      setLoadingAction(null)
    }
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <Smartphone className="w-5 h-5" />
      case 'tablet':
        return <Smartphone className="w-5 h-5" />
      default:
        return <Settings className="w-5 h-5" />
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  /**
   * Handle password form input changes
   */
  const handlePasswordInputChange = (field: keyof PasswordFormData, value: string) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  /**
   * Enhanced password form validation
   */
  const validatePasswordForm = async (): Promise<string | null> => {
    if (!passwordForm.currentPassword) {
      return 'Current password is required'
    }
    if (!passwordForm.newPassword) {
      return 'New password is required'
    }
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      return 'New passwords do not match'
    }
    if (passwordForm.currentPassword === passwordForm.newPassword) {
      return 'New password must be different from current password'
    }

    // Check password security requirements
    if (!passwordValidation?.isValid) {
      if (passwordValidation?.breachCheck.isBreached) {
        return 'This password has been found in data breaches. Please choose a different password.'
      }
      if (passwordValidation?.isCommonPassword) {
        return 'This password is too common. Please choose a more unique password.'
      }
      if (passwordValidation?.errors && passwordValidation.errors.length > 0) {
        return passwordValidation.errors[0]
      }
      return 'Password does not meet security requirements'
    }

    // Check password history
    if (user) {
      const isPasswordReused = !(await PasswordSecurityService.checkPasswordHistory(user.uid, passwordForm.newPassword))
      if (isPasswordReused) {
        return 'You cannot reuse one of your recent passwords. Please choose a different password.'
      }
    }

    return null
  }

  /**
   * Handle password change with enhanced security
   */
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) return

    setPasswordChangeLoading(true)

    try {
      const validationError = await validatePasswordForm()
      if (validationError) {
        toast.error(validationError)
        return
      }

      // Re-authenticate user with current password
      const credential = EmailAuthProvider.credential(
        user.email!,
        passwordForm.currentPassword
      )
      await reauthenticateWithCredential(user, credential)

      // Update password
      await updatePassword(user, passwordForm.newPassword)

      // Update password history
      await PasswordSecurityService.updatePasswordHistory(
        user.uid,
        passwordForm.newPassword,
        '127.0.0.1', // Will be updated with actual IP
        navigator.userAgent
      )

      // Reset form
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
      setPasswordValidation(null)

      toast.success('Password updated successfully!')

      // Show security improvement message if applicable
      if (passwordValidation?.strength === 'strong') {
        toast.success('Great! Your new password is very secure.')
      }
    } catch (error: any) {
      console.error('Error updating password:', error)
      if (error.code === 'auth/wrong-password') {
        toast.error('Current password is incorrect')
      } else if (error.code === 'auth/weak-password') {
        toast.error('New password is too weak')
      } else if (error.code === 'auth/requires-recent-login') {
        toast.error('Please sign out and sign back in before changing your password')
      } else {
        toast.error('Failed to update password. Please try again.')
      }
    } finally {
      setPasswordChangeLoading(false)
    }
  }

  if (loading) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 text-gray-400 animate-spin mx-auto mb-4" />
            <p className="text-gray-400">Loading security settings...</p>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  if (!user || !profile) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <p className="text-gray-400">Please log in to manage your security settings.</p>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout navigation="consolidated">
      <div className="space-y-6">
        {/* Security Score Dashboard */}
        <SecurityScoreDashboard
          profile={profile}
          onActionClick={(action) => {
            // Handle quick actions from security dashboard
            switch (action) {
              case 'setup-mfa':
                setShowMFAModal(true)
                break
              case 'update-password':
                // Focus password section
                document.getElementById('password')?.scrollIntoView({ behavior: 'smooth' })
                break
              default:
                console.log('Security action:', action)
            }
          }}
        />
        {/* Page Header */}
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <Shield className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">Security Settings</h1>
              <p className="text-gray-400">Manage your account security and authentication methods</p>
            </div>
          </div>

          {/* Security Score */}
          <div className="bg-gray-900 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-white font-medium">Security Score</span>
              <span className="text-2xl font-bold text-green-400">
                {mfaSettings?.enabled ? '85' : '45'}/100
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-500 ${
                  mfaSettings?.enabled ? 'bg-green-500 w-[85%]' : 'bg-yellow-500 w-[45%]'
                }`}
              />
            </div>
            <p className="text-gray-400 text-sm mt-2">
              {mfaSettings?.enabled
                ? 'Great! Your account is well protected.'
                : 'Consider enabling MFA to improve your security score.'
              }
            </p>
          </div>
        </div>

        {/* Multi-Factor Authentication */}
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                mfaSettings?.enabled ? 'bg-green-600' : 'bg-gray-600'
              }`}>
                <Smartphone className="w-4 h-4 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-white">Multi-Factor Authentication</h2>
                <p className="text-gray-400 text-sm">
                  Add an extra layer of security to your account
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {mfaSettings?.enabled ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900 text-green-300">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Enabled
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900 text-yellow-300">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Disabled
                </span>
              )}
            </div>
          </div>

          {mfaSettings?.enabled ? (
            <div className="space-y-4">
              <div className="bg-gray-900 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">
                      {mfaSettings.backupCodesGenerated ? '✓' : '✗'}
                    </div>
                    <div className="text-sm text-gray-400">Backup Codes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">
                      {10 - mfaSettings.backupCodesUsed}
                    </div>
                    <div className="text-sm text-gray-400">Codes Remaining</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">
                      {mfaSettings.trustedDevicesCount}
                    </div>
                    <div className="text-sm text-gray-400">Trusted Devices</div>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleDisableMFA}
                  disabled={loadingAction === 'disable-mfa'}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors disabled:opacity-50"
                >
                  {loadingAction === 'disable-mfa' ? 'Disabling...' : 'Disable MFA'}
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-yellow-400 font-medium text-sm">Recommended</p>
                    <p className="text-yellow-300 text-sm">
                      Enable MFA to significantly improve your account security. This prevents unauthorized access even if your password is compromised.
                    </p>
                  </div>
                </div>
              </div>

              <button
                onClick={() => setShowMFASetup(true)}
                className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Enable Multi-Factor Authentication
              </button>
            </div>
          )}
        </div>

        {/* Trusted Devices */}
        {mfaSettings?.enabled && trustedDevices.length > 0 && (
          <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
            <h2 className="text-lg font-semibold text-white mb-4">Trusted Devices</h2>
            <div className="space-y-3">
              {trustedDevices.map((device) => (
                <div key={device.id} className="bg-gray-900 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="text-gray-400">
                        {getDeviceIcon(device.deviceType)}
                      </div>
                      <div>
                        <div className="text-white font-medium">{device.deviceName}</div>
                        <div className="text-gray-400 text-sm">
                          Added {formatDate(device.addedAt)} • Last used {formatDate(device.lastUsed)}
                        </div>
                        <div className="text-gray-500 text-xs">
                          {device.ipAddress} • {device.location || 'Unknown location'}
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => handleRemoveTrustedDevice(device.id)}
                      disabled={loadingAction === `remove-device-${device.id}`}
                      className="p-2 text-gray-400 hover:text-red-400 transition-colors disabled:opacity-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Phone Number Management */}
        <PhoneManagement />

        {/* Password Change */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.0 }}
          className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6"
        >
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
              <Lock className="w-4 h-4 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-white">Change Password</h2>
              <p className="text-gray-400 text-sm">Update your account password</p>
            </div>
          </div>

          <form onSubmit={handlePasswordChange} className="space-y-6">
            {/* Current Password */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Current Password *
              </label>
              <div className="relative">
                <input
                  type={showCurrentPassword ? 'text' : 'password'}
                  required
                  value={passwordForm.currentPassword}
                  onChange={(e) => handlePasswordInputChange('currentPassword', e.target.value)}
                  className="w-full px-4 py-3 pr-12 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter your current password"
                  autoComplete="current-password"
                />
                <button
                  type="button"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white transition-colors"
                >
                  {showCurrentPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* New Password with Security Analysis */}
            <SecurePasswordInput
              value={passwordForm.newPassword}
              onChange={(value) => handlePasswordInputChange('newPassword', value)}
              onValidationChange={setPasswordValidation}
              label="New Password"
              placeholder="Enter your new password"
              required
              showPasswordGenerator={true}
              showStrengthIndicator={true}
              showRecommendations={true}
              autoComplete="new-password"
            />

            {/* Confirm Password */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Confirm New Password *
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  required
                  value={passwordForm.confirmPassword}
                  onChange={(e) => handlePasswordInputChange('confirmPassword', e.target.value)}
                  className={`w-full px-4 py-3 pr-12 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-colors ${
                    passwordForm.confirmPassword && passwordForm.newPassword !== passwordForm.confirmPassword
                      ? 'border-red-500 focus:ring-red-500'
                      : passwordForm.confirmPassword && passwordForm.newPassword === passwordForm.confirmPassword
                      ? 'border-green-500 focus:ring-green-500'
                      : 'border-gray-600 focus:ring-blue-500'
                  }`}
                  placeholder="Confirm your new password"
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white transition-colors"
                >
                  {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {passwordForm.confirmPassword && passwordForm.newPassword !== passwordForm.confirmPassword && (
                <p className="text-red-400 text-sm mt-1">Passwords do not match</p>
              )}
              {passwordForm.confirmPassword && passwordForm.newPassword === passwordForm.confirmPassword && (
                <p className="text-green-400 text-sm mt-1">Passwords match</p>
              )}
            </div>

            {/* Security Summary */}
            {passwordValidation && passwordForm.newPassword && (
              <div className="bg-gray-900 rounded-lg p-4 border border-gray-700">
                <h4 className="text-sm font-medium text-white mb-2">Password Security Summary</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Strength:</span>
                    <span className={`ml-2 font-medium capitalize ${PasswordSecurityService.getStrengthColor(passwordValidation.strength)}`}>
                      {passwordValidation.strength}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-400">Risk Level:</span>
                    <span className={`ml-2 font-medium capitalize ${PasswordSecurityService.getRiskColor(passwordValidation.riskLevel)}`}>
                      {passwordValidation.riskLevel}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-400">Score:</span>
                    <span className="ml-2 font-medium text-white">{passwordValidation.score}/100</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Breach Check:</span>
                    <span className={`ml-2 font-medium ${passwordValidation.breachCheck.isBreached ? 'text-red-400' : 'text-green-400'}`}>
                      {passwordValidation.breachCheck.isBreached ? 'Found in breaches' : 'Clean'}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <div className="pt-4">
              <button
                type="submit"
                disabled={passwordChangeLoading || !passwordValidation?.isValid || passwordForm.newPassword !== passwordForm.confirmPassword}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {passwordChangeLoading ? (
                  <>
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    <span>Updating Password...</span>
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    <span>Update Password</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </motion.div>

        {/* Two-Factor Authentication */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 rounded-lg border border-gray-700 p-6"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Smartphone size={24} className="text-accent-400" />
              <div>
                <h2 className="text-xl font-semibold text-white">Two-Factor Authentication</h2>
                <p className="text-gray-400 text-sm">
                  Add an extra layer of security to your account
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <span className={`text-sm font-medium ${twoFactorEnabled ? 'text-green-400' : 'text-gray-400'}`}>
                {twoFactorEnabled ? 'Enabled' : 'Disabled'}
              </span>
              <button
                onClick={handleToggle2FA}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  twoFactorEnabled ? 'bg-accent-600' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    twoFactorEnabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>

          {twoFactorEnabled && (
            <div className="mt-4 p-4 bg-green-900/20 border border-green-700 rounded-lg">
              <div className="flex items-center gap-2 text-green-400">
                <CheckCircle size={16} />
                <span className="text-sm font-medium">Two-factor authentication is active</span>
              </div>
              <p className="text-green-300 text-xs mt-1">
                Your account is protected with an additional security layer
              </p>
            </div>
          )}
        </motion.div>

        {/* Security Logs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 rounded-lg border border-gray-700 p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Shield size={24} className="text-accent-400" />
              <h2 className="text-xl font-semibold text-white">Recent Security Activity</h2>
            </div>
            <button className="text-accent-400 hover:text-accent-300 transition-colors text-sm font-medium flex items-center gap-1">
              <RefreshCw size={14} />
              Refresh
            </button>
          </div>

          <div className="space-y-4">
            {securityLogs.map((log: SecurityLog) => (
              <div
                key={log.id}
                className="flex items-center justify-between p-4 bg-gray-700 rounded-lg border border-gray-600"
              >
                <div className="flex items-center gap-4">
                  <div className={`p-2 rounded-full ${
                    log.success ? 'bg-green-900/30 text-green-400' : 'bg-red-900/30 text-red-400'
                  }`}>
                    {log.action === 'Login' ? (
                      <Monitor size={16} />
                    ) : log.action === 'Password Change' ? (
                      <Key size={16} />
                    ) : (
                      <Shield size={16} />
                    )}
                  </div>

                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium text-white">{log.action}</h3>
                      {log.success ? (
                        <CheckCircle size={14} className="text-green-400" />
                      ) : (
                        <AlertTriangle size={14} className="text-red-400" />
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-400 mt-1">
                      <div className="flex items-center gap-1">
                        <Clock size={12} />
                        {log.timestamp.toLocaleDateString()} at {log.timestamp.toLocaleTimeString()}
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin size={12} />
                        {log.location}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-right">
                  <p className="text-gray-300 text-sm">{log.device}</p>
                  <p className="text-gray-400 text-xs">{log.ipAddress}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 text-center">
            <button className="text-accent-400 hover:text-accent-300 transition-colors text-sm font-medium">
              View All Security Activity
            </button>
          </div>
        </motion.div>

        {/* Account Security Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 rounded-lg border border-gray-700 p-6"
        >
          <div className="flex items-center gap-3 mb-6">
            <Shield size={24} className="text-accent-400" />
            <h2 className="text-xl font-semibold text-white">Security Status</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Email Verification */}
            <div className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-full ${
                  user.emailVerified ? 'bg-green-900/30 text-green-400' : 'bg-yellow-900/30 text-yellow-400'
                }`}>
                  <CheckCircle size={16} />
                </div>
                <div>
                  <h3 className="font-medium text-white">Email Verification</h3>
                  <p className="text-sm text-gray-400">
                    {user.emailVerified ? 'Verified' : 'Not verified'}
                  </p>
                </div>
              </div>
              {!user.emailVerified && (
                <button className="text-accent-400 hover:text-accent-300 text-sm font-medium">
                  Verify
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </div>

      {/* MFA Setup Modal */}
      <MFASetupModal
        isOpen={showMFASetup}
        onClose={() => setShowMFASetup(false)}
        onComplete={handleMFASetupComplete}
      />
    </ProfileLayout>
  )
}
