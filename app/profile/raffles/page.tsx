'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Trophy,
  Calendar,
  Clock,
  DollarSign,
  Users,
  Star,
  Gift,
  AlertCircle,
  CheckCircle,
  XCircle,
  Filter,
  Search
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'
import { db } from '@/lib/firebase'
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore'

// Interface for raffle entry display
interface RaffleEntryDisplay {
  id: string
  raffleId: string
  userId: string
  userEmail: string
  userName: string
  status: string
  products: Array<{
    id: string
    name: string
    price: number
    image: string
  }>
  entryDate: Date
  shippingMethod?: string
  shippingCost?: number
  paymentDeadline?: any
  [key: string]: any
}

const statusConfig = {
  active: {
    label: 'Active',
    color: 'bg-blue-900/30 text-blue-400 border-blue-700',
    icon: Clock
  },
  pending: {
    label: 'Draw Pending',
    color: 'bg-yellow-900/30 text-yellow-400 border-yellow-700',
    icon: AlertCircle
  },
  confirmed: {
    label: 'Confirmed',
    color: 'bg-blue-900/30 text-blue-400 border-blue-700',
    icon: CheckCircle
  },
  won: {
    label: 'Won!',
    color: 'bg-green-900/30 text-green-400 border-green-700',
    icon: Trophy
  },
  winner: {
    label: 'Winner!',
    color: 'bg-green-900/30 text-green-400 border-green-700',
    icon: Trophy
  },
  lost: {
    label: 'Not Selected',
    color: 'bg-gray-600/30 text-gray-400 border-gray-600',
    icon: XCircle
  },
  paid: {
    label: 'Paid',
    color: 'bg-purple-900/30 text-purple-400 border-purple-700',
    icon: CheckCircle
  }
}

export default function RaffleEntriesPage() {
  const { user, profile } = useUser()
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [raffleEntries, setRaffleEntries] = useState<RaffleEntryDisplay[]>([])
  const [loading, setLoading] = useState(true)

  // Load user's raffle entries from Firestore
  useEffect(() => {
    const loadRaffleEntries = async () => {
      if (!user) {
        setLoading(false)
        return
      }

      try {
        console.log('🎲 Loading raffle entries for user:', user.uid)

        // Simplified query to avoid index requirement
        const raffleEntriesQuery = query(
          collection(db, 'raffle_entries'),
          where('userId', '==', user.uid)
        )

        const snapshot = await getDocs(raffleEntriesQuery)
        console.log('📊 Found', snapshot.size, 'raffle entries')
        const entries = []

        for (const doc of snapshot.docs) {
          const entryData = doc.data()
          console.log('📋 Raffle entry:', entryData)

          // Get product details for each entry
          const products = []
          for (const productId of entryData.productIds || []) {
            try {
              const { getProduct } = await import('@/lib/firestore')
              const product = await getProduct(productId)
              if (product) {
                products.push(product)
              } else {
                console.warn('Product not found:', productId)
                // Add placeholder product for missing products
                products.push({
                  id: productId,
                  name: `Product ${productId}`,
                  price: 0,
                  image: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=200&h=200&fit=crop'
                })
              }
            } catch (error) {
              console.error('Error loading product:', productId, error)
              // Add placeholder product for error cases
              products.push({
                id: productId,
                name: `Product ${productId}`,
                price: 0,
                image: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=200&h=200&fit=crop'
              })
            }
          }

          // Safely convert createdAt to Date
          let entryDate = new Date()
          if (entryData.createdAt) {
            if (typeof entryData.createdAt.toDate === 'function') {
              entryDate = entryData.createdAt.toDate()
            } else if (entryData.createdAt instanceof Date) {
              entryDate = entryData.createdAt
            } else if (typeof entryData.createdAt === 'string') {
              entryDate = new Date(entryData.createdAt)
            }
          }

          entries.push({
            id: doc.id,
            raffleId: entryData.raffleId || '',
            userId: entryData.userId || '',
            userEmail: entryData.userEmail || '',
            userName: entryData.userName || '',
            status: entryData.status || 'pending',
            products: products,
            entryDate: entryDate,
            shippingMethod: entryData.shippingMethod,
            shippingCost: entryData.shippingCost,
            paymentDeadline: entryData.paymentDeadline,
            ...entryData
          })
        }

        // Sort entries by date (newest first) since we can't use orderBy in query
        entries.sort((a, b) => {
          const aTime = a.entryDate instanceof Date ? a.entryDate.getTime() : 0
          const bTime = b.entryDate instanceof Date ? b.entryDate.getTime() : 0
          return bTime - aTime
        })

        console.log('✅ Loaded raffle entries:', entries)
        setRaffleEntries(entries)
      } catch (error) {
        console.error('❌ Error loading raffle entries:', error)
        setRaffleEntries([])
      } finally {
        setLoading(false)
      }
    }

    loadRaffleEntries()
  }, [user])

  if (!user) {
    return (
      <ProfileLayout navigation="consolidated">
        <div className="text-center py-12">
          <Trophy className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">Please log in</h3>
          <p className="text-gray-400">You need to be logged in to view your raffle entries.</p>
        </div>
      </ProfileLayout>
    )
  }

  const filteredEntries = raffleEntries.filter(entry => {
    const matchesStatus = selectedStatus === 'all' || entry.status === selectedStatus
    const matchesSearch = entry.products.some(product =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase())
    )
    return matchesStatus && matchesSearch
  })

  const stats = {
    total: raffleEntries.length,
    won: raffleEntries.filter(e => e.status === 'won' || e.status === 'winner').length,
    active: raffleEntries.filter(e => e.status === 'active' || e.status === 'pending' || e.status === 'confirmed').length,
    winRate: raffleEntries.length > 0 ?
      (raffleEntries.filter(e => e.status === 'won' || e.status === 'winner').length / raffleEntries.length * 100).toFixed(1) : 0
  }

  // Handle payment for winner
  const handlePayment = async (entry: RaffleEntryDisplay, paymentMethod: string) => {
    try {
      console.log('💳 Processing payment for entry:', entry.id, 'Method:', paymentMethod)

      // Calculate total amount (products + shipping)
      const productTotal = entry.products.reduce((sum: number, product: any) => sum + (product.price || 0), 0)
      const shippingCost = entry.shippingCost || 0
      const totalAmount = productTotal + shippingCost

      // For demo purposes, simulate payment processing
      if (paymentMethod === 'paypal') {
        // In a real app, integrate with PayPal API
        alert(`PayPal payment of $${totalAmount.toFixed(2)} would be processed here.\n\nEntry ID: ${entry.id}\nTotal: $${totalAmount.toFixed(2)}`)
      } else if (paymentMethod === 'stripe') {
        // In a real app, integrate with Stripe API
        alert(`Stripe payment of $${totalAmount.toFixed(2)} would be processed here.\n\nEntry ID: ${entry.id}\nTotal: $${totalAmount.toFixed(2)}`)
      } else if (paymentMethod === 'bank') {
        // Show bank transfer details
        alert(`Bank Transfer Details:\n\nAmount: $${totalAmount.toFixed(2)}\nReference: RAF-${entry.id.slice(-8)}\n\nBank details would be shown here.`)
      }

    } catch (error) {
      console.error('❌ Payment error:', error)
      alert('Payment processing failed. Please try again.')
    }
  }

  const StatusIcon = ({ status }: { status: string }) => {
    const Icon = (statusConfig as any)[status]?.icon || Trophy
    return <Icon size={16} />
  }

  return (
    <ProfileLayout navigation="consolidated">
      <div className="space-y-6">
        {/* Header & Stats */}
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-white truncate">
                Raffle Entries
              </h1>
              <p className="text-gray-400 mt-1">
                Track your raffle participation and wins
              </p>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Trophy className="text-accent-500" size={20} />
                <span className="text-sm text-gray-400">Total Entries</span>
              </div>
              <p className="text-2xl font-bold text-white">{stats.total}</p>
            </div>
            <div className="bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Star className="text-green-500" size={20} />
                <span className="text-sm text-gray-400">Wins</span>
              </div>
              <p className="text-2xl font-bold text-green-400">{stats.won}</p>
            </div>
            <div className="bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="text-blue-500" size={20} />
                <span className="text-sm text-gray-400">Active</span>
              </div>
              <p className="text-2xl font-bold text-blue-400">{stats.active}</p>
            </div>
            <div className="bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Gift className="text-purple-500" size={20} />
                <span className="text-sm text-gray-400">Win Rate</span>
              </div>
              <p className="text-2xl font-bold text-purple-400">{stats.winRate}%</p>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search raffles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-500"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="confirmed">Confirmed</option>
                <option value="pending">Draw Pending</option>
                <option value="winner">Winner</option>
                <option value="won">Won</option>
                <option value="lost">Not Selected</option>
                <option value="paid">Paid</option>
              </select>
            </div>
          </div>
        </div>

        {/* Raffle Entries List */}
        <div className="space-y-4">
          {loading ? (
            <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-12 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto mb-4"></div>
              <h3 className="text-lg font-medium text-white mb-2">Loading your raffle entries...</h3>
              <p className="text-gray-400">Please wait while we fetch your data</p>
            </div>
          ) : filteredEntries.length === 0 ? (
            <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-12 text-center">
              <Trophy className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No raffle entries found</h3>
              <p className="text-gray-400">
                {searchQuery || selectedStatus !== 'all'
                  ? 'Try adjusting your filters'
                  : 'You haven\'t entered any raffles yet. Start by joining a raffle!'
                }
              </p>
              {!searchQuery && selectedStatus === 'all' && (
                <a
                  href="/shop?category=raffle"
                  className="inline-block mt-4 bg-accent-600 hover:bg-accent-700 text-white px-6 py-2 rounded-lg transition-colors"
                >
                  Browse Raffles
                </a>
              )}
            </div>
          ) : (
            filteredEntries.map((entry) => (
              <motion.div
                key={entry.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6"
              >
                <div className="flex flex-col space-y-4">
                  {/* Entry Header */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1">
                        Raffle Entry #{entry.id.slice(-8)}
                      </h3>
                      <p className="text-sm text-gray-400">
                        Submitted on {(() => {
                          let dateObj = entry.entryDate
                          if (typeof dateObj === 'string') dateObj = new Date(dateObj)
                          if (dateObj && typeof (dateObj as any).toDate === 'function') dateObj = (dateObj as any).toDate()
                          return dateObj instanceof Date && !isNaN(dateObj.getTime())
                            ? dateObj.toLocaleDateString()
                            : 'Invalid date'
                        })()}
                      </p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium border flex items-center space-x-1 ${(statusConfig as any)[entry.status]?.color}`}>
                      <StatusIcon status={entry.status} />
                      <span>{(statusConfig as any)[entry.status]?.label}</span>
                    </span>
                  </div>

                  {/* Products Grid */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-300 mb-3">
                      Products ({entry.products.length})
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {entry.products.map((product, productIndex) => (
                        <div key={`${entry.id}-${product.id}`} className="bg-gray-700/50 rounded-lg p-3">
                          <div className="flex items-center space-x-3">
                            <img
                              src={product.image || 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=200&h=200&fit=crop'}
                              alt={product.name}
                              className="w-12 h-12 rounded-lg object-cover"
                            />
                            <div className="flex-1 min-w-0">
                              <h5 className="text-sm font-medium text-white truncate">
                                {product.name}
                              </h5>
                              <p className="text-xs text-accent-400 font-semibold">
                                ${typeof product.price === 'number' ? product.price.toFixed(2) : '0.00'}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Entry Details */}
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-gray-700">
                    <div>
                      <p className="text-xs text-gray-400 mb-1">Total Products</p>
                      <p className="text-sm text-white font-medium">
                        {entry.products.length}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">Total Value</p>
                      <p className="text-sm font-semibold text-accent-400">
                        ${entry.products.reduce((sum, product) => sum + (product.price || 0), 0).toFixed(2)}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">Shipping Method</p>
                      <p className="text-sm text-white">
                        {entry.shippingMethod || 'Standard'}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">Shipping Cost</p>
                      <p className="text-sm text-white">
                        ${(entry.shippingCost || 0).toFixed(2)}
                      </p>
                    </div>
                  </div>

                  {/* Winner Status */}
                  {(entry.status === 'won' || entry.status === 'winner') && (
                    <div className="bg-green-900/20 border border-green-700 rounded-lg p-6">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                          <Trophy className="text-white" size={20} />
                        </div>
                        <div className="flex-1">
                          <h4 className="text-green-400 font-semibold text-lg">🎉 Congratulations!</h4>
                          <p className="text-green-200 text-sm">
                            You won this raffle! Complete your payment to claim your prize.
                          </p>
                        </div>
                      </div>

                      {/* Payment Deadline */}
                      {entry.paymentDeadline && (
                        <div className="bg-yellow-900/20 border border-yellow-600 rounded-lg p-3 mb-4">
                          <div className="flex items-center space-x-2">
                            <Clock className="text-yellow-400" size={16} />
                            <span className="text-yellow-300 text-sm font-medium">
                              Payment Deadline: {new Date(entry.paymentDeadline.toDate()).toLocaleString()}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Order Summary */}
                      <div className="bg-gray-800/50 rounded-lg p-4 mb-4">
                        <h5 className="text-white font-medium mb-3">Order Summary</h5>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-300">Products ({entry.products.length})</span>
                            <span className="text-white">
                              ${entry.products.reduce((sum, product) => sum + (product.price || 0), 0).toFixed(2)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-300">Shipping ({entry.shippingMethod || 'Standard'})</span>
                            <span className="text-white">${(entry.shippingCost || 0).toFixed(2)}</span>
                          </div>
                          <div className="border-t border-gray-600 pt-2 flex justify-between font-semibold">
                            <span className="text-white">Total</span>
                            <span className="text-green-400">
                              ${(entry.products.reduce((sum, product) => sum + (product.price || 0), 0) + (entry.shippingCost || 0)).toFixed(2)}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Payment Options */}
                      <div className="space-y-3">
                        <h5 className="text-white font-medium">Choose Payment Method</h5>
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                          <button
                            onClick={() => handlePayment(entry, 'paypal')}
                            className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-colors"
                          >
                            <DollarSign size={16} />
                            <span>PayPal</span>
                          </button>
                          <button
                            onClick={() => handlePayment(entry, 'stripe')}
                            className="flex items-center justify-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-colors"
                          >
                            <DollarSign size={16} />
                            <span>Credit Card</span>
                          </button>
                          <button
                            onClick={() => handlePayment(entry, 'bank')}
                            className="flex items-center justify-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-lg transition-colors"
                          >
                            <DollarSign size={16} />
                            <span>Bank Transfer</span>
                          </button>
                        </div>
                      </div>

                      {/* Additional Info */}
                      <div className="mt-4 p-3 bg-blue-900/20 border border-blue-600 rounded-lg">
                        <p className="text-blue-200 text-xs">
                          💡 <strong>Note:</strong> After payment confirmation, your order will be processed and shipped to your registered address.
                          You'll receive tracking information via email.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>
    </ProfileLayout>
  )
}
