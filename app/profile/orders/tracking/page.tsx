/**
 * Order Tracking Page
 * 
 * Allows users to track their active orders and shipments with real-time status updates.
 * Provides detailed tracking information, estimated delivery dates, and shipment history.
 * 
 * Features:
 * - Real-time order tracking status
 * - Shipment timeline with status updates
 * - Estimated delivery dates
 * - Tracking number lookup
 * - Mobile-responsive design
 * - Integration with shipping providers
 * 
 * Route: /profile/orders/tracking
 * Authentication: Required
 * Navigation: Referenced in PROFILE_NAVIGATION.orders.tracking
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Package, 
  Truck, 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Search,
  RefreshCw,
  Calendar,
  ArrowRight
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'

// Types for order tracking
interface TrackingEvent {
  id: string
  timestamp: Date
  status: string
  location: string
  description: string
  isCompleted: boolean
}

interface TrackedOrder {
  id: string
  orderNumber: string
  trackingNumber: string
  status: 'processing' | 'shipped' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'exception'
  estimatedDelivery: Date
  carrier: string
  events: TrackingEvent[]
  items: {
    name: string
    quantity: number
    image?: string
  }[]
}

/**
 * Order Tracking Page Component
 */
export default function OrderTrackingPage() {
  const { user, profile, loading: profileLoading } = useUser()
  
  const [trackedOrders, setTrackedOrders] = useState<TrackedOrder[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [refreshing, setRefreshing] = useState(false)

  // Mock data for demonstration - replace with actual API calls
  useEffect(() => {
    const loadTrackingData = async () => {
      setLoading(true)
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock tracking data
      const mockOrders: TrackedOrder[] = [
        {
          id: '1',
          orderNumber: 'SYN-2024-001',
          trackingNumber: '1Z999AA1234567890',
          status: 'in_transit',
          estimatedDelivery: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
          carrier: 'UPS',
          items: [
            { name: 'Artisan Keycap Set - Galaxy', quantity: 1 },
            { name: 'Custom Cable - Purple', quantity: 1 }
          ],
          events: [
            {
              id: '1',
              timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
              status: 'Order Processed',
              location: 'Syndicaps Warehouse',
              description: 'Order has been processed and prepared for shipment',
              isCompleted: true
            },
            {
              id: '2',
              timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
              status: 'Shipped',
              location: 'Los Angeles, CA',
              description: 'Package has been shipped and is on its way',
              isCompleted: true
            },
            {
              id: '3',
              timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
              status: 'In Transit',
              location: 'Phoenix, AZ',
              description: 'Package is in transit to destination',
              isCompleted: true
            },
            {
              id: '4',
              timestamp: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000),
              status: 'Out for Delivery',
              location: 'Your City',
              description: 'Package is out for delivery',
              isCompleted: false
            }
          ]
        }
      ]
      
      setTrackedOrders(mockOrders)
      setLoading(false)
    }

    if (user) {
      loadTrackingData()
    }
  }, [user])

  const handleRefresh = async () => {
    setRefreshing(true)
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusIcon = (status: TrackedOrder['status']) => {
    switch (status) {
      case 'processing':
        return <Clock className="text-yellow-500" size={20} />
      case 'shipped':
      case 'in_transit':
        return <Truck className="text-blue-500" size={20} />
      case 'out_for_delivery':
        return <MapPin className="text-purple-500" size={20} />
      case 'delivered':
        return <CheckCircle className="text-green-500" size={20} />
      case 'exception':
        return <AlertCircle className="text-red-500" size={20} />
      default:
        return <Package className="text-gray-500" size={20} />
    }
  }

  const getStatusColor = (status: TrackedOrder['status']) => {
    switch (status) {
      case 'processing':
        return 'text-yellow-500'
      case 'shipped':
      case 'in_transit':
        return 'text-blue-500'
      case 'out_for_delivery':
        return 'text-purple-500'
      case 'delivered':
        return 'text-green-500'
      case 'exception':
        return 'text-red-500'
      default:
        return 'text-gray-500'
    }
  }

  const filteredOrders = trackedOrders.filter(order =>
    order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
    order.trackingNumber.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (profileLoading || loading) {
    return (
      <ProfileLayout
        navigation="consolidated"
      >
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout
      navigation="consolidated"
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-white">Order Tracking</h1>
            <p className="text-gray-400">Track your active shipments and deliveries</p>
          </div>
          
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center space-x-2 px-4 py-2 bg-accent-600 hover:bg-accent-700 disabled:opacity-50 text-white rounded-lg transition-colors"
          >
            <RefreshCw className={`${refreshing ? 'animate-spin' : ''}`} size={16} />
            <span>Refresh</span>
          </button>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search by order number or tracking number..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
          />
        </div>

        {/* Tracked Orders */}
        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <Package className="mx-auto text-gray-600 mb-4" size={48} />
            <h3 className="text-lg font-medium text-gray-300 mb-2">No Active Shipments</h3>
            <p className="text-gray-500">
              {searchQuery ? 'No orders match your search.' : 'You don\'t have any active shipments to track.'}
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {filteredOrders.map((order) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 rounded-lg p-6 border border-gray-700"
              >
                {/* Order Header */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                  <div className="flex items-center space-x-3 mb-4 sm:mb-0">
                    {getStatusIcon(order.status)}
                    <div>
                      <h3 className="font-semibold text-white">Order {order.orderNumber}</h3>
                      <p className="text-sm text-gray-400">Tracking: {order.trackingNumber}</p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className={`font-medium capitalize ${getStatusColor(order.status)}`}>
                      {order.status.replace('_', ' ')}
                    </p>
                    <div className="flex items-center text-sm text-gray-400 mt-1">
                      <Calendar size={14} className="mr-1" />
                      <span>Est. {order.estimatedDelivery.toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>

                {/* Items */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-300 mb-3">Items in this shipment:</h4>
                  <div className="space-y-2">
                    {order.items.map((item, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <span className="text-gray-300">{item.name}</span>
                        <span className="text-gray-500">Qty: {item.quantity}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Tracking Timeline */}
                <div>
                  <h4 className="text-sm font-medium text-gray-300 mb-4">Tracking History:</h4>
                  <div className="space-y-4">
                    {order.events.map((event) => (
                      <div key={event.id} className="flex items-start space-x-3">
                        <div className={`flex-shrink-0 w-3 h-3 rounded-full mt-1 ${
                          event.isCompleted ? 'bg-accent-500' : 'bg-gray-600'
                        }`} />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className={`text-sm font-medium ${
                              event.isCompleted ? 'text-white' : 'text-gray-400'
                            }`}>
                              {event.status}
                            </p>
                            <p className="text-xs text-gray-500">
                              {event.timestamp.toLocaleDateString()} {event.timestamp.toLocaleTimeString()}
                            </p>
                          </div>
                          <p className="text-sm text-gray-400 mt-1">{event.location}</p>
                          <p className="text-xs text-gray-500 mt-1">{event.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Carrier Info */}
                <div className="mt-6 pt-4 border-t border-gray-700">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Carrier:</span>
                    <span className="text-white font-medium">{order.carrier}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </ProfileLayout>
  )
}
