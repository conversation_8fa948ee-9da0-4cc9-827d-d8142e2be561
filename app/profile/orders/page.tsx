'use client'

import React, { useState, useEffect, Suspense } from 'react'
import { motion } from 'framer-motion'
import {
  Package,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  RefreshCw,
  Search
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'
import InvoiceModal from '@/components/orders/InvoiceModal'
import { getUserOrders, Order } from '@/lib/firestore'
import { useSearchParams } from 'next/navigation'

type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'

// Order interface for display (extends Firestore Order with additional display fields)
interface OrderData extends Omit<Order, 'createdAt' | 'updatedAt' | 'items'> {
  orderNumber: string
  date: string
  currency: string
  subtotal: number
  shippingCost: number
  tax: number
  discount?: number
  total: number // Add missing total property
  billing?: {
    name: string
    email: string
    phone?: string
    address: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  shippingDetails?: {
    method: string
    trackingNumber?: string
    estimatedDelivery?: string
    actualDelivery?: string
  }
  payment?: {
    method: string
    transactionId: string
  }
  items: Array<{
    productId: string
    productName: string
    name: string
    variant: string
    quantity: number
    price: number
    image: string
  }>
}

// Helper function to convert Firestore Order to OrderData for display
const convertOrderToOrderData = (order: Order): OrderData => {
  return {
    ...order,
    orderNumber: `SC-${order.id.slice(-8).toUpperCase()}`, // Generate order number from ID
    date: order.createdAt?.toDate().toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
    currency: 'USD',
    subtotal: order.totalAmount * 0.85, // Estimate subtotal (85% of total)
    shippingCost: order.totalAmount * 0.10, // Estimate shipping (10% of total)
    tax: order.totalAmount * 0.05, // Estimate tax (5% of total)
    total: order.totalAmount,
    items: order.items.map(item => ({
      productId: item.productId,
      productName: item.productName,
      name: item.productName || 'Unknown Product',
      variant: 'Standard', // Default variant
      quantity: item.quantity,
      price: item.price,
      image: '/placeholder-product.jpg' // Default image
    })),
    billing: {
      name: order.shippingAddress.name,
      email: '', // Not available in current schema
      address: order.shippingAddress.address,
      city: order.shippingAddress.city,
      state: order.shippingAddress.state,
      zipCode: order.shippingAddress.zipCode,
      country: order.shippingAddress.country,
      phone: order.shippingAddress.phone
    },
    shippingDetails: {
      method: 'Standard Shipping',
      trackingNumber: undefined, // Will be available when order is shipped
      estimatedDelivery: undefined,
      actualDelivery: undefined
    },
    payment: {
      method: order.paymentMethod,
      transactionId: order.id
    }
  }
}

const statusConfig = {
  pending: {
    label: 'Pending',
    color: 'bg-yellow-900/30 text-yellow-400 border-yellow-700',
    icon: Clock
  },
  processing: {
    label: 'Processing',
    color: 'bg-blue-900/30 text-blue-400 border-blue-700',
    icon: RefreshCw
  },
  shipped: {
    label: 'Shipped',
    color: 'bg-purple-900/30 text-purple-400 border-purple-700',
    icon: Truck
  },
  delivered: {
    label: 'Delivered',
    color: 'bg-green-900/30 text-green-400 border-green-700',
    icon: CheckCircle
  },
  cancelled: {
    label: 'Cancelled',
    color: 'bg-red-900/30 text-red-400 border-red-700',
    icon: XCircle
  }
}

function OrderManagementPage() {
  const { user } = useUser()
  const searchParams = useSearchParams()
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedOrder, setSelectedOrder] = useState<OrderData | null>(null)
  const [showInvoice, setShowInvoice] = useState(false)
  const [modalMode, setModalMode] = useState<'details' | 'invoice'>('details')
  const [orders, setOrders] = useState<OrderData[]>([])
  const [loading, setLoading] = useState(true)
  const [showConfirmation, setShowConfirmation] = useState(false)

  // Load user orders from Firestore
  useEffect(() => {
    const loadOrders = async () => {
      if (!user?.uid) {
        setLoading(false)
        return
      }

      try {
        const userOrders = await getUserOrders(user.uid)
        const orderData = userOrders.map(convertOrderToOrderData)
        setOrders(orderData)
      } catch (error) {
        console.error('Error loading orders:', error)
      } finally {
        setLoading(false)
      }
    }

    loadOrders()
  }, [user?.uid])

  // Show confirmation if ?success=1 is present
  useEffect(() => {
    if (searchParams?.get('success') === '1') {
      setShowConfirmation(true)
      const timer = setTimeout(() => setShowConfirmation(false), 7000)
      return () => clearTimeout(timer)
    }
  }, [searchParams])

  if (!user) {
    return (
      <ProfileLayout>
        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">Please log in</h3>
          <p className="text-gray-400">You need to be logged in to view your orders.</p>
        </div>
      </ProfileLayout>
    )
  }

  if (loading) {
    return (
      <ProfileLayout>
        <div className="text-center py-12">
          <RefreshCw className="mx-auto h-12 w-12 text-gray-400 mb-4 animate-spin" />
          <h3 className="text-lg font-medium text-white mb-2">Loading orders...</h3>
          <p className="text-gray-400">Please wait while we fetch your order history.</p>
        </div>
      </ProfileLayout>
    )
  }

  const filteredOrders = orders.filter(order => {
    const matchesStatus = selectedStatus === 'all' || order.status === selectedStatus
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.items.some(item => item.name.toLowerCase().includes(searchQuery.toLowerCase()))
    return matchesStatus && matchesSearch
  })

  const StatusIcon = ({ status }: { status: keyof typeof statusConfig }) => {
    const Icon = statusConfig[status]?.icon || Package
    return <Icon size={16} />
  }

  return (
    <ProfileLayout navigation="consolidated">
      <div className="space-y-6">
        {/* Order Confirmation Banner */}
        {showConfirmation && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-green-900/20 border border-green-700 text-green-200 rounded-lg p-4 flex items-center justify-between shadow-lg mb-2"
          >
            <div className="flex items-center space-x-3">
              <CheckCircle className="text-green-400" size={28} />
              <div>
                <div className="font-semibold text-green-300">Thank you! Your order was placed successfully.</div>
                <div className="text-green-200 text-sm">You will receive a confirmation email soon. Track your order status below.</div>
              </div>
            </div>
            <button
              onClick={() => setShowConfirmation(false)}
              className="ml-4 text-green-300 hover:text-green-100 focus:outline-none"
              aria-label="Close"
            >
              <XCircle size={22} />
            </button>
          </motion.div>
        )}

        {/* Header */}
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-white truncate">
                Order Management
              </h1>
              <p className="text-gray-400 mt-1">
                Track and manage your orders
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              <span className="bg-accent-900/20 text-accent-400 px-3 py-1 rounded-full text-sm border border-accent-700">
                {filteredOrders.length} Orders
              </span>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search orders..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-500"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-12 text-center">
              <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No orders found</h3>
              <p className="text-gray-400">
                {searchQuery || selectedStatus !== 'all' 
                  ? 'Try adjusting your filters' 
                  : 'You haven\'t placed any orders yet'
                }
              </p>
            </div>
          ) : (
            filteredOrders.map((order) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6"
              >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-white">
                          {order.orderNumber}
                        </h3>
                        <p className="text-sm text-gray-400">
                          Ordered on {new Date(order.date).toLocaleDateString()}
                        </p>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium border flex items-center space-x-1 ${statusConfig[order.status]?.color}`}>
                        <StatusIcon status={order.status} />
                        <span>{statusConfig[order.status]?.label}</span>
                      </span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-2">Items ({order.items.length})</h4>
                        <div className="space-y-2">
                          {order.items.slice(0, 2).map((item, index) => (
                            <div key={index} className="flex items-center space-x-3">
                              <img
                                src={item.image}
                                alt={item.name}
                                className="w-10 h-10 rounded-lg object-cover"
                              />
                              <div className="flex-1 min-w-0">
                                <p className="text-sm text-white truncate">{item.name}</p>
                                <p className="text-xs text-gray-400">{item.variant} × {item.quantity}</p>
                              </div>
                            </div>
                          ))}
                          {order.items.length > 2 && (
                            <p className="text-xs text-gray-400">
                              +{order.items.length - 2} more items
                            </p>
                          )}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-2">Order Total</h4>
                        <p className="text-xl font-bold text-white">
                          ${order.total.toFixed(2)}
                        </p>
                        {order.shippingDetails?.trackingNumber && (
                          <div className="mt-2">
                            <p className="text-xs text-gray-400">Tracking: {order.shippingDetails.trackingNumber}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 mt-4 lg:mt-0">
                    <button
                      onClick={() => {
                        setSelectedOrder(order)
                        setModalMode('invoice')
                        setShowInvoice(true)
                      }}
                      className="flex items-center justify-center space-x-2 px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors"
                    >
                      <Eye size={16} />
                      <span>View Details</span>
                    </button>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>

        {/* Invoice Modal */}
        <InvoiceModal
          order={selectedOrder}
          isOpen={showInvoice}
          mode={modalMode}
          onClose={() => {
            setShowInvoice(false)
            setSelectedOrder(null)
          }}
        />
      </div>
    </ProfileLayout>
  )
}

// Wrapper component with Suspense boundary for useSearchParams
export default function OrderManagementPageWrapper() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading orders...</p>
        </div>
      </div>
    }>
      <OrderManagementPage />
    </Suspense>
  )
}
