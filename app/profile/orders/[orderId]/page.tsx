/**
 * Individual Order Detail Page
 * 
 * Displays comprehensive details for a specific order including items,
 * shipping information, payment details, and order status tracking.
 * 
 * Features:
 * - Complete order information display
 * - Real-time order status tracking
 * - Item details with images and specifications
 * - Shipping and billing address display
 * - Payment method and transaction details
 * - Order actions (cancel, return, reorder)
 * - Mobile-responsive design
 * 
 * Route: /profile/orders/[orderId]
 * Authentication: Required
 * Navigation: Dynamic route from order history
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Package, 
  Truck, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  CreditCard,
  MapPin,
  Calendar,
  Download,
  RefreshCw,
  ShoppingCart,
  Star
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'
import { validateOrderId, logRouteValidationError } from '@/lib/utils/routeValidation'
import { notFound } from 'next/navigation'

// Types for order details
interface OrderItem {
  id: string
  name: string
  image: string
  price: number
  quantity: number
  variant?: string
  sku: string
}

interface OrderAddress {
  name: string
  street: string
  city: string
  state: string
  zipCode: string
  country: string
}

interface OrderDetails {
  id: string
  orderNumber: string
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  orderDate: Date
  estimatedDelivery?: Date
  actualDelivery?: Date
  items: OrderItem[]
  subtotal: number
  shipping: number
  tax: number
  total: number
  paymentMethod: string
  shippingAddress: OrderAddress
  billingAddress: OrderAddress
  trackingNumber?: string
  carrier?: string
  notes?: string
}

/**
 * Individual Order Detail Page Component
 */
export default function OrderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { user, profile, loading: profileLoading } = useUser()

  const [order, setOrder] = useState<OrderDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Enhanced parameter validation
  const rawOrderId = params.orderId as string

  // Validate order ID format
  const validationResult = validateOrderId(rawOrderId)

  if (!validationResult.isValid) {
    // Log validation error for monitoring
    logRouteValidationError(validationResult.error!, {
      route: '/profile/orders/[orderId]',
      value: rawOrderId,
      timestamp: new Date()
    })

    // Return 404 for invalid order IDs
    notFound()
  }

  const orderId = validationResult.sanitizedValue!

  // Mock data for demonstration - replace with actual API calls
  useEffect(() => {
    const loadOrderDetails = async () => {
      setLoading(true)
      setError(null)
      
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock order data - in real implementation, fetch by orderId
        const mockOrder: OrderDetails = {
          id: orderId,
          orderNumber: `ORD-${orderId.toUpperCase()}`,
          status: 'shipped',
          orderDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          estimatedDelivery: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
          items: [
            {
              id: '1',
              name: 'Galaxy Keycap Set - Premium Edition',
              image: '/images/keycaps/galaxy-premium.jpg',
              price: 149.99,
              quantity: 1,
              variant: 'Cherry MX Compatible',
              sku: 'GKS-PREM-001'
            },
            {
              id: '2',
              name: 'Artisan Keycap - Dragon Design',
              image: '/images/keycaps/dragon-artisan.jpg',
              price: 89.99,
              quantity: 2,
              variant: 'Resin Cast',
              sku: 'ART-DRG-002'
            }
          ],
          subtotal: 329.97,
          shipping: 15.00,
          tax: 27.60,
          total: 372.57,
          paymentMethod: 'Visa ****4242',
          shippingAddress: {
            name: 'John Doe',
            street: '123 Keyboard Lane',
            city: 'Tech City',
            state: 'CA',
            zipCode: '90210',
            country: 'United States'
          },
          billingAddress: {
            name: 'John Doe',
            street: '123 Keyboard Lane',
            city: 'Tech City',
            state: 'CA',
            zipCode: '90210',
            country: 'United States'
          },
          trackingNumber: 'TRK123456789',
          carrier: 'FedEx',
          notes: 'Please leave package at front door if no one is home.'
        }
        
        setOrder(mockOrder)
      } catch (err) {
        setError('Failed to load order details. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    if (user && orderId) {
      loadOrderDetails()
    }
  }, [user, orderId])

  const getStatusIcon = (status: OrderDetails['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="text-yellow-500" size={20} />
      case 'confirmed':
        return <CheckCircle className="text-blue-500" size={20} />
      case 'processing':
        return <Package className="text-purple-500" size={20} />
      case 'shipped':
        return <Truck className="text-green-500" size={20} />
      case 'delivered':
        return <CheckCircle className="text-green-600" size={20} />
      case 'cancelled':
        return <AlertCircle className="text-red-500" size={20} />
      default:
        return <Clock className="text-gray-500" size={20} />
    }
  }

  const getStatusColor = (status: OrderDetails['status']) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-500 bg-yellow-500/10'
      case 'confirmed':
        return 'text-blue-500 bg-blue-500/10'
      case 'processing':
        return 'text-purple-500 bg-purple-500/10'
      case 'shipped':
        return 'text-green-500 bg-green-500/10'
      case 'delivered':
        return 'text-green-600 bg-green-600/10'
      case 'cancelled':
        return 'text-red-500 bg-red-500/10'
      default:
        return 'text-gray-500 bg-gray-500/10'
    }
  }

  if (profileLoading || loading) {
    return (
      <ProfileLayout 
        
        wishlistItemCount={0}
        navigation="consolidated"
      >
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500"></div>
        </div>
      </ProfileLayout>
    )
  }

  if (error || !order) {
    return (
      <ProfileLayout 
        
        wishlistItemCount={0}
        navigation="consolidated"
      >
        <div className="text-center py-12">
          <AlertCircle className="mx-auto text-red-500 mb-4" size={48} />
          <h3 className="text-lg font-medium text-gray-300 mb-2">Order Not Found</h3>
          <p className="text-gray-500 mb-4">
            {error || 'The order you\'re looking for doesn\'t exist or you don\'t have permission to view it.'}
          </p>
          <button
            onClick={() => router.push('/profile/orders')}
            className="flex items-center space-x-2 mx-auto px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors"
          >
            <ArrowLeft size={16} />
            <span>Back to Orders</span>
          </button>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout 
      
      wishlistItemCount={0}
      navigation="consolidated"
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push('/profile/orders')}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ArrowLeft size={20} />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-white">Order Details</h1>
              <p className="text-gray-400">{order.orderNumber}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {getStatusIcon(order.status)}
            <span className={`px-3 py-1 rounded-full text-sm font-medium capitalize ${getStatusColor(order.status)}`}>
              {order.status}
            </span>
          </div>
        </div>

        {/* Order Summary */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="font-medium text-white mb-2">Order Date</h3>
              <div className="flex items-center space-x-2 text-gray-300">
                <Calendar size={16} />
                <span>{order.orderDate.toLocaleDateString()}</span>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-white mb-2">Payment Method</h3>
              <div className="flex items-center space-x-2 text-gray-300">
                <CreditCard size={16} />
                <span>{order.paymentMethod}</span>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-white mb-2">Total Amount</h3>
              <p className="text-2xl font-bold text-white">${order.total.toFixed(2)}</p>
            </div>
          </div>
        </div>

        {/* Tracking Information */}
        {order.trackingNumber && (
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 className="font-medium text-white mb-4">Tracking Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-400 mb-1">Tracking Number</p>
                <p className="font-mono text-white">{order.trackingNumber}</p>
              </div>
              <div>
                <p className="text-sm text-gray-400 mb-1">Carrier</p>
                <p className="text-white">{order.carrier}</p>
              </div>
            </div>
            {order.estimatedDelivery && (
              <div className="mt-4">
                <p className="text-sm text-gray-400 mb-1">Estimated Delivery</p>
                <p className="text-white">{order.estimatedDelivery.toLocaleDateString()}</p>
              </div>
            )}
          </div>
        )}

        {/* Order Items */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="font-medium text-white mb-4">Order Items</h3>
          <div className="space-y-4">
            {order.items.map((item) => (
              <div key={item.id} className="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-16 h-16 rounded-lg object-cover"
                  onError={(e) => {
                    e.currentTarget.src = '/images/placeholder-keycap.jpg'
                  }}
                />
                <div className="flex-1">
                  <h4 className="font-medium text-white">{item.name}</h4>
                  {item.variant && (
                    <p className="text-sm text-gray-400">{item.variant}</p>
                  )}
                  <p className="text-xs text-gray-500">SKU: {item.sku}</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-white">${item.price.toFixed(2)}</p>
                  <p className="text-sm text-gray-400">Qty: {item.quantity}</p>
                </div>
              </div>
            ))}
          </div>
          
          {/* Order Total Breakdown */}
          <div className="mt-6 pt-4 border-t border-gray-600">
            <div className="space-y-2">
              <div className="flex justify-between text-gray-300">
                <span>Subtotal</span>
                <span>${order.subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-gray-300">
                <span>Shipping</span>
                <span>${order.shipping.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-gray-300">
                <span>Tax</span>
                <span>${order.tax.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-lg font-semibold text-white pt-2 border-t border-gray-600">
                <span>Total</span>
                <span>${order.total.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Addresses */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 className="font-medium text-white mb-4 flex items-center space-x-2">
              <MapPin size={16} />
              <span>Shipping Address</span>
            </h3>
            <div className="text-gray-300 space-y-1">
              <p>{order.shippingAddress.name}</p>
              <p>{order.shippingAddress.street}</p>
              <p>
                {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
              </p>
              <p>{order.shippingAddress.country}</p>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 className="font-medium text-white mb-4 flex items-center space-x-2">
              <CreditCard size={16} />
              <span>Billing Address</span>
            </h3>
            <div className="text-gray-300 space-y-1">
              <p>{order.billingAddress.name}</p>
              <p>{order.billingAddress.street}</p>
              <p>
                {order.billingAddress.city}, {order.billingAddress.state} {order.billingAddress.zipCode}
              </p>
              <p>{order.billingAddress.country}</p>
            </div>
          </div>
        </div>

        {/* Order Actions */}
        <div className="flex flex-wrap gap-4">
          <button className="flex items-center space-x-2 px-4 py-2 bg-accent-600 hover:bg-accent-700 text-white rounded-lg transition-colors">
            <RefreshCw size={16} />
            <span>Reorder Items</span>
          </button>
          
          <button className="flex items-center space-x-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">
            <Download size={16} />
            <span>Download Invoice</span>
          </button>
          
          {order.status === 'delivered' && (
            <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
              <Star size={16} />
              <span>Leave Review</span>
            </button>
          )}
          
          {['pending', 'confirmed'].includes(order.status) && (
            <button className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
              <AlertCircle size={16} />
              <span>Cancel Order</span>
            </button>
          )}
        </div>

        {/* Order Notes */}
        {order.notes && (
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 className="font-medium text-white mb-2">Order Notes</h3>
            <p className="text-gray-300">{order.notes}</p>
          </div>
        )}
      </div>
    </ProfileLayout>
  )
}
