/**
 * Navigation Accessibility Hooks
 * 
 * Enhanced accessibility hooks specifically designed for navigation components
 * with keyboard shortcuts, focus management, and ARIA enhancements.
 * 
 * Features:
 * - Navigation-specific keyboard shortcuts
 * - Focus management for navigation elements
 * - Screen reader announcements for navigation changes
 * - Skip links and landmark navigation
 * - Roving tabindex for navigation items
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import { useEffect, useCallback, useRef, useState } from 'react'
import { useNavigation } from '@/lib/navigation/NavigationProvider'
import {
  keyboardShortcutsManager,
  focusManager,
  ariaLiveManager,
  accessibilityPreferences,
  KeyboardShortcut
} from '@/lib/navigation/AccessibilityEnhancements'

// ===== TYPES =====

export interface NavigationKeyboardShortcuts {
  toggleSearch: KeyboardShortcut
  toggleSuggestions: KeyboardShortcut
  toggleSettings: KeyboardShortcut
  closeModal: KeyboardShortcut
  nextSuggestion: KeyboardShortcut
  previousSuggestion: KeyboardShortcut
  selectSuggestion: KeyboardShortcut
  skipToMain: KeyboardShortcut
  skipToNavigation: KeyboardShortcut
}

export interface UseNavigationAccessibilityOptions {
  enableKeyboardShortcuts?: boolean
  enableFocusManagement?: boolean
  enableAnnouncements?: boolean
  enableSkipLinks?: boolean
}

// ===== MAIN HOOK =====

/**
 * Hook for navigation accessibility features
 */
export function useNavigationAccessibility(
  options: UseNavigationAccessibilityOptions = {}
) {
  const {
    enableKeyboardShortcuts = true,
    enableFocusManagement = true,
    enableAnnouncements = true,
    enableSkipLinks = true
  } = options

  const {
    state,
    toggleSearch,
    toggleSuggestions,
    setActiveCategory,
    trackBehavior
  } = useNavigation()

  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(0)
  const [skipLinksVisible, setSkipLinksVisible] = useState(false)
  const navigationRef = useRef<HTMLElement>(null)

  // ===== KEYBOARD SHORTCUTS =====

  useEffect(() => {
    if (!enableKeyboardShortcuts || !keyboardShortcutsManager || !ariaLiveManager) return

    const shortcuts: Record<string, Omit<KeyboardShortcut, 'action'> & { action: () => void }> = {
      toggleSearch: {
        key: 'k',
        ctrlKey: true,
        description: 'Toggle search',
        category: 'navigation',
        action: () => {
          toggleSearch()
          ariaLiveManager?.announce('Search toggled', 'polite')
          trackBehavior({
            type: 'keyboard_shortcut',
            data: { shortcut: 'toggle_search' }
          })
        }
      },
      toggleSuggestions: {
        key: 's',
        ctrlKey: true,
        altKey: true,
        description: 'Toggle suggestions panel',
        category: 'navigation',
        action: () => {
          toggleSuggestions()
          ariaLiveManager?.announce(
            state.showSuggestions ? 'Suggestions hidden' : 'Suggestions shown',
            'polite'
          )
          trackBehavior({
            type: 'keyboard_shortcut',
            data: { shortcut: 'toggle_suggestions' }
          })
        }
      },
      closeModal: {
        key: 'Escape',
        description: 'Close modal or overlay',
        category: 'navigation',
        action: () => {
          if (state.showSearch) {
            toggleSearch(false)
            ariaLiveManager?.announce('Search closed', 'polite')
          }
          if (skipLinksVisible) {
            setSkipLinksVisible(false)
          }
          trackBehavior({
            type: 'keyboard_shortcut',
            data: { shortcut: 'close_modal' }
          })
        }
      },
      nextSuggestion: {
        key: 'ArrowDown',
        description: 'Navigate to next suggestion',
        category: 'suggestions',
        action: () => {
          if (state.contextualSuggestions.length > 0) {
            const nextIndex = (selectedSuggestionIndex + 1) % state.contextualSuggestions.length
            setSelectedSuggestionIndex(nextIndex)
            ariaLiveManager?.announce(
              `Suggestion ${nextIndex + 1} of ${state.contextualSuggestions.length}: ${state.contextualSuggestions[nextIndex]?.item.label}`,
              'polite'
            )
          }
        }
      },
      previousSuggestion: {
        key: 'ArrowUp',
        description: 'Navigate to previous suggestion',
        category: 'suggestions',
        action: () => {
          if (state.contextualSuggestions.length > 0) {
            const prevIndex = selectedSuggestionIndex === 0
              ? state.contextualSuggestions.length - 1
              : selectedSuggestionIndex - 1
            setSelectedSuggestionIndex(prevIndex)
            ariaLiveManager?.announce(
              `Suggestion ${prevIndex + 1} of ${state.contextualSuggestions.length}: ${state.contextualSuggestions[prevIndex]?.item.label}`,
              'polite'
            )
          }
        }
      },
      selectSuggestion: {
        key: 'Enter',
        description: 'Select current suggestion',
        category: 'suggestions',
        action: () => {
          const selectedSuggestion = state.contextualSuggestions[selectedSuggestionIndex]
          if (selectedSuggestion) {
            ariaLiveManager?.announce(`Navigating to ${selectedSuggestion.item.label}`, 'assertive')
            // Navigation would be handled by parent component
            trackBehavior({
              type: 'suggestion_selected',
              data: {
                suggestionId: selectedSuggestion.item.id,
                method: 'keyboard'
              }
            })
          }
        }
      },
      skipToMain: {
        key: '1',
        altKey: true,
        description: 'Skip to main content',
        category: 'navigation',
        action: () => {
          if (typeof document === 'undefined') return
          const mainElement = document.querySelector('main') || document.querySelector('[role="main"]')
          if (mainElement) {
            (mainElement as HTMLElement).focus()
            ariaLiveManager?.announce('Skipped to main content', 'assertive')
          }
        }
      },
      skipToNavigation: {
        key: '2',
        altKey: true,
        description: 'Skip to navigation',
        category: 'navigation',
        action: () => {
          if (navigationRef.current) {
            navigationRef.current.focus()
            ariaLiveManager?.announce('Skipped to navigation', 'assertive')
          }
        }
      }
    }

    // Register shortcuts
    Object.entries(shortcuts).forEach(([id, shortcut]) => {
      keyboardShortcutsManager?.register(`nav-${id}`, {
        ...shortcut,
        category: shortcut.category as any
      })
    })

    // Cleanup
    return () => {
      Object.keys(shortcuts).forEach(id => {
        keyboardShortcutsManager?.unregister(`nav-${id}`)
      })
    }
  }, [
    enableKeyboardShortcuts,
    toggleSearch,
    toggleSuggestions,
    state.showSearch,
    state.showSuggestions,
    state.contextualSuggestions,
    selectedSuggestionIndex,
    skipLinksVisible,
    trackBehavior
  ])

  // ===== FOCUS MANAGEMENT =====

  const trapFocusInNavigation = useCallback(() => {
    if (!enableFocusManagement || !navigationRef.current || !focusManager) return

    focusManager.trapFocus(navigationRef.current, {
      restoreFocus: true,
      initialFocus: navigationRef.current.querySelector('[tabindex="0"]') as HTMLElement
    })
  }, [enableFocusManagement])

  const releaseFocusTrap = useCallback(() => {
    if (!enableFocusManagement || !focusManager) return
    focusManager.releaseFocusTrap()
  }, [enableFocusManagement])

  // ===== ANNOUNCEMENTS =====

  const announceNavigationChange = useCallback((
    newLocation: string,
    context?: string
  ) => {
    if (!enableAnnouncements || !ariaLiveManager) return

    let message = `Navigated to ${newLocation}`
    if (context) {
      message += `. ${context}`
    }

    ariaLiveManager.announce(message, 'polite')
    trackBehavior({
      type: 'navigation_announced',
      data: { location: newLocation, context }
    })
  }, [enableAnnouncements, trackBehavior])

  const announceSuggestionUpdate = useCallback((
    suggestionCount: number,
    reason?: string
  ) => {
    if (!enableAnnouncements || !ariaLiveManager) return

    let message = `${suggestionCount} navigation suggestions available`
    if (reason) {
      message += ` based on ${reason}`
    }

    ariaLiveManager.announce(message, 'polite')
  }, [enableAnnouncements])

  const announceSearchResults = useCallback((
    resultCount: number,
    query: string
  ) => {
    if (!enableAnnouncements || !ariaLiveManager) return

    const message = `${resultCount} search results found for "${query}"`
    ariaLiveManager.announce(message, 'polite')
  }, [enableAnnouncements])

  // ===== SKIP LINKS =====

  const showSkipLinks = useCallback(() => {
    if (!enableSkipLinks) return
    setSkipLinksVisible(true)
  }, [enableSkipLinks])

  const hideSkipLinks = useCallback(() => {
    if (!enableSkipLinks) return
    setSkipLinksVisible(false)
  }, [enableSkipLinks])

  // ===== ROVING TABINDEX =====

  const [rovingTabIndex, setRovingTabIndex] = useState(0)

  const updateRovingTabIndex = useCallback((
    items: HTMLElement[],
    newIndex: number
  ) => {
    if (newIndex < 0 || newIndex >= items.length) return

    // Update tabindex attributes
    items.forEach((item, index) => {
      item.setAttribute('tabindex', index === newIndex ? '0' : '-1')
    })

    setRovingTabIndex(newIndex)
    
    // Focus the new active item
    items[newIndex]?.focus()
  }, [])

  const moveRovingFocus = useCallback((
    items: HTMLElement[],
    direction: 'next' | 'previous' | 'first' | 'last'
  ) => {
    let newIndex: number

    switch (direction) {
      case 'next':
        newIndex = (rovingTabIndex + 1) % items.length
        break
      case 'previous':
        newIndex = rovingTabIndex === 0 ? items.length - 1 : rovingTabIndex - 1
        break
      case 'first':
        newIndex = 0
        break
      case 'last':
        newIndex = items.length - 1
        break
      default:
        return
    }

    updateRovingTabIndex(items, newIndex)
  }, [rovingTabIndex, updateRovingTabIndex])

  // ===== ACCESSIBILITY STATE =====

  const [accessibilityState, setAccessibilityState] = useState({
    keyboardNavigation: false,
    screenReaderMode: false,
    highContrastMode: accessibilityPreferences?.get('highContrast') || false,
    reducedMotion: accessibilityPreferences?.get('reducedMotion') || false
  })

  useEffect(() => {
    if (typeof document === 'undefined') return

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        setAccessibilityState(prev => ({ ...prev, keyboardNavigation: true }))
      }
    }

    const handleMouseDown = () => {
      setAccessibilityState(prev => ({ ...prev, keyboardNavigation: false }))
    }

    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('mousedown', handleMouseDown)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('mousedown', handleMouseDown)
    }
  }, [])

  return {
    // Refs
    navigationRef,
    
    // State
    selectedSuggestionIndex,
    setSelectedSuggestionIndex,
    skipLinksVisible,
    accessibilityState,
    
    // Focus management
    trapFocusInNavigation,
    releaseFocusTrap,
    
    // Announcements
    announceNavigationChange,
    announceSuggestionUpdate,
    announceSearchResults,
    
    // Skip links
    showSkipLinks,
    hideSkipLinks,
    
    // Roving tabindex
    updateRovingTabIndex,
    moveRovingFocus,
    rovingTabIndex,
    
    // Utilities
    getShortcutsByCategory: () => keyboardShortcutsManager?.getShortcutsByCategory() || new Map(),
    isKeyboardNavigating: accessibilityState.keyboardNavigation,
    isScreenReaderMode: accessibilityState.screenReaderMode,
    isHighContrastMode: accessibilityState.highContrastMode,
    isReducedMotion: accessibilityState.reducedMotion
  }
}

export default useNavigationAccessibility
