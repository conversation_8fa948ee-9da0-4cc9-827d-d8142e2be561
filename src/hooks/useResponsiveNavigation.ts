/**
 * Responsive Navigation Hook
 * 
 * Provides responsive breakpoint detection for navigation components.
 * Extracted from existing navigation components for reusability.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useState, useEffect } from 'react'

export interface ResponsiveNavigationState {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  width: number
  height: number
}

/**
 * Hook for responsive navigation detection
 */
export const useResponsiveNavigation = (): ResponsiveNavigationState => {
  const [state, setState] = useState<ResponsiveNavigationState>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    width: 1024,
    height: 768
  })

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      setState({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
        width,
        height
      })
    }

    // Initial check
    checkDevice()
    
    // Listen for resize events
    window.addEventListener('resize', checkDevice)
    return () => window.removeEventListener('resize', checkDevice)
  }, [])

  return state
}

/**
 * Hook for mobile-specific navigation behavior
 */
export const useMobileNavigation = () => {
  const { isMobile, isTablet } = useResponsiveNavigation()
  
  return {
    isMobile,
    isTablet,
    shouldShowBottomNav: isMobile,
    shouldShowSidebar: !isMobile,
    shouldUseCompactMode: isTablet,
    touchTargetSize: isMobile ? 44 : 32
  }
}

export default useResponsiveNavigation
