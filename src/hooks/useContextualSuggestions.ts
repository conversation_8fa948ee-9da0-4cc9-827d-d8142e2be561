/**
 * Contextual Suggestions Hook
 * 
 * React hook for automatically generating and managing contextual navigation
 * suggestions based on current page context, user behavior, and completion status.
 * 
 * Features:
 * - Automatic suggestion generation on page changes
 * - Context detection and analysis
 * - User behavior tracking integration
 * - Session management
 * - Performance optimization with debouncing
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import { useEffect, useCallback, useMemo } from 'react'
import { usePathname } from 'next/navigation'
import { useNavigation } from '@/lib/navigation/NavigationProvider'
import { SuggestionContext } from '@/lib/navigation/ContextualSuggestions'

// ===== TYPES =====

export interface UseContextualSuggestionsOptions {
  enabled?: boolean
  debounceMs?: number
  maxSuggestions?: number
  sessionId?: string
  userProfile?: {
    completionStatus?: any
    preferences?: any
    engagementLevel?: 'new' | 'casual' | 'active' | 'power'
  }
}

export interface ContextualSuggestionsHookReturn {
  suggestions: any[]
  isLoading: boolean
  refreshSuggestions: () => void
  trackPageVisit: (duration: number) => void
  trackUserAction: (action: string, data?: any) => void
}

// ===== UTILITY FUNCTIONS =====

/**
 * Extract category from current path
 */
function getCategoryFromPath(pathname: string, categories: any[]): string | undefined {
  for (const category of categories) {
    for (const item of category.items || []) {
      if (pathname.startsWith(item.href)) {
        return category.id
      }
    }
  }
  return undefined
}

/**
 * Determine user segment based on profile and behavior
 */
function getUserSegment(userProfile?: any): string {
  if (!userProfile) return 'new'
  
  const { completionStatus, engagementLevel } = userProfile
  
  if (engagementLevel) return engagementLevel
  
  if (completionStatus?.profileCompletion > 0.8) return 'active'
  if (completionStatus?.profileCompletion > 0.5) return 'casual'
  
  return 'new'
}

/**
 * Calculate session metrics
 */
function getSessionMetrics() {
  const sessionStart = sessionStorage.getItem('navigationSessionStart')
  const pagesVisited = JSON.parse(sessionStorage.getItem('navigationPagesVisited') || '[]')
  
  const startTime = sessionStart ? parseInt(sessionStart) : Date.now()
  const sessionDuration = Date.now() - startTime
  
  return {
    sessionDuration,
    pagesVisitedInSession: pagesVisited.length,
    sessionStart: startTime
  }
}

// ===== MAIN HOOK =====

export function useContextualSuggestions(
  options: UseContextualSuggestionsOptions = {}
): ContextualSuggestionsHookReturn {
  const {
    enabled = true,
    debounceMs = 1000,
    maxSuggestions = 5,
    sessionId,
    userProfile
  } = options

  const pathname = usePathname()
  const {
    state,
    generateSuggestions,
    trackBehavior
  } = useNavigation()

  // Generate session ID if not provided
  const effectiveSessionId = useMemo(() => {
    if (sessionId) return sessionId
    
    let storedSessionId = sessionStorage.getItem('navigationSessionId')
    if (!storedSessionId) {
      storedSessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('navigationSessionId', storedSessionId)
      sessionStorage.setItem('navigationSessionStart', Date.now().toString())
    }
    
    return storedSessionId
  }, [sessionId])

  // Create suggestion context
  const suggestionContext = useMemo((): SuggestionContext => {
    const now = new Date()
    const sessionMetrics = getSessionMetrics()
    
    return {
      currentPage: pathname,
      currentCategory: getCategoryFromPath(pathname, state.categories),
      timeOfDay: now.getHours(),
      dayOfWeek: now.getDay(),
      sessionDuration: sessionMetrics.sessionDuration,
      pagesVisitedInSession: sessionMetrics.pagesVisitedInSession,
      userSegment: getUserSegment(userProfile)
    }
  }, [pathname, state.categories, userProfile])

  // Track page visits
  const trackPageVisit = useCallback((duration: number) => {
    if (!enabled) return

    // Store page visit in session storage
    const pagesVisited = JSON.parse(sessionStorage.getItem('navigationPagesVisited') || '[]')
    const pageVisit = {
      href: pathname,
      timestamp: Date.now(),
      duration,
      title: document.title
    }
    
    pagesVisited.push(pageVisit)
    
    // Keep only last 20 pages
    if (pagesVisited.length > 20) {
      pagesVisited.shift()
    }
    
    sessionStorage.setItem('navigationPagesVisited', JSON.stringify(pagesVisited))

    // Track behavior
    trackBehavior({
      type: 'page_visit',
      data: pageVisit
    })
  }, [enabled, pathname, trackBehavior])

  // Track user actions
  const trackUserAction = useCallback((action: string, data?: any) => {
    if (!enabled) return

    trackBehavior({
      type: 'user_action',
      data: { action, data, timestamp: Date.now(), page: pathname }
    })
  }, [enabled, trackBehavior, pathname])

  // Generate suggestions with debouncing
  const refreshSuggestions = useCallback(() => {
    if (!enabled || !state.categories.length || !state.allItems.length) return

    const timeoutId = setTimeout(() => {
      generateSuggestions(effectiveSessionId, suggestionContext)
    }, debounceMs)

    return () => clearTimeout(timeoutId)
  }, [
    enabled,
    state.categories.length,
    state.allItems.length,
    generateSuggestions,
    effectiveSessionId,
    suggestionContext,
    debounceMs
  ])

  // Auto-generate suggestions on context changes
  useEffect(() => {
    if (!enabled) return

    const cleanup = refreshSuggestions()
    return cleanup
  }, [enabled, refreshSuggestions])

  // Track page visit duration on unmount/page change
  useEffect(() => {
    const pageStartTime = Date.now()
    
    return () => {
      const duration = Date.now() - pageStartTime
      if (duration > 1000) { // Only track if user spent more than 1 second
        trackPageVisit(duration)
      }
    }
  }, [pathname, trackPageVisit])

  // Update completion status when user profile changes
  useEffect(() => {
    if (!enabled || !userProfile) return

    trackBehavior({
      type: 'profile_update',
      data: {
        completionStatus: userProfile.completionStatus,
        preferences: userProfile.preferences,
        timestamp: Date.now()
      }
    })
  }, [enabled, userProfile, trackBehavior])

  // Cleanup session data on component unmount
  useEffect(() => {
    return () => {
      // Clean up old session data (older than 24 hours)
      const sessionStart = sessionStorage.getItem('navigationSessionStart')
      if (sessionStart) {
        const startTime = parseInt(sessionStart)
        const hoursSinceStart = (Date.now() - startTime) / (1000 * 60 * 60)
        
        if (hoursSinceStart > 24) {
          sessionStorage.removeItem('navigationSessionId')
          sessionStorage.removeItem('navigationSessionStart')
          sessionStorage.removeItem('navigationPagesVisited')
        }
      }
    }
  }, [])

  return {
    suggestions: state.contextualSuggestions.slice(0, maxSuggestions),
    isLoading: state.loading,
    refreshSuggestions,
    trackPageVisit,
    trackUserAction
  }
}

// ===== ADDITIONAL HOOKS =====

/**
 * Hook for tracking specific user interactions
 */
export function useNavigationTracking() {
  const { trackBehavior } = useNavigation()

  const trackClick = useCallback((elementId: string, context?: any) => {
    trackBehavior({
      type: 'click',
      data: { elementId, context, timestamp: Date.now() }
    })
  }, [trackBehavior])

  const trackHover = useCallback((elementId: string, duration: number) => {
    trackBehavior({
      type: 'hover',
      data: { elementId, duration, timestamp: Date.now() }
    })
  }, [trackBehavior])

  const trackScroll = useCallback((scrollDepth: number, maxScroll: number) => {
    trackBehavior({
      type: 'scroll',
      data: { scrollDepth, maxScroll, timestamp: Date.now() }
    })
  }, [trackBehavior])

  return {
    trackClick,
    trackHover,
    trackScroll
  }
}

/**
 * Hook for managing suggestion preferences
 */
export function useSuggestionPreferences() {
  const { state, trackBehavior } = useNavigation()

  const dismissSuggestion = useCallback((suggestionId: string, reason: string) => {
    trackBehavior({
      type: 'suggestion_dismissed',
      data: { suggestionId, reason, timestamp: Date.now() }
    })
  }, [trackBehavior])

  const rateSuggestion = useCallback((suggestionId: string, rating: number) => {
    trackBehavior({
      type: 'suggestion_rated',
      data: { suggestionId, rating, timestamp: Date.now() }
    })
  }, [trackBehavior])

  const updatePreferences = useCallback((preferences: any) => {
    trackBehavior({
      type: 'preferences_updated',
      data: { preferences, timestamp: Date.now() }
    })
  }, [trackBehavior])

  return {
    dismissSuggestion,
    rateSuggestion,
    updatePreferences,
    currentSuggestions: state.contextualSuggestions
  }
}

export default useContextualSuggestions
