/**
 * Navigation Migration Integration Tests
 * 
 * Tests to verify that the migration from legacy navigation components
 * to ConsolidatedNavigation was successful and all functionality works correctly.
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { NavigationProvider } from '@/lib/navigation/NavigationProvider'
import { ConsolidatedNavigation } from '@/components/navigation/ConsolidatedNavigation'
import { UserProfile } from '@/types/profile'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(() => '/profile/account'),
}))

// Mock user hook
jest.mock('@/lib/useUser', () => ({
  useUser: jest.fn(() => ({
    user: mockUser,
    profile: mockProfile,
    loading: false
  }))
}))

// Mock wishlist store
jest.mock('@/store/wishlistStore', () => ({
  useWishlistStore: jest.fn(() => ({
    items: [{ id: '1' }, { id: '2' }]
  }))
}))

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  prefetch: jest.fn(),
}

const mockUser = {
  uid: 'test-user-id',
  email: '<EMAIL>',
  displayName: 'Test User'
}

const mockProfile: UserProfile = {
  id: 'test-user-id',
  displayName: 'Test User',
  email: '<EMAIL>',
  completionPercentage: 85,
  tier: 'premium',
  points: 2450,
  avatar: '/images/test-avatar.jpg'
} as UserProfile

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <NavigationProvider profile={mockProfile} wishlistItemCount={2}>
      {component}
    </NavigationProvider>
  )
}

describe('Navigation Migration Integration Tests', () => {
  beforeEach(() => {
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    jest.clearAllMocks()
  })

  describe('NavigationProvider Integration', () => {
    it('should provide navigation context successfully', () => {
      const TestComponent = () => {
        return (
          <ConsolidatedNavigation
            profile={mockProfile}
            wishlistItemCount={2}
            loading={false}
            variant="desktop"
            showSearch={true}
            showQuickSettings={true}
            showBreadcrumbs={false}
          />
        )
      }

      renderWithProviders(<TestComponent />)
      
      // Should render navigation without errors
      expect(screen.getByRole('navigation')).toBeInTheDocument()
    })

    it('should handle user profile data correctly', () => {
      const TestComponent = () => {
        return (
          <ConsolidatedNavigation
            profile={mockProfile}
            wishlistItemCount={2}
            loading={false}
            variant="desktop"
            showSearch={true}
            showQuickSettings={true}
            showBreadcrumbs={false}
          />
        )
      }

      renderWithProviders(<TestComponent />)
      
      // Should display user information
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })
  })

  describe('ConsolidatedNavigation Functionality', () => {
    it('should render desktop navigation variant', () => {
      renderWithProviders(
        <ConsolidatedNavigation
          profile={mockProfile}
          wishlistItemCount={2}
          loading={false}
          variant="desktop"
          showSearch={true}
          showQuickSettings={true}
          showBreadcrumbs={false}
        />
      )

      const navigation = screen.getByRole('navigation')
      expect(navigation).toHaveClass('desktop-navigation')
    })

    it('should show search functionality when enabled', () => {
      renderWithProviders(
        <ConsolidatedNavigation
          profile={mockProfile}
          wishlistItemCount={2}
          loading={false}
          variant="desktop"
          showSearch={true}
          showQuickSettings={true}
          showBreadcrumbs={false}
        />
      )

      // Should have search input or button
      const searchElement = screen.getByRole('searchbox') || screen.getByLabelText(/search/i)
      expect(searchElement).toBeInTheDocument()
    })

    it('should handle navigation clicks correctly', async () => {
      renderWithProviders(
        <ConsolidatedNavigation
          profile={mockProfile}
          wishlistItemCount={2}
          loading={false}
          variant="desktop"
          showSearch={true}
          showQuickSettings={true}
          showBreadcrumbs={false}
        />
      )

      // Find and click a navigation link
      const accountLink = screen.getByText(/account/i)
      fireEvent.click(accountLink)

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalled()
      })
    })
  })

  describe('Accessibility Features', () => {
    it('should have proper ARIA labels', () => {
      renderWithProviders(
        <ConsolidatedNavigation
          profile={mockProfile}
          wishlistItemCount={2}
          loading={false}
          variant="desktop"
          showSearch={true}
          showQuickSettings={true}
          showBreadcrumbs={false}
        />
      )

      const navigation = screen.getByRole('navigation')
      expect(navigation).toHaveAttribute('aria-label')
    })

    it('should support keyboard navigation', () => {
      renderWithProviders(
        <ConsolidatedNavigation
          profile={mockProfile}
          wishlistItemCount={2}
          loading={false}
          variant="desktop"
          showSearch={true}
          showQuickSettings={true}
          showBreadcrumbs={false}
        />
      )

      const navigation = screen.getByRole('navigation')
      const firstLink = navigation.querySelector('a')
      
      if (firstLink) {
        firstLink.focus()
        expect(document.activeElement).toBe(firstLink)
      }
    })
  })

  describe('Migration Compatibility', () => {
    it('should maintain backward compatibility with legacy props', () => {
      // Test that ConsolidatedNavigation accepts legacy prop patterns
      const legacyProps = {
        profile: mockProfile,
        wishlistItemCount: 2,
        loading: false,
        variant: 'desktop' as const,
        showSearch: true,
        showQuickSettings: true,
        showBreadcrumbs: true,
        onNavigate: jest.fn()
      }

      renderWithProviders(<ConsolidatedNavigation {...legacyProps} />)
      
      expect(screen.getByRole('navigation')).toBeInTheDocument()
    })

    it('should handle missing optional props gracefully', () => {
      const minimalProps = {
        profile: mockProfile,
        wishlistItemCount: 2,
        loading: false
      }

      renderWithProviders(<ConsolidatedNavigation {...minimalProps} />)
      
      expect(screen.getByRole('navigation')).toBeInTheDocument()
    })
  })

  describe('Performance', () => {
    it('should render without performance issues', () => {
      const startTime = performance.now()
      
      renderWithProviders(
        <ConsolidatedNavigation
          profile={mockProfile}
          wishlistItemCount={2}
          loading={false}
          variant="desktop"
          showSearch={true}
          showQuickSettings={true}
          showBreadcrumbs={false}
        />
      )
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      // Should render in less than 100ms
      expect(renderTime).toBeLessThan(100)
    })
  })
})
