/**
 * Admin Security Audit System
 * 
 * Comprehensive security audit logging and monitoring system for admin operations.
 * Tracks all admin activities, security events, and potential threats.
 * 
 * Features:
 * - Comprehensive audit logging
 * - Real-time security monitoring
 * - Threat detection and alerting
 * - Activity pattern analysis
 * - Compliance reporting
 * - Automated incident response
 * 
 * <AUTHOR> Team
 */

export interface SecurityAuditEvent {
  id: string
  timestamp: Date
  eventType: 'authentication' | 'authorization' | 'data_access' | 'data_modification' | 'system_access' | 'security_violation'
  severity: 'low' | 'medium' | 'high' | 'critical'
  adminId?: string
  adminEmail?: string
  adminRole?: string
  action: string
  resource?: string
  ipAddress: string
  userAgent: string
  sessionId?: string
  success: boolean
  errorMessage?: string
  metadata: Record<string, any>
  riskScore: number
  securityFlags: string[]
  geolocation?: {
    country?: string
    region?: string
    city?: string
    timezone?: string
  }
}

export interface SecurityMetrics {
  totalEvents: number
  eventsByType: Record<string, number>
  eventsBySeverity: Record<string, number>
  failedAuthAttempts: number
  suspiciousActivities: number
  highRiskEvents: number
  uniqueAdmins: number
  uniqueIPs: number
  averageRiskScore: number
  timeRange: {
    start: Date
    end: Date
  }
}

export interface ThreatPattern {
  id: string
  name: string
  description: string
  pattern: (events: SecurityAuditEvent[]) => boolean
  severity: 'low' | 'medium' | 'high' | 'critical'
  autoResponse?: (events: SecurityAuditEvent[]) => void
}

// In-memory audit log (use database in production)
const auditLog: SecurityAuditEvent[] = []
const maxLogSize = 10000 // Keep last 10k events in memory

/**
 * Generate unique event ID
 */
function generateEventId(): string {
  return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Calculate risk score based on event characteristics
 */
function calculateRiskScore(event: Partial<SecurityAuditEvent>): number {
  let score = 0

  // Base score by event type
  switch (event.eventType) {
    case 'authentication':
      score += event.success ? 5 : 25
      break
    case 'authorization':
      score += event.success ? 10 : 30
      break
    case 'data_modification':
      score += 20
      break
    case 'system_access':
      score += 15
      break
    case 'security_violation':
      score += 50
      break
    default:
      score += 5
  }

  // Severity multiplier
  switch (event.severity) {
    case 'critical':
      score *= 3
      break
    case 'high':
      score *= 2
      break
    case 'medium':
      score *= 1.5
      break
    case 'low':
      score *= 1
      break
  }

  // Security flags impact
  if (event.securityFlags) {
    score += event.securityFlags.length * 5
    
    // Specific high-risk flags
    const highRiskFlags = ['brute_force', 'privilege_escalation', 'data_exfiltration', 'suspicious_ip']
    const hasHighRiskFlag = event.securityFlags.some(flag => highRiskFlags.includes(flag))
    if (hasHighRiskFlag) {
      score += 30
    }
  }

  // Failed action penalty
  if (!event.success) {
    score += 15
  }

  return Math.min(score, 100) // Cap at 100
}

/**
 * Log security audit event
 */
export function logSecurityEvent(eventData: Omit<SecurityAuditEvent, 'id' | 'timestamp' | 'riskScore'>): SecurityAuditEvent {
  const event: SecurityAuditEvent = {
    id: generateEventId(),
    timestamp: new Date(),
    riskScore: calculateRiskScore(eventData),
    ...eventData
  }

  // Add to audit log
  auditLog.push(event)

  // Maintain log size limit
  if (auditLog.length > maxLogSize) {
    auditLog.splice(0, auditLog.length - maxLogSize)
  }

  // Console logging with appropriate level
  const logLevel = event.severity === 'critical' || event.severity === 'high' ? 'error' : 
                   event.severity === 'medium' ? 'warn' : 'info'
  
  console[logLevel](`🔒 Security Audit [${event.severity.toUpperCase()}]:`, {
    id: event.id,
    type: event.eventType,
    action: event.action,
    admin: event.adminEmail,
    success: event.success,
    riskScore: event.riskScore,
    ip: event.ipAddress,
    flags: event.securityFlags
  })

  // Check for threat patterns
  checkThreatPatterns([event])

  // In production: send to monitoring service, database, SIEM, etc.
  
  return event
}

/**
 * Predefined threat patterns
 */
const THREAT_PATTERNS: ThreatPattern[] = [
  {
    id: 'brute_force_login',
    name: 'Brute Force Login Attempt',
    description: 'Multiple failed login attempts from same IP',
    severity: 'high',
    pattern: (events) => {
      const recentEvents = events.filter(e => 
        e.eventType === 'authentication' && 
        !e.success &&
        Date.now() - e.timestamp.getTime() < 15 * 60 * 1000 // Last 15 minutes
      )
      
      const ipCounts = recentEvents.reduce((acc, event) => {
        acc[event.ipAddress] = (acc[event.ipAddress] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      
      return Object.values(ipCounts).some(count => count >= 5)
    },
    autoResponse: (events) => {
      console.warn('🚨 THREAT DETECTED: Brute force login attempt')
      // In production: block IP, send alert, etc.
    }
  },
  {
    id: 'privilege_escalation',
    name: 'Privilege Escalation Attempt',
    description: 'Admin attempting to access resources beyond their role',
    severity: 'critical',
    pattern: (events) => {
      return events.some(e => 
        e.eventType === 'authorization' && 
        !e.success &&
        e.securityFlags.includes('insufficient_permissions') &&
        e.adminRole === 'admin' &&
        e.resource?.includes('superadmin')
      )
    },
    autoResponse: (events) => {
      console.error('🚨 CRITICAL THREAT: Privilege escalation attempt detected')
      // In production: suspend account, send immediate alert
    }
  },
  {
    id: 'suspicious_data_access',
    name: 'Suspicious Data Access Pattern',
    description: 'Unusual data access patterns indicating potential data exfiltration',
    severity: 'high',
    pattern: (events) => {
      const dataAccessEvents = events.filter(e => 
        e.eventType === 'data_access' &&
        Date.now() - e.timestamp.getTime() < 60 * 60 * 1000 // Last hour
      )
      
      return dataAccessEvents.length > 100 // More than 100 data access events in an hour
    },
    autoResponse: (events) => {
      console.warn('🚨 THREAT DETECTED: Suspicious data access pattern')
      // In production: rate limit, require additional authentication
    }
  },
  {
    id: 'off_hours_access',
    name: 'Off-Hours Admin Access',
    description: 'Admin access during unusual hours',
    severity: 'medium',
    pattern: (events) => {
      return events.some(e => {
        const hour = e.timestamp.getHours()
        return (hour < 6 || hour > 22) && e.eventType === 'authentication' && e.success
      })
    }
  }
]

/**
 * Check events against threat patterns
 */
function checkThreatPatterns(newEvents: SecurityAuditEvent[]): void {
  // Get recent events for pattern analysis
  const recentEvents = auditLog.filter(e => 
    Date.now() - e.timestamp.getTime() < 24 * 60 * 60 * 1000 // Last 24 hours
  )

  for (const pattern of THREAT_PATTERNS) {
    try {
      if (pattern.pattern([...recentEvents, ...newEvents])) {
        console.warn(`🚨 Threat pattern detected: ${pattern.name}`)
        
        // Log the threat detection as a security event
        logSecurityEvent({
          eventType: 'security_violation',
          severity: pattern.severity,
          action: `threat_pattern_detected:${pattern.id}`,
          ipAddress: 'system',
          userAgent: 'security_monitor',
          success: true,
          metadata: {
            patternId: pattern.id,
            patternName: pattern.name,
            description: pattern.description
          },
          securityFlags: ['automated_threat_detection', pattern.id]
        })

        // Execute auto-response if configured
        if (pattern.autoResponse) {
          pattern.autoResponse(newEvents)
        }
      }
    } catch (error) {
      console.error(`Error checking threat pattern ${pattern.id}:`, error)
    }
  }
}

/**
 * Get security metrics for a time range
 */
export function getSecurityMetrics(
  startDate: Date = new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
  endDate: Date = new Date()
): SecurityMetrics {
  const events = auditLog.filter(e => 
    e.timestamp >= startDate && e.timestamp <= endDate
  )

  const eventsByType = events.reduce((acc, event) => {
    acc[event.eventType] = (acc[event.eventType] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const eventsBySeverity = events.reduce((acc, event) => {
    acc[event.severity] = (acc[event.severity] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const failedAuthAttempts = events.filter(e => 
    e.eventType === 'authentication' && !e.success
  ).length

  const suspiciousActivities = events.filter(e => 
    e.securityFlags.some(flag => ['suspicious_ip', 'unusual_pattern', 'anomaly_detected'].includes(flag))
  ).length

  const highRiskEvents = events.filter(e => e.riskScore >= 70).length

  const uniqueAdmins = new Set(events.map(e => e.adminId).filter(Boolean)).size
  const uniqueIPs = new Set(events.map(e => e.ipAddress)).size

  const totalRiskScore = events.reduce((sum, e) => sum + e.riskScore, 0)
  const averageRiskScore = events.length > 0 ? totalRiskScore / events.length : 0

  return {
    totalEvents: events.length,
    eventsByType,
    eventsBySeverity,
    failedAuthAttempts,
    suspiciousActivities,
    highRiskEvents,
    uniqueAdmins,
    uniqueIPs,
    averageRiskScore: Math.round(averageRiskScore * 100) / 100,
    timeRange: { start: startDate, end: endDate }
  }
}

/**
 * Get recent high-risk events
 */
export function getHighRiskEvents(limit: number = 50): SecurityAuditEvent[] {
  return auditLog
    .filter(e => e.riskScore >= 50)
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, limit)
}

/**
 * Search audit log
 */
export function searchAuditLog(criteria: {
  adminId?: string
  eventType?: string
  severity?: string
  ipAddress?: string
  startDate?: Date
  endDate?: Date
  minRiskScore?: number
}): SecurityAuditEvent[] {
  return auditLog.filter(event => {
    if (criteria.adminId && event.adminId !== criteria.adminId) return false
    if (criteria.eventType && event.eventType !== criteria.eventType) return false
    if (criteria.severity && event.severity !== criteria.severity) return false
    if (criteria.ipAddress && event.ipAddress !== criteria.ipAddress) return false
    if (criteria.startDate && event.timestamp < criteria.startDate) return false
    if (criteria.endDate && event.timestamp > criteria.endDate) return false
    if (criteria.minRiskScore && event.riskScore < criteria.minRiskScore) return false
    return true
  }).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
}

/**
 * Export audit log for compliance
 */
export function exportAuditLog(format: 'json' | 'csv' = 'json'): string {
  if (format === 'csv') {
    const headers = ['ID', 'Timestamp', 'Event Type', 'Severity', 'Admin Email', 'Action', 'Success', 'IP Address', 'Risk Score']
    const rows = auditLog.map(event => [
      event.id,
      event.timestamp.toISOString(),
      event.eventType,
      event.severity,
      event.adminEmail || '',
      event.action,
      event.success.toString(),
      event.ipAddress,
      event.riskScore.toString()
    ])
    
    return [headers, ...rows].map(row => row.join(',')).join('\n')
  }
  
  return JSON.stringify(auditLog, null, 2)
}
