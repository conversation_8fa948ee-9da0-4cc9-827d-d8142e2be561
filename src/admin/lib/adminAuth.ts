/**
 * Admin Authentication Utilities
 *
 * Centralized authentication functions specifically for admin functionality.
 * Provides role-based access control and admin-specific authentication helpers.
 *
 * Features:
 * - Admin role verification
 * - Superadmin role verification
 * - Admin access control helpers
 * - Role-based permission checking
 *
 * <AUTHOR> Team
 */

/**
 * Checks if a user has admin privileges
 *
 * @param role - User's role string
 * @returns True if user is admin or superadmin
 */
export const isAdmin = (role?: string | null): boolean => {
  return role === 'admin' || role === 'superadmin'
}

/**
 * Checks if a user has superadmin privileges
 *
 * @param role - User's role string
 * @returns True if user is superadmin
 */
export const isSuperAdmin = (role?: string | null): boolean => {
  return role === 'superadmin'
}

/**
 * Checks if a user has access to admin features
 * Enhanced with additional security validation
 *
 * @param role - User's role string
 * @param requireSuperAdmin - Whether to require superadmin specifically
 * @param additionalChecks - Additional security validation options
 * @returns True if user has required admin access
 */
export const hasAdminAccess = (
  role?: string | null,
  requireSuperAdmin: boolean = false,
  additionalChecks?: {
    validateToken?: boolean
    checkSession?: boolean
    requireMFA?: boolean
  }
): boolean => {
  // Basic role check
  const hasValidRole = requireSuperAdmin ? isSuperAdmin(role) : isAdmin(role)

  if (!hasValidRole) {
    return false
  }

  // Additional security checks can be implemented here
  // For now, return basic role validation
  // TODO: Implement token validation, session checking, and MFA verification

  return hasValidRole
}

/**
 * Gets admin permission level for a user
 *
 * @param role - User's role string
 * @returns Permission level: 'none', 'admin', or 'superadmin'
 */
export const getAdminPermissionLevel = (role?: string | null): 'none' | 'admin' | 'superadmin' => {
  if (role === 'superadmin') return 'superadmin'
  if (role === 'admin') return 'admin'
  return 'none'
}

/**
 * Validates admin access and throws error if insufficient
 *
 * @param role - User's role string
 * @param requireSuperAdmin - Whether to require superadmin specifically
 * @throws Error if user doesn't have required admin access
 */
export const validateAdminAccess = (role?: string | null, requireSuperAdmin: boolean = false): void => {
  if (!hasAdminAccess(role, requireSuperAdmin)) {
    const requiredLevel = requireSuperAdmin ? 'superadmin' : 'admin'
    throw new Error(`Access denied. Required role: ${requiredLevel}, current role: ${role || 'none'}`)
  }
}

/**
 * Admin role constants for consistent usage
 */
export const ADMIN_ROLES = {
  USER: 'user',
  ADMIN: 'admin',
  SUPERADMIN: 'superadmin'
} as const

/**
 * Checks if a role is a valid admin role
 *
 * @param role - Role string to validate
 * @returns True if role is a valid admin role
 */
export const isValidAdminRole = (role: string): role is import('../types/admin').AdminRole => {
  return Object.values(ADMIN_ROLES).includes(role as import('../types/admin').AdminRole)
}

/**
 * Enhanced admin authentication with token validation
 *
 * @param userRole - User's role
 * @param authToken - Authentication token
 * @param sessionId - Session identifier
 * @returns Enhanced authentication result
 */
export const validateAdminAuthentication = (
  userRole?: string | null,
  authToken?: string | null,
  sessionId?: string | null
): {
  isValid: boolean
  hasAdminRole: boolean
  hasValidToken: boolean
  hasValidSession: boolean
  permissionLevel: 'none' | 'admin' | 'superadmin'
} => {
  const hasAdminRole = isAdmin(userRole)
  const hasValidToken = Boolean(authToken && authToken.length > 0)
  const hasValidSession = Boolean(sessionId && sessionId.length > 0)

  // For now, require either token or session for admin access
  // TODO: Implement proper token validation and session verification
  const isValid = hasAdminRole && (hasValidToken || hasValidSession)

  return {
    isValid,
    hasAdminRole,
    hasValidToken,
    hasValidSession,
    permissionLevel: getAdminPermissionLevel(userRole)
  }
}

/**
 * Enhanced security audit logging for admin authentication attempts
 *
 * @param attempt - Authentication attempt details
 */
export const logAdminAuthAttempt = (attempt: {
  userRole?: string | null
  success: boolean
  ipAddress?: string
  userAgent?: string
  timestamp?: Date
  sessionId?: string
  route?: string
  riskScore?: number
  securityFlags?: string[]
}): void => {
  const logEntry = {
    ...attempt,
    timestamp: attempt.timestamp || new Date(),
    severity: attempt.success ? 'info' : 'warning',
    category: 'admin_authentication',
    source: 'ProtectedAdminRoute'
  }

  // Enhanced logging with structured data
  console.log(`${attempt.success ? '✅' : '❌'} Admin Auth:`, logEntry)

  // In production, send to monitoring service
  if (!attempt.success) {
    console.warn('🚨 Failed admin authentication attempt:', {
      userRole: attempt.userRole,
      ipAddress: attempt.ipAddress,
      userAgent: attempt.userAgent?.substring(0, 100),
      riskScore: attempt.riskScore,
      securityFlags: attempt.securityFlags,
      timestamp: logEntry.timestamp
    })
  }

  // TODO: Implement proper audit logging to database/monitoring service
  // - Store in audit_logs table
  // - Send alerts for suspicious patterns
  // - Update security metrics
  // - Trigger automated responses for high-risk attempts
}
