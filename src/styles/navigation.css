/**
 * Enhanced Navigation Styles - Version 2.0
 *
 * Consolidated navigation system with advanced features:
 * - Component factory pattern support
 * - Advanced search and filtering
 * - Keyboard shortcuts integration
 * - Responsive variant styling
 * - Consistent purple theming and accessibility improvements
 * Following Syndicaps design system with tech-inspired elements and neon accents
 */

/* ===== NAVIGATION BASE STYLES ===== */

.nav-container {
  @apply bg-gray-900/95 backdrop-blur-sm border border-gray-700;
  @apply transition-all duration-300 ease-out;
}

.nav-container:hover {
  @apply border-accent-500/30 shadow-lg shadow-accent-500/10;
}

/* ===== NAVIGATION ITEMS ===== */

.nav-item {
  @apply flex items-center space-x-3 px-3 py-3 rounded-lg;
  @apply transition-all duration-300 ease-out;
  @apply min-h-[44px] min-w-[44px] touch-target-lg;
  @apply focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-900;
  @apply relative overflow-hidden;
}

.nav-item::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-transparent to-transparent;
  @apply transition-all duration-300 ease-out opacity-0;
  @apply rounded-lg;
}

.nav-item:hover::before {
  @apply from-accent-900/20 to-accent-800/10 opacity-100;
}

.nav-item-active {
  @apply bg-gradient-to-r from-accent-900/30 to-accent-800/20;
  @apply text-accent-400 border-l-2 border-accent-500;
  @apply shadow-lg shadow-accent-500/20;
}

.nav-item-active::before {
  @apply from-accent-900/10 to-accent-800/10 opacity-100;
}

.nav-item-hover {
  @apply hover:bg-gradient-to-r hover:from-accent-900/20 hover:to-accent-800/10;
  @apply hover:text-accent-300 hover:shadow-md hover:shadow-accent-500/20;
  @apply hover:scale-105 hover:translate-x-1;
}

.nav-item-text {
  @apply text-gray-300 transition-colors duration-300;
}

.nav-item-active .nav-item-text {
  @apply text-accent-400;
}

.nav-item:hover .nav-item-text {
  @apply text-accent-300;
}

.nav-item-icon {
  @apply text-gray-400 transition-all duration-300;
  @apply flex-shrink-0;
}

.nav-item-active .nav-item-icon {
  @apply text-accent-500;
}

.nav-item:hover .nav-item-icon {
  @apply text-accent-400 scale-110;
}

/* ===== NAVIGATION CATEGORIES ===== */

.nav-category {
  @apply mb-6;
}

.nav-category-header {
  @apply flex items-center space-x-2 px-3 py-2 mb-3;
  @apply text-sm font-semibold text-gray-400 uppercase tracking-wider;
}

.nav-category-icon {
  @apply text-accent-500 flex-shrink-0;
}

.nav-category-label {
  @apply text-gray-400;
}

/* ===== NAVIGATION BADGES ===== */

.nav-badge {
  @apply px-2 py-1 text-xs rounded-full flex-shrink-0 ml-auto;
  @apply transition-all duration-300;
}

.nav-badge-notification {
  @apply bg-red-500 text-white;
  @apply animate-pulse;
}

.nav-badge-count {
  @apply bg-accent-500 text-white;
  @apply hover:bg-accent-400;
}

.nav-badge-new {
  @apply bg-green-500 text-white;
  @apply animate-bounce;
}

.nav-badge-default {
  @apply bg-gray-600 text-gray-300;
}

/* ===== MOBILE NAVIGATION ===== */

.nav-mobile {
  @apply fixed bottom-0 left-0 right-0 z-50;
  @apply bg-gray-900/95 backdrop-blur-sm border-t border-gray-700;
  @apply safe-area-pb;
}

.nav-mobile-item {
  @apply flex flex-col items-center justify-center;
  @apply min-h-[60px] min-w-[60px] transition-all duration-300;
  @apply focus:outline-none focus:ring-2 focus:ring-accent-500;
  @apply relative overflow-hidden;
}

.nav-mobile-item::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-t from-transparent to-transparent;
  @apply transition-all duration-300 ease-out opacity-0;
}

.nav-mobile-item:hover::before,
.nav-mobile-item-active::before {
  @apply from-accent-900/30 to-accent-800/20 opacity-100;
}

.nav-mobile-item-active {
  @apply text-accent-400 bg-accent-900/20;
}

.nav-mobile-item:hover {
  @apply text-accent-300 bg-accent-900/10;
  @apply scale-105;
}

.nav-mobile-icon {
  @apply transition-all duration-300;
}

.nav-mobile-item-active .nav-mobile-icon {
  @apply text-accent-500 scale-110;
}

.nav-mobile-item:hover .nav-mobile-icon {
  @apply text-accent-400 scale-105;
}

.nav-mobile-label {
  @apply text-xs mt-1 transition-colors duration-300;
}

/* ===== SEARCH FUNCTIONALITY ===== */

.nav-search {
  @apply bg-gray-800 border border-gray-600 rounded-lg p-4 mb-4;
  @apply transition-all duration-300;
}

.nav-search:focus-within {
  @apply border-accent-500 shadow-lg shadow-accent-500/20;
}

.nav-search-input {
  @apply w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg;
  @apply text-white placeholder-gray-400;
  @apply focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500;
  @apply transition-all duration-300;
}

.nav-search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2;
  @apply text-gray-400 transition-colors duration-300;
}

.nav-search:focus-within .nav-search-icon {
  @apply text-accent-500;
}

/* ===== QUICK SETTINGS ===== */

.nav-quick-settings {
  @apply bg-gray-800 border border-gray-600 rounded-lg p-4 mb-4;
  @apply transition-all duration-300;
}

.nav-toggle {
  @apply relative inline-flex h-5 w-9 items-center rounded-full;
  @apply transition-colors duration-300;
  @apply focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-800;
}

.nav-toggle-active {
  @apply bg-accent-500;
}

.nav-toggle-inactive {
  @apply bg-gray-600;
}

.nav-toggle-thumb {
  @apply inline-block h-3 w-3 transform rounded-full bg-white;
  @apply transition-transform duration-300;
}

.nav-toggle-active .nav-toggle-thumb {
  @apply translate-x-5;
}

.nav-toggle-inactive .nav-toggle-thumb {
  @apply translate-x-1;
}

/* ===== TECH-INSPIRED EFFECTS ===== */

.nav-tech-glow {
  @apply relative;
}

.nav-tech-glow::after {
  content: '';
  @apply absolute inset-0 rounded-lg opacity-0;
  @apply bg-gradient-to-r from-transparent via-accent-500/20 to-transparent;
  @apply transition-opacity duration-500;
  @apply pointer-events-none;
}

.nav-tech-glow:hover::after {
  @apply opacity-100;
  animation: tech-scan 2s ease-in-out infinite;
}

@keyframes tech-scan {
  0%, 100% { 
    transform: translateX(-100%) skewX(-15deg); 
    opacity: 0;
  }
  50% { 
    transform: translateX(100%) skewX(-15deg); 
    opacity: 1;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

.nav-item[aria-current="page"] {
  @apply nav-item-active;
}

.nav-item:focus-visible {
  @apply ring-2 ring-accent-500 ring-offset-2 ring-offset-gray-900;
}

.nav-mobile-item:focus-visible {
  @apply ring-2 ring-accent-500 ring-offset-2 ring-offset-gray-900;
}

/* ===== RESPONSIVE UTILITIES ===== */

.touch-target-lg {
  @apply min-h-[44px] min-w-[44px];
}

.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

/* ===== ANIMATION UTILITIES ===== */

.nav-fade-in {
  animation: nav-fade-in 0.3s ease-out;
}

.nav-slide-up {
  animation: nav-slide-up 0.3s ease-out;
}

@keyframes nav-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes nav-slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== CONSOLIDATED NAVIGATION SYSTEM ===== */

/* Enhanced search components */
.nav-search-container {
  @apply absolute top-0 left-0 right-0 z-50;
  @apply bg-gray-800 border border-gray-700 rounded-lg shadow-xl;
  @apply p-4 space-y-3;
}

.nav-search-input {
  @apply w-full bg-gray-700 border border-gray-600 rounded-lg;
  @apply px-10 py-2 text-white placeholder-gray-400;
  @apply focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent;
}

.nav-search-results {
  @apply max-h-64 overflow-y-auto space-y-1;
  @apply border-t border-gray-700 pt-3;
}

.nav-search-result {
  @apply flex items-center space-x-3 p-2 rounded-lg;
  @apply text-gray-300 hover:bg-gray-700 transition-colors;
  @apply cursor-pointer;
}

.nav-search-hint {
  @apply flex items-center space-x-2 text-xs text-gray-500;
  @apply border-t border-gray-700 pt-2;
}

/* Enhanced quick settings */
.nav-quick-settings {
  @apply absolute top-0 right-0 z-40;
  @apply bg-gray-800 border border-gray-700 rounded-lg shadow-xl;
  @apply p-4 min-w-[200px];
}

.nav-setting-item {
  @apply flex items-center space-x-2 cursor-pointer;
  @apply hover:bg-gray-700 rounded px-2 py-1 transition-colors;
}

.nav-checkbox {
  @apply rounded border-gray-600 bg-gray-700 text-accent-500;
  @apply focus:ring-accent-500 focus:ring-offset-gray-800;
}

/* Responsive variant enhancements */
.nav-tablet {
  @apply w-56 h-full;
}

.nav-compact-item {
  @apply py-1 px-2;
}

.nav-mobile-category {
  @apply space-y-1;
}

/* Enhanced accessibility */
.nav-search-input:focus-visible {
  @apply outline-none ring-2 ring-accent-500 ring-offset-2 ring-offset-gray-800;
}

/* High contrast mode enhancements */
@media (prefers-contrast: high) {
  .nav-search-container {
    @apply border-2 border-accent-500;
  }
}

/* Reduced motion enhancements */
@media (prefers-reduced-motion: reduce) {
  .nav-search-container,
  .nav-quick-settings {
    @apply transition-none;
  }
}

/* ===== ADVANCED SEARCH COMPONENTS ===== */

.nav-search-overlay {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-50;
  @apply flex items-start justify-center pt-20;
}

.nav-search-container {
  @apply bg-gray-800 border border-gray-700 rounded-xl shadow-2xl;
  @apply w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden;
  @apply flex flex-col p-4 space-y-4;
}

.nav-search-input {
  @apply w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-lg;
  @apply text-white placeholder-gray-400 text-base;
  @apply focus:border-accent-500 focus:ring-2 focus:ring-accent-500/20;
  @apply transition-all duration-200 outline-none;
}

.nav-search-suggestions {
  @apply bg-gray-700 border border-gray-600 rounded-lg;
  @apply divide-y divide-gray-600 overflow-hidden;
  @apply absolute top-full left-0 right-0 mt-1 z-10;
}

.nav-search-results {
  @apply flex-1 overflow-y-auto space-y-2;
  @apply max-h-96;
}

/* Custom scrollbar styling */
.nav-search-results::-webkit-scrollbar {
  width: 6px;
}

.nav-search-results::-webkit-scrollbar-track {
  background: #1f2937; /* gray-800 */
  border-radius: 3px;
}

.nav-search-results::-webkit-scrollbar-thumb {
  background: #4b5563; /* gray-600 */
  border-radius: 3px;
}

.nav-search-results::-webkit-scrollbar-thumb:hover {
  background: #6b7280; /* gray-500 */
}

.nav-search-result {
  @apply p-3 rounded-lg border border-transparent;
  @apply hover:bg-gray-700/50 hover:border-accent-500/30;
  @apply transition-all duration-200 cursor-pointer;
  @apply focus:outline-none focus:ring-2 focus:ring-accent-500/50;
}

.nav-search-result.selected {
  @apply bg-accent-900/30 border-accent-500/50;
}

.nav-search-result-icon {
  @apply w-5 h-5 text-gray-400 flex-shrink-0;
}

.nav-search-result-content {
  @apply flex-1 min-w-0 ml-3;
}

.nav-search-result-title {
  @apply font-medium text-gray-200 truncate;
}

.nav-search-result-description {
  @apply text-sm text-gray-400 truncate mt-1;
}

.nav-search-result-meta {
  @apply flex items-center justify-between mt-2;
}

.nav-search-result-category {
  @apply text-xs text-gray-500;
}

.nav-search-hint {
  @apply px-4 py-3 border-t border-gray-700 bg-gray-800/50;
  @apply text-xs text-gray-400 flex items-center justify-center;
}

/* Search result highlighting */
.nav-search-result mark {
  @apply bg-accent-500/30 text-accent-200 rounded px-0.5;
}

/* Category filter buttons */
.nav-category-filter {
  @apply px-3 py-1 rounded-full text-xs font-medium;
  @apply transition-all duration-200 cursor-pointer;
  @apply border border-transparent;
}

.nav-category-filter:not(.active) {
  @apply bg-gray-700 text-gray-300 hover:bg-gray-600;
  @apply hover:border-gray-500;
}

.nav-category-filter.active {
  @apply bg-accent-500 text-white border-accent-400;
  @apply shadow-lg shadow-accent-500/25;
}

/* Keyboard shortcuts styling */
.nav-search-hint kbd {
  @apply px-1.5 py-0.5 bg-gray-700 text-gray-300 rounded text-xs;
  @apply border border-gray-600 font-mono;
}

/* ===== CONTEXTUAL SUGGESTIONS PANEL ===== */

.contextual-suggestions-panel {
  @apply bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-lg;
  @apply p-4 space-y-3 overflow-y-auto;
  max-height: 400px;
}

/* Custom scrollbar for suggestions panel */
.contextual-suggestions-panel::-webkit-scrollbar {
  width: 6px;
}

.contextual-suggestions-panel::-webkit-scrollbar-track {
  background: #1f2937; /* gray-800 */
  border-radius: 3px;
}

.contextual-suggestions-panel::-webkit-scrollbar-thumb {
  background: #8b5cf6; /* accent-500 */
  border-radius: 3px;
}

.contextual-suggestions-panel::-webkit-scrollbar-thumb:hover {
  background: #7c3aed; /* accent-600 */
}

.suggestion-card {
  @apply relative overflow-hidden;
  @apply transition-all duration-300 ease-out;
}

.suggestion-card::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-transparent via-accent-500/5 to-transparent;
  @apply opacity-0 transition-opacity duration-300 pointer-events-none;
}

.suggestion-card:hover::before {
  @apply opacity-100;
}

/* Priority indicators */
.suggestion-priority-urgent {
  @apply border-red-500/30 shadow-lg shadow-red-500/20;
}

.suggestion-priority-high {
  @apply border-orange-500/30 shadow-lg shadow-orange-500/20;
}

.suggestion-priority-medium {
  @apply border-accent-500/30 shadow-lg shadow-accent-500/20;
}

.suggestion-priority-low {
  @apply border-gray-500/30 shadow-lg shadow-gray-500/20;
}

/* Suggestion reason icons */
.suggestion-reason-icon {
  @apply flex-shrink-0 transition-colors duration-200;
}

/* Progress bars for completion suggestions */
.suggestion-progress-bar {
  @apply w-full bg-gray-700 rounded-full h-1.5 overflow-hidden;
}

.suggestion-progress-fill {
  @apply h-full rounded-full transition-all duration-500 ease-out;
}

/* Dismiss options dropdown */
.suggestion-dismiss-dropdown {
  @apply absolute top-8 right-2 bg-gray-900 border border-gray-600 rounded-lg shadow-xl z-20;
  @apply min-w-[140px] overflow-hidden;
}

.suggestion-dismiss-option {
  @apply block w-full px-3 py-2 text-left text-sm text-gray-300;
  @apply hover:bg-gray-700 transition-colors duration-200;
  @apply border-0 bg-transparent cursor-pointer;
}

.suggestion-dismiss-option:first-child {
  @apply rounded-t-lg;
}

.suggestion-dismiss-option:last-child {
  @apply rounded-b-lg;
}

/* Compact mode adjustments */
.contextual-suggestions-panel.compact {
  @apply p-3 space-y-2;
}

.contextual-suggestions-panel.compact .suggestion-card {
  @apply p-2;
}

.contextual-suggestions-panel.compact h3 {
  @apply text-sm;
}

/* Animation enhancements */
.suggestion-card-enter {
  @apply opacity-0 translate-y-4;
}

.suggestion-card-enter-active {
  @apply opacity-100 translate-y-0 transition-all duration-300 ease-out;
}

.suggestion-card-exit {
  @apply opacity-100 translate-x-0;
}

.suggestion-card-exit-active {
  @apply opacity-0 -translate-x-full transition-all duration-300 ease-in;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .contextual-suggestions-panel {
    @apply p-3 space-y-2;
  }

  .suggestion-card {
    @apply p-3;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .suggestion-card {
    @apply border-2;
  }

  .suggestion-priority-urgent {
    @apply border-red-400;
  }

  .suggestion-priority-high {
    @apply border-orange-400;
  }

  .suggestion-priority-medium {
    @apply border-accent-400;
  }

  .suggestion-priority-low {
    @apply border-gray-400;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .suggestion-card,
  .suggestion-card::before,
  .suggestion-progress-fill {
    @apply transition-none;
  }

  .suggestion-card-enter-active,
  .suggestion-card-exit-active {
    @apply transition-none;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Skip Links */
.skip-links {
  z-index: 9999;
}

.skip-link-button {
  min-height: 44px; /* WCAG touch target */
  min-width: 44px;
}

/* Keyboard Navigation Indicators */
.keyboard-navigation *:focus {
  outline: 2px solid #8b5cf6 !important;
  outline-offset: 2px !important;
  border-radius: 4px;
}

.keyboard-navigation .nav-item:focus {
  @apply bg-accent-500/20 border-accent-500;
  box-shadow: 0 0 0 2px #8b5cf6;
}

/* High Contrast Mode */
.high-contrast {
  --nav-bg: #000000;
  --nav-text: #ffffff;
  --nav-border: #ffffff;
  --nav-hover: #ffffff;
  --nav-active: #ffff00;
}

.high-contrast .nav-item {
  border: 1px solid var(--nav-border);
  background: var(--nav-bg);
  color: var(--nav-text);
}

.high-contrast .nav-item:hover {
  background: var(--nav-hover);
  color: var(--nav-bg);
}

.high-contrast .nav-item.active {
  background: var(--nav-active);
  color: var(--nav-bg);
}

/* Reduced Motion */
.reduced-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus Indicators */
.nav-focus-indicator {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #8b5cf6;
  border-radius: 6px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.nav-item:focus .nav-focus-indicator {
  opacity: 1;
}

/* Roving Tabindex */
.nav-roving-tabindex [tabindex="0"] {
  position: relative;
}

.nav-roving-tabindex [tabindex="0"]::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #8b5cf6;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.nav-roving-tabindex [tabindex="0"]:focus::before {
  opacity: 1;
}

/* ARIA Live Regions */
.nav-live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Touch Targets */
@media (pointer: coarse) {
  .nav-item,
  .nav-button,
  .skip-link-button {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
  }
}

/* Print Styles */
@media print {
  .skip-links,
  .contextual-suggestions-panel,
  .nav-search-overlay {
    display: none !important;
  }
}
