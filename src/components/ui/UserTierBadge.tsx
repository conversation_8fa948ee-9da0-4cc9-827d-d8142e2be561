'use client'

/**
 * User Tier Badge Component
 * 
 * Displays user tier badges with appropriate styling and colors
 * based on the Syndicaps gamification system.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import React from 'react'
import { Crown, Award, Star, Shield, Gem } from 'lucide-react'
import { UserTier } from '@/types/timeline'

interface UserTierBadgeProps {
  /** User tier */
  tier: UserTier
  /** Size variant */
  size?: 'xs' | 'sm' | 'md' | 'lg'
  /** Whether to show icon */
  showIcon?: boolean
  /** Custom CSS classes */
  className?: string
}

/**
 * Get tier configuration
 */
const getTierConfig = (tier: UserTier) => {
  switch (tier) {
    case 'Bronze':
      return {
        icon: <Shield className="w-3 h-3" />,
        bgColor: 'tier-bronze-bg',
        borderColor: 'tier-bronze-border',
        textColor: 'tier-bronze-text',
        label: 'Bronze'
      }
    case 'Silver':
      return {
        icon: <Star className="w-3 h-3" />,
        bgColor: 'tier-silver-bg',
        borderColor: 'tier-silver-border',
        textColor: 'tier-silver-text',
        label: 'Silver'
      }
    case 'Gold':
      return {
        icon: <Award className="w-3 h-3" />,
        bgColor: 'tier-gold-bg',
        borderColor: 'tier-gold-border',
        textColor: 'tier-gold-text',
        label: 'Gold'
      }
    case 'Platinum':
      return {
        icon: <Crown className="w-3 h-3" />,
        bgColor: 'tier-platinum-bg',
        borderColor: 'tier-platinum-border',
        textColor: 'tier-platinum-text',
        label: 'Platinum'
      }
    case 'Diamond':
      return {
        icon: <Gem className="w-3 h-3" />,
        bgColor: 'tier-diamond-bg',
        borderColor: 'tier-diamond-border',
        textColor: 'tier-diamond-text',
        label: 'Diamond'
      }
    default:
      return {
        icon: <Shield className="w-3 h-3" />,
        bgColor: 'bg-gray-600/20',
        borderColor: 'border-gray-500/30',
        textColor: 'text-gray-400',
        label: tier
      }
  }
}

/**
 * Get size classes
 */
const getSizeClasses = (size: string) => {
  switch (size) {
    case 'xs':
      return 'px-1.5 py-0.5 text-xs'
    case 'sm':
      return 'px-2 py-1 text-xs'
    case 'md':
      return 'px-2.5 py-1 text-sm'
    case 'lg':
      return 'px-3 py-1.5 text-sm'
    default:
      return 'px-2 py-1 text-xs'
  }
}

/**
 * User Tier Badge Component
 */
const UserTierBadge: React.FC<UserTierBadgeProps> = ({
  tier,
  size = 'sm',
  showIcon = true,
  className = ''
}) => {
  const config = getTierConfig(tier)
  const sizeClasses = getSizeClasses(size)

  return (
    <span
      className={`
        inline-flex items-center space-x-1 rounded-full border font-medium
        ${config.bgColor} ${config.borderColor} ${config.textColor}
        ${sizeClasses} ${className}
      `}
      title={`${config.label} Tier`}
    >
      {showIcon && config.icon}
      <span>{config.label}</span>
    </span>
  )
}

export default UserTierBadge
