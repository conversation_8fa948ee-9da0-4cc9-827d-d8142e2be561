/**
 * Contextual Suggestions Panel
 * 
 * Smart navigation suggestions panel that displays contextual recommendations
 * based on user behavior, completion status, and current context.
 * 
 * Features:
 * - Intelligent suggestion display with priority indicators
 * - Animated suggestion cards with hover effects
 * - Dismissible suggestions with feedback tracking
 * - Suggestion reason explanations
 * - Progress indicators for completion-based suggestions
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Lightbulb, 
  TrendingUp, 
  Clock, 
  Target, 
  Zap, 
  X, 
  ChevronRight,
  Star,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { ContextualSuggestion, SuggestionReason } from '@/lib/navigation/ContextualSuggestions'
import { NavigationItem } from '@/lib/navigation/NavigationProvider'

// ===== TYPES =====

export interface ContextualSuggestionsPanelProps {
  suggestions: ContextualSuggestion[]
  onSuggestionClick: (suggestion: ContextualSuggestion) => void
  onSuggestionDismiss: (suggestionId: string, reason: 'not_interested' | 'completed' | 'not_relevant') => void
  className?: string
  maxSuggestions?: number
  showReasons?: boolean
  compact?: boolean
}

export interface SuggestionCardProps {
  suggestion: ContextualSuggestion
  onSelect: () => void
  onDismiss: (reason: 'not_interested' | 'completed' | 'not_relevant') => void
  showReason?: boolean
  compact?: boolean
}

// ===== UTILITY FUNCTIONS =====

/**
 * Get icon for suggestion reason
 */
const getSuggestionReasonIcon = (reason: SuggestionReason) => {
  switch (reason) {
    case 'completion_next_step':
      return <Target size={16} className="text-green-400" />
    case 'behavior_pattern':
      return <TrendingUp size={16} className="text-blue-400" />
    case 'time_based':
      return <Clock size={16} className="text-orange-400" />
    case 'context_related':
      return <Lightbulb size={16} className="text-yellow-400" />
    case 'onboarding_flow':
      return <Star size={16} className="text-purple-400" />
    case 'engagement_boost':
      return <Zap size={16} className="text-pink-400" />
    case 'feature_discovery':
      return <AlertCircle size={16} className="text-cyan-400" />
    case 'task_continuation':
      return <CheckCircle size={16} className="text-emerald-400" />
    default:
      return <Lightbulb size={16} className="text-gray-400" />
  }
}

/**
 * Get human-readable reason text
 */
const getSuggestionReasonText = (reason: SuggestionReason): string => {
  switch (reason) {
    case 'completion_next_step':
      return 'Complete your profile'
    case 'behavior_pattern':
      return 'Based on your usage'
    case 'time_based':
      return 'Popular at this time'
    case 'context_related':
      return 'Related to current page'
    case 'onboarding_flow':
      return 'Getting started'
    case 'engagement_boost':
      return 'Boost your engagement'
    case 'feature_discovery':
      return 'Discover new features'
    case 'task_continuation':
      return 'Continue your task'
    default:
      return 'Suggested for you'
  }
}

/**
 * Get priority indicator styling
 */
const getPriorityIndicator = (priority: ContextualSuggestion['priority']) => {
  switch (priority) {
    case 'urgent':
      return {
        color: 'bg-red-500',
        text: 'text-red-400',
        border: 'border-red-500/30',
        glow: 'shadow-red-500/20'
      }
    case 'high':
      return {
        color: 'bg-orange-500',
        text: 'text-orange-400',
        border: 'border-orange-500/30',
        glow: 'shadow-orange-500/20'
      }
    case 'medium':
      return {
        color: 'bg-accent-500',
        text: 'text-accent-400',
        border: 'border-accent-500/30',
        glow: 'shadow-accent-500/20'
      }
    case 'low':
      return {
        color: 'bg-gray-500',
        text: 'text-gray-400',
        border: 'border-gray-500/30',
        glow: 'shadow-gray-500/20'
      }
  }
}

// ===== SUGGESTION CARD COMPONENT =====

const SuggestionCard: React.FC<SuggestionCardProps> = ({
  suggestion,
  onSelect,
  onDismiss,
  showReason = true,
  compact = false
}) => {
  const [showDismissOptions, setShowDismissOptions] = useState(false)
  const priorityStyle = getPriorityIndicator(suggestion.priority)

  const handleDismiss = (reason: 'not_interested' | 'completed' | 'not_relevant') => {
    onDismiss(reason)
    setShowDismissOptions(false)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, x: -100 }}
      whileHover={{ scale: 1.02 }}
      className={`
        suggestion-card relative group
        ${priorityStyle.border} ${priorityStyle.glow}
        ${compact ? 'p-3' : 'p-4'}
        bg-gray-800/50 backdrop-blur-sm border rounded-lg
        hover:bg-gray-700/50 transition-all duration-300
        cursor-pointer overflow-hidden
      `}
      onClick={onSelect}
    >
      {/* Priority Indicator */}
      <div className={`absolute top-0 left-0 w-1 h-full ${priorityStyle.color}`} />
      
      {/* Dismiss Button */}
      <button
        onClick={(e) => {
          e.stopPropagation()
          setShowDismissOptions(!showDismissOptions)
        }}
        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
        aria-label="Dismiss suggestion"
      >
        <X size={14} className="text-gray-400 hover:text-white" />
      </button>

      {/* Dismiss Options */}
      <AnimatePresence>
        {showDismissOptions && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="absolute top-8 right-2 bg-gray-900 border border-gray-600 rounded-lg shadow-xl z-10"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => handleDismiss('not_interested')}
              className="block w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 rounded-t-lg"
            >
              Not interested
            </button>
            <button
              onClick={() => handleDismiss('completed')}
              className="block w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700"
            >
              Already completed
            </button>
            <button
              onClick={() => handleDismiss('not_relevant')}
              className="block w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 rounded-b-lg"
            >
              Not relevant
            </button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Content */}
      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className="flex-shrink-0 mt-0.5">
          <suggestion.item.icon
            size={compact ? 18 : 20}
            className={`${priorityStyle.text} transition-colors`}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h4 className={`font-medium text-white truncate ${compact ? 'text-sm' : 'text-base'}`}>
              {suggestion.item.label}
            </h4>
            {suggestion.priority === 'urgent' && (
              <span className="flex-shrink-0 px-1.5 py-0.5 bg-red-500/20 text-red-400 text-xs rounded-full">
                Urgent
              </span>
            )}
          </div>

          {suggestion.item.description && !compact && (
            <p className="text-sm text-gray-400 mb-2 line-clamp-2">
              {suggestion.item.description}
            </p>
          )}

          {/* Reason and Metadata */}
          {showReason && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getSuggestionReasonIcon(suggestion.reason)}
                <span className="text-xs text-gray-500">
                  {getSuggestionReasonText(suggestion.reason)}
                </span>
              </div>
              
              {!compact && (
                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  <span>
                    {Math.round(suggestion.metadata.confidence * 100)}% confidence
                  </span>
                  <ChevronRight size={12} className="text-gray-400" />
                </div>
              )}
            </div>
          )}

          {/* Progress Bar for Completion Suggestions */}
          {suggestion.reason === 'completion_next_step' && !compact && (
            <div className="mt-2">
              <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                <span>Progress</span>
                <span>{Math.round(suggestion.metadata.expectedBenefit * 100)}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-1.5">
                <div
                  className={`h-1.5 rounded-full ${priorityStyle.color} transition-all duration-300`}
                  style={{ width: `${suggestion.metadata.expectedBenefit * 100}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Hover Effect Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-accent-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
    </motion.div>
  )
}

// ===== MAIN PANEL COMPONENT =====

export const ContextualSuggestionsPanel: React.FC<ContextualSuggestionsPanelProps> = ({
  suggestions,
  onSuggestionClick,
  onSuggestionDismiss,
  className = '',
  maxSuggestions = 5,
  showReasons = true,
  compact = false
}) => {
  const [dismissedSuggestions, setDismissedSuggestions] = useState<Set<string>>(new Set())

  // Filter out dismissed suggestions
  const visibleSuggestions = suggestions
    .filter(suggestion => !dismissedSuggestions.has(suggestion.item.id))
    .slice(0, maxSuggestions)

  const handleSuggestionDismiss = useCallback((
    suggestionId: string, 
    reason: 'not_interested' | 'completed' | 'not_relevant'
  ) => {
    setDismissedSuggestions(prev => new Set([...prev, suggestionId]))
    onSuggestionDismiss(suggestionId, reason)
  }, [onSuggestionDismiss])

  if (visibleSuggestions.length === 0) {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`contextual-suggestions-panel ${className}`}
    >
      {/* Header */}
      <div className="flex items-center space-x-2 mb-4">
        <Lightbulb size={18} className="text-accent-400" />
        <h3 className={`font-semibold text-white ${compact ? 'text-sm' : 'text-base'}`}>
          Suggested for you
        </h3>
        <span className="px-2 py-0.5 bg-accent-500/20 text-accent-400 text-xs rounded-full">
          {visibleSuggestions.length}
        </span>
      </div>

      {/* Suggestions List */}
      <div className={`space-y-${compact ? '2' : '3'}`}>
        <AnimatePresence mode="popLayout">
          {visibleSuggestions.map((suggestion, index) => (
            <SuggestionCard
              key={suggestion.item.id}
              suggestion={suggestion}
              onSelect={() => onSuggestionClick(suggestion)}
              onDismiss={(reason) => handleSuggestionDismiss(suggestion.item.id, reason)}
              showReason={showReasons}
              compact={compact}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Footer */}
      {!compact && visibleSuggestions.length > 0 && (
        <div className="mt-4 pt-3 border-t border-gray-700">
          <p className="text-xs text-gray-500 text-center">
            Suggestions are personalized based on your activity and preferences
          </p>
        </div>
      )}
    </motion.div>
  )
}

export default ContextualSuggestionsPanel
