/**
 * Navigation Search Components
 * 
 * Advanced search UI components for navigation with fuzzy matching,
 * keyboard navigation, and intelligent suggestions.
 * 
 * Features:
 * - Real-time search with debouncing
 * - Keyboard navigation (arrow keys, enter, escape)
 * - Search result highlighting
 * - Category filtering
 * - Recent items display
 * - Search suggestions
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, Clock, Filter, X, ArrowRight, Zap } from 'lucide-react'
import { NavigationSearchEngine, SearchResult, SearchOptions } from '@/lib/navigation/SearchEngine'
import { NavigationItem, NavigationCategory } from '@/lib/navigation/NavigationProvider'

// ===== TYPES =====

export interface SearchComponentProps {
  items: NavigationItem[]
  categories: NavigationCategory[]
  searchEngine: NavigationSearchEngine
  onNavigate: (item: NavigationItem) => void
  onClose: () => void
  placeholder?: string
  maxResults?: number
  showCategories?: boolean
  showRecent?: boolean
  className?: string
}

export interface SearchResultItemProps {
  result: SearchResult
  isSelected: boolean
  onSelect: () => void
  onNavigate: () => void
}

// ===== UTILITY FUNCTIONS =====

/**
 * Debounce hook for search input
 */
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Highlight matched text in search results
 */
const HighlightedText: React.FC<{
  text: string
  ranges: Array<{ start: number; end: number }>
}> = ({ text, ranges }) => {
  if (ranges.length === 0) return <>{text}</>

  const parts: React.ReactNode[] = []
  let lastIndex = 0

  ranges.forEach((range, index) => {
    // Add text before highlight
    if (range.start > lastIndex) {
      parts.push(text.slice(lastIndex, range.start))
    }

    // Add highlighted text
    parts.push(
      <mark
        key={index}
        className="bg-accent-500/30 text-accent-200 rounded px-0.5"
      >
        {text.slice(range.start, range.end)}
      </mark>
    )

    lastIndex = range.end
  })

  // Add remaining text
  if (lastIndex < text.length) {
    parts.push(text.slice(lastIndex))
  }

  return <>{parts}</>
}

// ===== SEARCH RESULT ITEM COMPONENT =====

const SearchResultItem: React.FC<SearchResultItemProps> = ({
  result,
  isSelected,
  onSelect,
  onNavigate
}) => {
  const { item, category, matchType, highlightRanges } = result

  const getMatchTypeIcon = () => {
    switch (matchType) {
      case 'exact':
        return <Zap size={12} className="text-green-400" />
      case 'recent':
        return <Clock size={12} className="text-blue-400" />
      case 'category':
        return <Filter size={12} className="text-purple-400" />
      default:
        return <Search size={12} className="text-gray-400" />
    }
  }

  const getMatchTypeLabel = () => {
    switch (matchType) {
      case 'exact':
        return 'Exact match'
      case 'recent':
        return 'Recently visited'
      case 'category':
        return 'Category match'
      case 'description':
        return 'Description match'
      default:
        return 'Fuzzy match'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={`
        nav-search-result
        ${isSelected ? 'bg-accent-900/30 border-accent-500/50' : 'border-transparent'}
        border rounded-lg p-3 cursor-pointer
        transition-all duration-200
      `}
      onMouseEnter={onSelect}
      onClick={onNavigate}
      role="option"
      aria-selected={isSelected}
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-0.5">
          <item.icon
            size={18}
            className={`${isSelected ? 'text-accent-400' : 'text-gray-400'} transition-colors`}
          />
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h4 className={`font-medium truncate ${isSelected ? 'text-white' : 'text-gray-200'}`}>
              <HighlightedText text={item.label} ranges={highlightRanges} />
            </h4>
            {getMatchTypeIcon()}
          </div>

          {item.description && (
            <p className="text-sm text-gray-400 truncate mb-1">
              {item.description}
            </p>
          )}

          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500">
              {category.label} • {getMatchTypeLabel()}
            </span>
            {item.badge && (
              <span className={`nav-badge nav-badge-${item.badge.type} text-xs`}>
                {item.badge.count}
              </span>
            )}
          </div>
        </div>

        <div className="flex-shrink-0">
          <ArrowRight
            size={14}
            className={`${isSelected ? 'text-accent-400' : 'text-gray-500'} transition-colors`}
          />
        </div>
      </div>
    </motion.div>
  )
}

// ===== MAIN SEARCH COMPONENT =====

export const NavigationSearch: React.FC<SearchComponentProps> = ({
  items,
  categories,
  searchEngine,
  onNavigate,
  onClose,
  placeholder = "Search navigation...",
  maxResults = 8,
  showCategories = true,
  showRecent = true,
  className = ""
}) => {
  const [query, setQuery] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)

  const inputRef = useRef<HTMLInputElement>(null)
  const resultsRef = useRef<HTMLDivElement>(null)

  const debouncedQuery = useDebounce(query, 150)

  // Perform search
  const searchOptions: SearchOptions = {
    query: debouncedQuery,
    categories: selectedCategories.length > 0 ? selectedCategories : undefined,
    maxResults,
    includeRecent: showRecent,
    fuzzyThreshold: 0.3,
    boostRecent: true,
    contextualBoost: true
  }

  const results = searchEngine.search(items, categories, searchOptions)

  // Update suggestions
  useEffect(() => {
    if (query.length > 0 && query.length < 3) {
      const newSuggestions = searchEngine.getSuggestions(query, 5)
      setSuggestions(newSuggestions)
      setShowSuggestions(newSuggestions.length > 0)
    } else {
      setShowSuggestions(false)
    }
  }, [query, searchEngine])

  // Reset selected index when results change
  useEffect(() => {
    setSelectedIndex(0)
  }, [results])

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus()
  }, [])

  // Keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => Math.min(prev + 1, results.length - 1))
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => Math.max(prev - 1, 0))
        break
      case 'Enter':
        e.preventDefault()
        if (results[selectedIndex]) {
          handleNavigate(results[selectedIndex].item)
        }
        break
      case 'Escape':
        e.preventDefault()
        onClose()
        break
      case 'Tab':
        if (showSuggestions && suggestions.length > 0) {
          e.preventDefault()
          setQuery(suggestions[0])
          setShowSuggestions(false)
        }
        break
    }
  }, [results, selectedIndex, suggestions, showSuggestions, onClose])

  const handleNavigate = (item: NavigationItem) => {
    searchEngine.trackNavigation(item, query)
    onNavigate(item)
    onClose()
  }

  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    )
  }

  const handleSuggestionSelect = (suggestion: string) => {
    setQuery(suggestion)
    setShowSuggestions(false)
    inputRef.current?.focus()
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className={`nav-search-container ${className}`}
      role="combobox"
      aria-expanded="true"
      aria-haspopup="listbox"
    >
      {/* Search Input */}
      <div className="relative">
        <Search
          size={18}
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
        />
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="nav-search-input pl-10 pr-10"
          aria-label="Search navigation"
          aria-autocomplete="list"
          aria-controls="search-results"
          role="searchbox"
        />
        <button
          onClick={onClose}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
          aria-label="Close search"
        >
          <X size={18} />
        </button>
      </div>

      {/* Search Suggestions */}
      <AnimatePresence>
        {showSuggestions && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="nav-search-suggestions"
          >
            {suggestions.map((suggestion, index) => (
              <button
                key={suggestion}
                onClick={() => handleSuggestionSelect(suggestion)}
                className="w-full text-left px-3 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors"
              >
                <Search size={14} className="inline mr-2" />
                {suggestion}
              </button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Category Filters */}
      {showCategories && categories.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-3">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => handleCategoryToggle(category.id)}
              className={`
                px-3 py-1 rounded-full text-xs font-medium transition-colors
                ${selectedCategories.includes(category.id)
                  ? 'bg-accent-500 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }
              `}
            >
              <category.icon size={12} className="inline mr-1" />
              {category.label}
            </button>
          ))}
        </div>
      )}

      {/* Search Results */}
      <div
        ref={resultsRef}
        id="search-results"
        className="nav-search-results"
        role="listbox"
        aria-label="Search results"
      >
        <AnimatePresence mode="popLayout">
          {results.length > 0 ? (
            results.map((result, index) => (
              <SearchResultItem
                key={result.item.id}
                result={result}
                isSelected={index === selectedIndex}
                onSelect={() => setSelectedIndex(index)}
                onNavigate={() => handleNavigate(result.item)}
              />
            ))
          ) : query.trim() ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-8 text-gray-400"
            >
              <Search size={24} className="mx-auto mb-2 opacity-50" />
              <p>No results found for "{query}"</p>
              <p className="text-sm mt-1">Try a different search term or browse categories</p>
            </motion.div>
          ) : showRecent ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-4 text-gray-400"
            >
              <Clock size={20} className="mx-auto mb-2 opacity-50" />
              <p className="text-sm">Recent items will appear here</p>
            </motion.div>
          ) : null}
        </AnimatePresence>
      </div>

      {/* Search Hints */}
      <div className="nav-search-hint">
        <div className="flex items-center space-x-4 text-xs">
          <span className="flex items-center space-x-1">
            <kbd className="px-1.5 py-0.5 bg-gray-700 rounded text-xs">↑↓</kbd>
            <span>Navigate</span>
          </span>
          <span className="flex items-center space-x-1">
            <kbd className="px-1.5 py-0.5 bg-gray-700 rounded text-xs">Enter</kbd>
            <span>Select</span>
          </span>
          <span className="flex items-center space-x-1">
            <kbd className="px-1.5 py-0.5 bg-gray-700 rounded text-xs">Esc</kbd>
            <span>Close</span>
          </span>
          {showSuggestions && (
            <span className="flex items-center space-x-1">
              <kbd className="px-1.5 py-0.5 bg-gray-700 rounded text-xs">Tab</kbd>
              <span>Complete</span>
            </span>
          )}
        </div>
      </div>
    </motion.div>
  )
}

export default NavigationSearch
