/**
 * Backward Compatibility Layer for Navigation Components
 * 
 * Provides compatibility wrappers for legacy navigation components to ensure
 * smooth transition to ConsolidatedNavigation without breaking existing implementations.
 * 
 * This layer allows gradual migration by:
 * - Maintaining existing component APIs
 * - Providing deprecation warnings
 * - Mapping legacy props to new system
 * - Ensuring visual consistency
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @deprecated Use ConsolidatedNavigation directly for new implementations
 */

'use client'

import React, { useEffect } from 'react'
import { UserProfile } from '@/types/profile'
import { 
  ConsolidatedNavigation, 
  DesktopNavigation, 
  MobileNavigation,
  ConsolidatedNavigationProps 
} from './ConsolidatedNavigation'

// ===== DEPRECATION UTILITIES =====

const logDeprecationWarning = (componentName: string, replacement: string) => {
  if (process.env.NODE_ENV === 'development') {
    console.warn(
      `🚨 DEPRECATION WARNING: ${componentName} is deprecated and will be removed in v4.0.0. ` +
      `Please migrate to ${replacement}. See migration guide: /docs/navigation-migration-guide.md`
    )
  }
}

// ===== UNIFIED NAVIGATION COMPATIBILITY =====

interface LegacyUnifiedNavigationProps {
  profile: UserProfile | null
  wishlistItemCount: number
  loading: boolean
  variant?: 'desktop' | 'mobile' | 'auto'
  showSearch?: boolean
  showQuickSettings?: boolean
  showBreadcrumbs?: boolean
  className?: string
  onNavigate?: (href: string) => void
}

/**
 * @deprecated Use ConsolidatedNavigation instead
 */
export const UnifiedNavigation: React.FC<LegacyUnifiedNavigationProps> = (props) => {
  useEffect(() => {
    logDeprecationWarning('UnifiedNavigation', 'ConsolidatedNavigation')
  }, [])

  return <ConsolidatedNavigation {...props} />
}

// ===== SMART NAVIGATION COMPATIBILITY =====

interface LegacySmartNavigationProps {
  profile: UserProfile | null
  wishlistItemCount: number
  loading: boolean
  maxSuggestions?: number
  showCategories?: boolean
  className?: string
  compact?: boolean
}

/**
 * @deprecated Use DesktopNavigation instead
 */
export const SmartNavigation: React.FC<LegacySmartNavigationProps> = ({
  profile,
  wishlistItemCount,
  loading,
  maxSuggestions = 6, // Legacy prop - ignored in new system
  showCategories = true,
  className = '',
  compact = false
}) => {
  useEffect(() => {
    logDeprecationWarning('SmartNavigation', 'DesktopNavigation')
  }, [])

  // Map legacy props to new system
  const mappedProps: ConsolidatedNavigationProps = {
    profile,
    wishlistItemCount,
    loading,
    variant: 'desktop',
    showSearch: true,
    showQuickSettings: showCategories,
    showBreadcrumbs: false,
    showLabels: true,
    compact,
    className
  }

  return <ConsolidatedNavigation {...mappedProps} />
}

// ===== PROFILE NAVIGATION COMPATIBILITY =====

interface LegacyProfileNavigationProps {
  profile: UserProfile | null
  wishlistItemCount: number
  loading: boolean
  className?: string
}

/**
 * @deprecated Use ConsolidatedNavigation with simplified config instead
 */
export const ProfileNavigation: React.FC<LegacyProfileNavigationProps> = ({
  profile,
  wishlistItemCount,
  loading,
  className = ''
}) => {
  useEffect(() => {
    logDeprecationWarning('ProfileNavigation', 'ConsolidatedNavigation')
  }, [])

  // Configure for simple profile navigation
  const mappedProps: ConsolidatedNavigationProps = {
    profile,
    wishlistItemCount,
    loading,
    variant: 'desktop',
    showSearch: false,
    showQuickSettings: false,
    showBreadcrumbs: false,
    showLabels: true,
    compact: false,
    className
  }

  return <ConsolidatedNavigation {...mappedProps} />
}

// ===== PROFILE BOTTOM NAV COMPATIBILITY =====

interface LegacyProfileBottomNavProps {
  className?: string
  onCategoryChange?: (category: string) => void
}

/**
 * @deprecated Use MobileNavigation instead
 */
export const ProfileBottomNav: React.FC<LegacyProfileBottomNavProps> = ({
  className = '',
  onCategoryChange
}) => {
  useEffect(() => {
    logDeprecationWarning('ProfileBottomNav', 'MobileNavigation')
  }, [])

  // Note: Legacy component didn't require profile/wishlist props
  // For compatibility, we'll use null values and show a warning
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.warn(
        '⚠️ ProfileBottomNav compatibility mode: profile and wishlistItemCount are required for full functionality. ' +
        'Please update to MobileNavigation with proper props.'
      )
    }
  }, [])

  const mappedProps: ConsolidatedNavigationProps = {
    profile: null, // Legacy compatibility - limited functionality
    wishlistItemCount: 0, // Legacy compatibility - limited functionality
    loading: false,
    variant: 'mobile',
    showSearch: false,
    showQuickSettings: false,
    showBreadcrumbs: false,
    showLabels: true,
    compact: false,
    className,
    onNavigate: onCategoryChange ? (href: string) => {
      // Extract category from href for backward compatibility
      const pathParts = href.split('/')
      const category = pathParts[2] || 'account'
      onCategoryChange(category)
    } : undefined
  }

  return <ConsolidatedNavigation {...mappedProps} />
}

// ===== MOBILE BOTTOM NAV COMPATIBILITY =====

interface LegacyMobileBottomNavProps {
  className?: string
  showLabels?: boolean
  variant?: 'default' | 'minimal'
}

/**
 * @deprecated Use MobileNavigation instead
 */
export const MobileBottomNav: React.FC<LegacyMobileBottomNavProps> = ({
  className = '',
  showLabels = true,
  variant = 'default'
}) => {
  useEffect(() => {
    logDeprecationWarning('MobileBottomNav', 'MobileNavigation')
  }, [])

  // Note: Legacy component didn't require profile/wishlist props
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.warn(
        '⚠️ MobileBottomNav compatibility mode: profile and wishlistItemCount are required for full functionality. ' +
        'Please update to MobileNavigation with proper props.'
      )
    }
  }, [])

  const mappedProps: ConsolidatedNavigationProps = {
    profile: null, // Legacy compatibility - limited functionality
    wishlistItemCount: 0, // Legacy compatibility - limited functionality
    loading: false,
    variant: 'mobile',
    showSearch: false,
    showQuickSettings: false,
    showBreadcrumbs: false,
    showLabels,
    compact: variant === 'minimal',
    className
  }

  return <ConsolidatedNavigation {...mappedProps} />
}

// ===== MIGRATION HELPERS =====

/**
 * Migration helper to check if legacy components are still in use
 */
export const checkLegacyUsage = () => {
  if (process.env.NODE_ENV === 'development') {
    // This would be called from a development tool or script
    console.group('🔍 Legacy Navigation Component Usage Check')
    console.log('Scanning for deprecated navigation components...')
    console.log('Run migration script: npm run migrate:navigation')
    console.groupEnd()
  }
}

/**
 * Props mapping utility for migration assistance
 */
export const mapLegacyProps = {
  UnifiedNavigation: (props: LegacyUnifiedNavigationProps): ConsolidatedNavigationProps => props,
  
  SmartNavigation: (props: LegacySmartNavigationProps): ConsolidatedNavigationProps => ({
    profile: props.profile,
    wishlistItemCount: props.wishlistItemCount,
    loading: props.loading,
    variant: 'desktop',
    showSearch: true,
    showQuickSettings: props.showCategories ?? true,
    showBreadcrumbs: false,
    showLabels: true,
    compact: props.compact ?? false,
    className: props.className ?? ''
  }),
  
  ProfileNavigation: (props: LegacyProfileNavigationProps): ConsolidatedNavigationProps => ({
    profile: props.profile,
    wishlistItemCount: props.wishlistItemCount,
    loading: props.loading,
    variant: 'desktop',
    showSearch: false,
    showQuickSettings: false,
    showBreadcrumbs: false,
    showLabels: true,
    compact: false,
    className: props.className ?? ''
  })
}

// ===== EXPORT LEGACY COMPONENTS =====

// Re-export with deprecation warnings
export {
  UnifiedNavigation as LegacyUnifiedNavigation,
  SmartNavigation as LegacySmartNavigation,
  ProfileNavigation as LegacyProfileNavigation,
  ProfileBottomNav as LegacyProfileBottomNav,
  MobileBottomNav as LegacyMobileBottomNav
}

// Default exports for drop-in replacement
export default {
  UnifiedNavigation,
  SmartNavigation,
  ProfileNavigation,
  ProfileBottomNav,
  MobileBottomNav,
  checkLegacyUsage,
  mapLegacyProps
}
