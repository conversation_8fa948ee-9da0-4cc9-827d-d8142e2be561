/**
 * Consolidated Navigation Component
 * 
 * Single, unified navigation component that replaces:
 * - UnifiedNavigation
 * - SmartNavigation  
 * - ProfileNavigation
 * - ProfileBottomNav
 * - MobileBottomNav
 * 
 * Features:
 * - Automatic responsive behavior (desktop → tablet → mobile)
 * - Shared state management via NavigationProvider
 * - Component factory pattern for variant generation
 * - Advanced search and filtering capabilities
 * - Keyboard shortcuts and accessibility
 * - Consistent styling and behavior
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

'use client'

import React, { useEffect, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, Settings, X, Command } from 'lucide-react'
import { UserProfile } from '@/types/profile'
import { NavigationProvider, useNavigation } from '@/lib/navigation/NavigationProvider'
import { createNavigation, NavigationVariant } from '@/lib/navigation/NavigationFactory'
import { useResponsiveNavigation } from '@/hooks/useResponsiveNavigation'
import { useNavigationShortcuts } from '@/hooks/useKeyboardShortcuts'
import { NavigationSearch } from './SearchComponents'
import { ContextualSuggestionsPanel } from './ContextualSuggestionsPanel'
import { SkipLinks } from './SkipLinks'
import { useNavigationAccessibility } from '@/hooks/useNavigationAccessibility'

// ===== TYPES =====

export interface ConsolidatedNavigationProps {
  profile: UserProfile | null
  wishlistItemCount: number
  loading?: boolean
  
  // Behavior options
  variant?: NavigationVariant | 'auto'
  showSearch?: boolean
  showQuickSettings?: boolean
  showBreadcrumbs?: boolean
  showLabels?: boolean
  compact?: boolean
  
  // Styling
  className?: string
  
  // Callbacks
  onNavigate?: (href: string) => void
  onSearchToggle?: (show: boolean) => void
  onSettingsToggle?: (show: boolean) => void
}

// ===== SEARCH COMPONENT =====

interface NavigationSearchProps {
  onClose: () => void
}

const NavigationSearch: React.FC<NavigationSearchProps> = ({ onClose }) => {
  const { state, setSearchQuery, searchItems } = useNavigation()
  const [localQuery, setLocalQuery] = React.useState('')
  
  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchQuery(localQuery)
    }, 150)
    
    return () => clearTimeout(timer)
  }, [localQuery, setSearchQuery])
  
  const results = useMemo(() => {
    return localQuery.trim() ? searchItems(localQuery) : []
  }, [localQuery, searchItems])
  
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="nav-search-container"
    >
      {/* Search Input */}
      <div className="relative">
        <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <input
          type="text"
          value={localQuery}
          onChange={(e) => setLocalQuery(e.target.value)}
          placeholder="Search navigation..."
          className="nav-search-input"
          autoFocus
          aria-label="Search navigation items"
        />
        <button
          onClick={onClose}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
          aria-label="Close search"
        >
          <X size={16} />
        </button>
      </div>
      
      {/* Search Results */}
      <AnimatePresence>
        {results.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="nav-search-results"
          >
            {results.slice(0, 8).map((item) => (
              <motion.a
                key={item.id}
                href={item.href}
                className="nav-search-result"
                whileHover={{ backgroundColor: 'rgba(139, 92, 246, 0.1)' }}
                onClick={onClose}
              >
                <item.icon size={16} className="text-gray-400" />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm text-gray-200 truncate">
                    {item.label}
                  </div>
                  <div className="text-xs text-gray-500 truncate">
                    {item.description}
                  </div>
                </div>
                {item.badge && (
                  <span className={`nav-badge nav-badge-${item.badge.type}`}>
                    {item.badge.count}
                  </span>
                )}
              </motion.a>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Search Hint */}
      <div className="nav-search-hint">
        <Command size={12} />
        <span>Press Ctrl+K to search</span>
      </div>
    </motion.div>
  )
}

// ===== QUICK SETTINGS COMPONENT =====

interface QuickSettingsProps {
  profile: UserProfile | null
  onToggle: (setting: string, value: boolean) => void
}

const QuickSettings: React.FC<QuickSettingsProps> = ({ profile, onToggle }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="nav-quick-settings"
    >
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-medium text-gray-200">Quick Settings</h4>
        <Settings size={14} className="text-gray-400" />
      </div>
      
      <div className="space-y-2">
        <label className="nav-setting-item">
          <input
            type="checkbox"
            defaultChecked={true}
            onChange={(e) => onToggle('notifications', e.target.checked)}
            className="nav-checkbox"
          />
          <span className="text-sm text-gray-300">Notifications</span>
        </label>
        
        <label className="nav-setting-item">
          <input
            type="checkbox"
            defaultChecked={false}
            onChange={(e) => onToggle('compactMode', e.target.checked)}
            className="nav-checkbox"
          />
          <span className="text-sm text-gray-300">Compact Mode</span>
        </label>
        
        <label className="nav-setting-item">
          <input
            type="checkbox"
            defaultChecked={true}
            onChange={(e) => onToggle('animations', e.target.checked)}
            className="nav-checkbox"
          />
          <span className="text-sm text-gray-300">Animations</span>
        </label>
      </div>
    </motion.div>
  )
}

// ===== MAIN COMPONENT =====

/**
 * Internal Navigation Component (wrapped by provider)
 */
const NavigationInternal: React.FC<Omit<ConsolidatedNavigationProps, 'profile' | 'wishlistItemCount'>> = ({
  variant = 'auto',
  showSearch = true,
  showQuickSettings = true,
  showBreadcrumbs = false,
  showLabels = true,
  compact = false,
  className = '',
  onNavigate,
  onSearchToggle,
  onSettingsToggle
}) => {
  const { state, toggleSearch, resetSearch } = useNavigation()
  const { isMobile, isTablet } = useResponsiveNavigation()
  const accessibility = useNavigationAccessibility({
    enableKeyboardShortcuts: true,
    enableFocusManagement: true,
    enableAnnouncements: true,
    enableSkipLinks: true
  })
  
  // Determine effective variant
  const effectiveVariant: NavigationVariant = useMemo(() => {
    if (variant !== 'auto') return variant
    if (isMobile) return 'mobile'
    if (isTablet) return 'tablet'
    return 'desktop'
  }, [variant, isMobile, isTablet])
  
  // Keyboard shortcuts
  useNavigationShortcuts({
    onToggleSearch: () => {
      toggleSearch()
      onSearchToggle?.(!state.showSearch)
    },
    onCloseModal: () => {
      if (state.showSearch) {
        resetSearch()
        onSearchToggle?.(false)
      }
    }
  })
  
  // Create navigation component
  const NavigationComponent = useMemo(() => {
    return createNavigation({
      variant: effectiveVariant,
      showSearch,
      showQuickSettings,
      showBreadcrumbs,
      showLabels,
      compact,
      className
    })
  }, [effectiveVariant, showSearch, showQuickSettings, showBreadcrumbs, showLabels, compact, className])
  
  return (
    <div className="relative">
      {/* Skip Links */}
      {accessibility.skipLinksVisible && <SkipLinks />}

      {/* Search Overlay */}
      <AnimatePresence>
        {state.showSearch && showSearch && (
          <NavigationSearch
            items={state.allItems}
            categories={state.categories}
            searchEngine={state.searchEngine}
            onNavigate={(item) => {
              state.trackNavigation(item, state.searchQuery)
              onNavigate?.(item.href)
            }}
            onClose={() => {
              resetSearch()
              onSearchToggle?.(false)
            }}
            placeholder="Search navigation..."
            maxResults={8}
            showCategories={true}
            showRecent={true}
          />
        )}
      </AnimatePresence>
      
      {/* Quick Settings Overlay */}
      <AnimatePresence>
        {state.showQuickSettings && showQuickSettings && (
          <QuickSettings
            profile={null} // Will be passed from provider context
            onToggle={(setting, value) => {
              console.log('Toggle setting:', setting, value)
              onSettingsToggle?.(value)
            }}
          />
        )}
      </AnimatePresence>

      {/* Contextual Suggestions Panel */}
      {state.showSuggestions && state.contextualSuggestions.length > 0 && (
        <div className="mb-4">
          <ContextualSuggestionsPanel
            suggestions={state.contextualSuggestions}
            onSuggestionClick={(suggestion) => {
              state.trackBehavior({
                type: 'navigation',
                data: { from: state.suggestionContext?.currentPage, to: suggestion.item.href }
              })
              onNavigate?.(suggestion.item.href)
            }}
            onSuggestionDismiss={(suggestionId, reason) => {
              state.trackBehavior({
                type: 'suggestion_dismissed',
                data: { suggestionId, reason }
              })
            }}
            maxSuggestions={3}
            showReasons={true}
            compact={effectiveVariant === 'mobile'}
          />
        </div>
      )}

      {/* Main Navigation */}
      <nav
        ref={accessibility.navigationRef}
        role="navigation"
        aria-label="Main navigation"
        className={`
          ${accessibility.isKeyboardNavigating ? 'keyboard-navigation' : ''}
          ${accessibility.isHighContrastMode ? 'high-contrast' : ''}
          ${accessibility.isReducedMotion ? 'reduced-motion' : ''}
        `}
        onFocus={() => {
          accessibility.announceNavigationChange('Navigation menu')
        }}
      >
        <NavigationComponent
          profile={state.profile}
          wishlistItemCount={state.wishlistItemCount}
          onNavigate={(href) => {
            accessibility.announceNavigationChange(`Navigating to ${href}`)
            onNavigate?.(href)
          }}
        />
      </nav>
    </div>
  )
}

/**
 * Main Consolidated Navigation Component
 */
export const ConsolidatedNavigation: React.FC<ConsolidatedNavigationProps> = ({
  profile,
  wishlistItemCount,
  loading = false,
  ...props
}) => {
  return (
    <NavigationProvider
      profile={profile}
      wishlistItemCount={wishlistItemCount}
    >
      <NavigationInternal {...props} />
    </NavigationProvider>
  )
}

/**
 * Pre-configured navigation variants for easy usage
 */
export const DesktopNavigation: React.FC<ConsolidatedNavigationProps> = (props) => (
  <ConsolidatedNavigation {...props} variant="desktop" />
)

export const MobileNavigation: React.FC<ConsolidatedNavigationProps> = (props) => (
  <ConsolidatedNavigation {...props} variant="mobile" showSearch={false} showQuickSettings={false} />
)

export const TabletNavigation: React.FC<ConsolidatedNavigationProps> = (props) => (
  <ConsolidatedNavigation {...props} variant="tablet" compact={true} />
)

export const ResponsiveNavigation: React.FC<ConsolidatedNavigationProps> = (props) => (
  <ConsolidatedNavigation {...props} variant="auto" />
)

export default ConsolidatedNavigation
