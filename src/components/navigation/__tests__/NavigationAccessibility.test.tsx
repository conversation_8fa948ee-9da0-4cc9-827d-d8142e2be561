/**
 * Accessibility-Focused Test Suite for Navigation Components
 * 
 * Comprehensive testing for:
 * - WCAG 2.1 AA compliance
 * - Screen reader compatibility
 * - Keyboard navigation
 * - Focus management
 * - Skip links functionality
 * - High contrast mode
 * - Reduced motion preferences
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { axe, toHaveNoViolations } from 'jest-axe'
import { ConsolidatedNavigation } from '../ConsolidatedNavigation'
import { SkipLinks } from '../SkipLinks'
import { NavigationProvider } from '@/lib/navigation/NavigationProvider'
import { UserProfile } from '@/types/profile'

expect.extend(toHaveNoViolations)

// ===== TEST SETUP =====

const mockProfile: UserProfile = {
  id: 'test-user',
  displayName: 'Test User',
  email: '<EMAIL>',
  tier: 'premium',
  points: 1500,
  avatar: null,
  createdAt: new Date(),
  updatedAt: new Date()
}

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <NavigationProvider profile={mockProfile} wishlistItemCount={5}>
      {component}
    </NavigationProvider>
  )
}

// Mock matchMedia for responsive tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// ===== WCAG 2.1 AA COMPLIANCE TESTS =====

describe('Navigation - WCAG 2.1 AA Compliance', () => {
  test('passes automated accessibility audit', async () => {
    const { container } = renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    const results = await axe(container, {
      rules: {
        // Enable all WCAG 2.1 AA rules
        'color-contrast': { enabled: true },
        'keyboard-navigation': { enabled: true },
        'focus-visible': { enabled: true },
        'aria-labels': { enabled: true }
      }
    })
    
    expect(results).toHaveNoViolations()
  })

  test('has proper heading hierarchy', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    // Check for proper heading structure
    const headings = screen.getAllByRole('heading')
    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1))
      if (index > 0) {
        const prevLevel = parseInt(headings[index - 1].tagName.charAt(1))
        expect(level).toBeLessThanOrEqual(prevLevel + 1)
      }
    })
  })

  test('has sufficient color contrast', () => {
    const { container } = renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    // Check computed styles for contrast ratios
    const navItems = container.querySelectorAll('.nav-item')
    navItems.forEach(item => {
      const styles = window.getComputedStyle(item)
      // Color contrast should meet WCAG AA standards (4.5:1 for normal text)
      expect(styles.color).toBeDefined()
      expect(styles.backgroundColor).toBeDefined()
    })
  })

  test('has proper touch target sizes', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        variant="mobile"
      />
    )
    
    const touchTargets = screen.getAllByRole('button')
    touchTargets.forEach(target => {
      const rect = target.getBoundingClientRect()
      // WCAG requires minimum 44x44px touch targets
      expect(rect.width).toBeGreaterThanOrEqual(44)
      expect(rect.height).toBeGreaterThanOrEqual(44)
    })
  })
})

// ===== KEYBOARD NAVIGATION TESTS =====

describe('Navigation - Keyboard Navigation', () => {
  test('supports full keyboard navigation', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    // Tab through all interactive elements
    const interactiveElements = screen.getAllByRole('button')
    
    for (let i = 0; i < interactiveElements.length; i++) {
      await user.tab()
      expect(document.activeElement).toBeInTheDocument()
    }
  })

  test('implements roving tabindex correctly', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    // First navigation item should have tabindex="0"
    const navItems = screen.getAllByRole('menuitem')
    if (navItems.length > 0) {
      expect(navItems[0]).toHaveAttribute('tabindex', '0')
      
      // Other items should have tabindex="-1"
      for (let i = 1; i < navItems.length; i++) {
        expect(navItems[i]).toHaveAttribute('tabindex', '-1')
      }
    }
  })

  test('handles arrow key navigation', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    // Focus first navigation item
    const navItems = screen.getAllByRole('menuitem')
    if (navItems.length > 1) {
      navItems[0].focus()
      
      // Arrow down should move to next item
      await user.keyboard('{ArrowDown}')
      expect(navItems[1]).toHaveFocus()
      
      // Arrow up should move back
      await user.keyboard('{ArrowUp}')
      expect(navItems[0]).toHaveFocus()
    }
  })

  test('supports keyboard shortcuts', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        showSearch={true}
      />
    )
    
    // Ctrl+K should open search
    await user.keyboard('{Control>}k{/Control}')
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText(/search/i)).toBeInTheDocument()
    })
    
    // Escape should close search
    await user.keyboard('{Escape}')
    
    await waitFor(() => {
      expect(screen.queryByPlaceholderText(/search/i)).not.toBeInTheDocument()
    })
  })
})

// ===== SKIP LINKS TESTS =====

describe('Navigation - Skip Links', () => {
  test('renders skip links correctly', () => {
    render(<SkipLinks />)
    
    // Skip links should be present but initially hidden
    const skipLinks = screen.getByRole('navigation', { name: /skip navigation/i })
    expect(skipLinks).toBeInTheDocument()
  })

  test('shows skip links on keyboard navigation', async () => {
    const user = userEvent.setup()
    
    render(<SkipLinks />)
    
    // Tab should make skip links visible
    await user.tab()
    
    const skipToMain = screen.getByText(/skip to main content/i)
    expect(skipToMain).toBeVisible()
  })

  test('skip links navigate to correct targets', async () => {
    const user = userEvent.setup()
    
    // Add target elements to document
    document.body.innerHTML += `
      <main id="main-content">Main Content</main>
      <nav id="navigation">Navigation</nav>
    `
    
    render(<SkipLinks />)
    
    await user.tab()
    
    const skipToMain = screen.getByText(/skip to main content/i)
    await user.click(skipToMain)
    
    // Should focus main content
    const mainContent = document.getElementById('main-content')
    expect(mainContent).toHaveFocus()
  })
})

// ===== SCREEN READER TESTS =====

describe('Navigation - Screen Reader Support', () => {
  test('has proper ARIA labels and descriptions', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    const navigation = screen.getByRole('navigation')
    expect(navigation).toHaveAttribute('aria-label', 'Main navigation')
    
    // Check for ARIA descriptions on interactive elements
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(
        button.hasAttribute('aria-label') || 
        button.hasAttribute('aria-describedby') ||
        button.textContent
      ).toBeTruthy()
    })
  })

  test('announces navigation changes', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    // Look for ARIA live regions
    const liveRegions = screen.getAllByRole('status', { hidden: true })
    expect(liveRegions.length).toBeGreaterThan(0)
    
    // Navigation changes should update live regions
    const navItem = screen.getByText(/account/i)
    await user.click(navItem)
    
    // Check if live region was updated (implementation specific)
    expect(liveRegions[0]).toBeInTheDocument()
  })

  test('provides context for complex interactions', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        showSearch={true}
      />
    )
    
    // Search should have proper labeling
    const searchButton = screen.getByLabelText(/search/i)
    expect(searchButton).toHaveAttribute('aria-label')
    
    // Wishlist badge should have context
    const wishlistBadge = screen.getByText('5')
    expect(wishlistBadge.closest('[aria-label]')).toBeInTheDocument()
  })
})

// ===== FOCUS MANAGEMENT TESTS =====

describe('Navigation - Focus Management', () => {
  test('maintains focus within modal overlays', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        showSearch={true}
      />
    )
    
    // Open search overlay
    await user.keyboard('{Control>}k{/Control}')
    
    const searchInput = await screen.findByPlaceholderText(/search/i)
    expect(searchInput).toHaveFocus()
    
    // Tab should stay within search overlay
    await user.tab()
    const focusedElement = document.activeElement
    
    // Should still be within search container
    expect(searchInput.closest('.nav-search-container')).toContain(focusedElement)
  })

  test('restores focus after modal closes', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        showSearch={true}
      />
    )
    
    // Focus a navigation item first
    const navItem = screen.getByText(/account/i)
    navItem.focus()
    const originalFocus = document.activeElement
    
    // Open and close search
    await user.keyboard('{Control>}k{/Control}')
    await user.keyboard('{Escape}')
    
    // Focus should return to original element
    await waitFor(() => {
      expect(document.activeElement).toBe(originalFocus)
    })
  })

  test('handles focus indicators correctly', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    // Focus should show visual indicators
    const navItems = screen.getAllByRole('menuitem')
    if (navItems.length > 0) {
      await user.tab()
      
      // Check for focus indicator classes or styles
      const focusedElement = document.activeElement
      expect(focusedElement).toHaveClass(/focus|focused/)
    }
  })
})

// ===== RESPONSIVE ACCESSIBILITY TESTS =====

describe('Navigation - Responsive Accessibility', () => {
  test('maintains accessibility on mobile', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      value: 375,
      writable: true
    })
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        variant="mobile"
      />
    )
    
    // Mobile navigation should still be accessible
    const navigation = screen.getByRole('navigation')
    expect(navigation).toHaveAttribute('aria-label')
    
    // Touch targets should be appropriately sized
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      const rect = button.getBoundingClientRect()
      expect(rect.width).toBeGreaterThanOrEqual(44)
      expect(rect.height).toBeGreaterThanOrEqual(44)
    })
  })

  test('adapts to user preferences', () => {
    // Mock reduced motion preference
    Object.defineProperty(window, 'matchMedia', {
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
      })),
    })
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    // Should respect reduced motion preferences
    const navigation = screen.getByRole('navigation')
    expect(navigation).toHaveClass(/reduced-motion/)
  })
})
