/**
 * Comprehensive Test Suite for ConsolidatedNavigation
 * 
 * Tests cover:
 * - Component rendering and props
 * - Navigation functionality
 * - Accessibility compliance
 * - Keyboard shortcuts
 * - Search functionality
 * - Responsive behavior
 * - State management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import React from 'react'
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { axe, toHaveNoViolations } from 'jest-axe'
import { ConsolidatedNavigation, DesktopNavigation, MobileNavigation } from '../ConsolidatedNavigation'
import { NavigationProvider } from '@/lib/navigation/NavigationProvider'
import { UserProfile } from '@/types/profile'

// Extend Jest matchers
expect.extend(toHaveNoViolations)

// ===== TEST UTILITIES =====

const mockProfile: UserProfile = {
  id: 'test-user',
  displayName: 'Test User',
  email: '<EMAIL>',
  tier: 'premium',
  points: 1500,
  avatar: null,
  createdAt: new Date(),
  updatedAt: new Date()
}

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <NavigationProvider profile={mockProfile} wishlistItemCount={5}>
      {component}
    </NavigationProvider>
  )
}

// Mock Next.js router
jest.mock('next/navigation', () => ({
  usePathname: () => '/profile/account',
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn()
  })
}))

// Mock framer-motion for testing
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    nav: ({ children, ...props }: any) => <nav {...props}>{children}</nav>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
  useAnimation: () => ({
    start: jest.fn(),
    stop: jest.fn()
  })
}))

// ===== BASIC RENDERING TESTS =====

describe('ConsolidatedNavigation - Basic Rendering', () => {
  test('renders without crashing', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    expect(screen.getByRole('navigation')).toBeInTheDocument()
  })

  test('renders with all required props', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        loading={false}
        variant="desktop"
        showSearch={true}
        showQuickSettings={true}
        showBreadcrumbs={false}
        showLabels={true}
        compact={false}
        className="test-nav"
      />
    )
    
    const navigation = screen.getByRole('navigation')
    expect(navigation).toHaveClass('test-nav')
    expect(navigation).toHaveAttribute('aria-label', 'Main navigation')
  })

  test('renders desktop variant correctly', () => {
    renderWithProvider(
      <DesktopNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    expect(screen.getByRole('navigation')).toBeInTheDocument()
    // Desktop variant should show search by default
    expect(screen.getByLabelText(/search/i)).toBeInTheDocument()
  })

  test('renders mobile variant correctly', () => {
    renderWithProvider(
      <MobileNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    expect(screen.getByRole('navigation')).toBeInTheDocument()
    // Mobile variant should not show search by default
    expect(screen.queryByLabelText(/search/i)).not.toBeInTheDocument()
  })
})

// ===== NAVIGATION FUNCTIONALITY TESTS =====

describe('ConsolidatedNavigation - Navigation Functionality', () => {
  test('calls onNavigate when navigation item is clicked', async () => {
    const mockOnNavigate = jest.fn()
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        onNavigate={mockOnNavigate}
      />
    )
    
    // Find and click a navigation item
    const accountLink = screen.getByText(/account/i)
    await user.click(accountLink)
    
    expect(mockOnNavigate).toHaveBeenCalledWith(expect.stringContaining('/profile/account'))
  })

  test('shows wishlist count badge', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    expect(screen.getByText('5')).toBeInTheDocument()
  })

  test('handles loading state', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        loading={true}
      />
    )
    
    // Should show loading indicators or skeleton
    expect(screen.getByRole('navigation')).toBeInTheDocument()
  })
})

// ===== ACCESSIBILITY TESTS =====

describe('ConsolidatedNavigation - Accessibility', () => {
  test('has no accessibility violations', async () => {
    const { container } = renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    const results = await axe(container)
    expect(results).toHaveNoViolations()
  })

  test('has proper ARIA labels', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    const navigation = screen.getByRole('navigation')
    expect(navigation).toHaveAttribute('aria-label', 'Main navigation')
  })

  test('supports keyboard navigation', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        showSearch={true}
      />
    )
    
    // Tab through navigation items
    await user.tab()
    expect(document.activeElement).toBeInTheDocument()
    
    // Test keyboard shortcuts
    await user.keyboard('{Control>}k{/Control}')
    
    // Search should be visible after Ctrl+K
    await waitFor(() => {
      expect(screen.getByPlaceholderText(/search/i)).toBeInTheDocument()
    })
  })

  test('has proper focus management', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        showSearch={true}
      />
    )
    
    // Open search with keyboard shortcut
    await user.keyboard('{Control>}k{/Control}')
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText(/search/i)
      expect(searchInput).toHaveFocus()
    })
    
    // Close search with Escape
    await user.keyboard('{Escape}')
    
    await waitFor(() => {
      expect(screen.queryByPlaceholderText(/search/i)).not.toBeInTheDocument()
    })
  })

  test('supports screen readers with live regions', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    // Check for ARIA live regions
    const liveRegions = screen.getAllByRole('status', { hidden: true })
    expect(liveRegions.length).toBeGreaterThan(0)
  })
})

// ===== SEARCH FUNCTIONALITY TESTS =====

describe('ConsolidatedNavigation - Search Functionality', () => {
  test('opens search overlay with Ctrl+K', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        showSearch={true}
      />
    )
    
    await user.keyboard('{Control>}k{/Control}')
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText(/search/i)).toBeInTheDocument()
    })
  })

  test('performs fuzzy search', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        showSearch={true}
      />
    )
    
    // Open search
    await user.keyboard('{Control>}k{/Control}')
    
    const searchInput = await screen.findByPlaceholderText(/search/i)
    
    // Type search query
    await user.type(searchInput, 'accnt') // Intentional typo to test fuzzy matching
    
    await waitFor(() => {
      // Should find "Account" despite typo
      expect(screen.getByText(/account/i)).toBeInTheDocument()
    })
  })

  test('navigates with arrow keys in search results', async () => {
    const user = userEvent.setup()
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        showSearch={true}
      />
    )
    
    // Open search and type query
    await user.keyboard('{Control>}k{/Control}')
    const searchInput = await screen.findByPlaceholderText(/search/i)
    await user.type(searchInput, 'profile')
    
    // Use arrow keys to navigate results
    await user.keyboard('{ArrowDown}')
    await user.keyboard('{ArrowDown}')
    await user.keyboard('{ArrowUp}')
    
    // Should highlight a search result
    const selectedResult = screen.getByRole('option', { selected: true })
    expect(selectedResult).toBeInTheDocument()
  })
})

// ===== RESPONSIVE BEHAVIOR TESTS =====

describe('ConsolidatedNavigation - Responsive Behavior', () => {
  test('adapts to mobile viewport', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375
    })
    
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        variant="auto"
      />
    )
    
    // Should render mobile variant
    expect(screen.getByRole('navigation')).toBeInTheDocument()
  })

  test('shows compact mode correctly', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        compact={true}
      />
    )
    
    const navigation = screen.getByRole('navigation')
    expect(navigation).toBeInTheDocument()
    // Compact mode should have different styling
  })
})

// ===== STATE MANAGEMENT TESTS =====

describe('ConsolidatedNavigation - State Management', () => {
  test('maintains navigation state across renders', () => {
    const { rerender } = renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )
    
    // Re-render with different props
    rerender(
      <NavigationProvider profile={mockProfile} wishlistItemCount={10}>
        <ConsolidatedNavigation
          profile={mockProfile}
          wishlistItemCount={10}
        />
      </NavigationProvider>
    )
    
    // Should update wishlist count
    expect(screen.getByText('10')).toBeInTheDocument()
  })

  test('handles contextual suggestions', async () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        showQuickSettings={true}
      />
    )

    // Should show contextual suggestions based on current context
    await waitFor(() => {
      // Look for suggestion indicators or panels
      const suggestions = screen.queryAllByText(/suggestion/i)
      expect(suggestions.length).toBeGreaterThanOrEqual(0)
    })
  })
})

// ===== PERFORMANCE TESTS =====

describe('ConsolidatedNavigation - Performance', () => {
  test('renders within performance budget', () => {
    const startTime = performance.now()

    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )

    const endTime = performance.now()
    const renderTime = endTime - startTime

    // Should render within 100ms
    expect(renderTime).toBeLessThan(100)
  })

  test('handles large wishlist counts efficiently', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={999}
      />
    )

    expect(screen.getByText('999')).toBeInTheDocument()
  })

  test('debounces search input correctly', async () => {
    const user = userEvent.setup()

    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
        showSearch={true}
      />
    )

    // Open search
    await user.keyboard('{Control>}k{/Control}')
    const searchInput = await screen.findByPlaceholderText(/search/i)

    // Type rapidly to test debouncing
    await user.type(searchInput, 'test query', { delay: 10 })

    // Should handle rapid typing without performance issues
    expect(searchInput).toHaveValue('test query')
  })
})

// ===== INTEGRATION TESTS =====

describe('ConsolidatedNavigation - Integration', () => {
  test('integrates with NavigationProvider correctly', () => {
    renderWithProvider(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )

    // Should have access to provider context
    expect(screen.getByRole('navigation')).toBeInTheDocument()
  })

  test('works without NavigationProvider (standalone mode)', () => {
    // Test direct usage without provider
    render(
      <ConsolidatedNavigation
        profile={mockProfile}
        wishlistItemCount={5}
      />
    )

    expect(screen.getByRole('navigation')).toBeInTheDocument()
  })
})
