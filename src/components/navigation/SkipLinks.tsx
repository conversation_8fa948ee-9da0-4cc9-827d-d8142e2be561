/**
 * Skip Links Component
 * 
 * Accessibility component providing skip navigation links for keyboard users
 * and screen readers to quickly navigate to main content areas.
 * 
 * Features:
 * - Skip to main content
 * - Skip to navigation
 * - Skip to search
 * - Skip to footer
 * - Keyboard activation (Tab to show, Enter to navigate)
 * - Screen reader optimized
 * - WCAG 2.1 AA compliant
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ArrowDown, 
  Navigation, 
  Search, 
  Home, 
  Settings,
  ShoppingCart,
  User
} from 'lucide-react'

// ===== TYPES =====

export interface SkipLink {
  id: string
  label: string
  target: string
  icon?: React.ComponentType<{ size?: number; className?: string }>
  shortcut?: string
  description?: string
}

export interface SkipLinksProps {
  links?: SkipLink[]
  className?: string
  showOnFocus?: boolean
  autoHide?: boolean
  autoHideDelay?: number
}

// ===== DEFAULT SKIP LINKS =====

const defaultSkipLinks: SkipLink[] = [
  {
    id: 'main',
    label: 'Skip to main content',
    target: 'main, [role="main"], #main-content',
    icon: Home,
    shortcut: 'Alt+1',
    description: 'Jump to the main content area'
  },
  {
    id: 'navigation',
    label: 'Skip to navigation',
    target: 'nav, [role="navigation"], #navigation',
    icon: Navigation,
    shortcut: 'Alt+2',
    description: 'Jump to the main navigation menu'
  },
  {
    id: 'search',
    label: 'Skip to search',
    target: '[role="search"], #search, .search-container',
    icon: Search,
    shortcut: 'Alt+3',
    description: 'Jump to the search functionality'
  },
  {
    id: 'profile',
    label: 'Skip to profile',
    target: '#profile, .profile-section, [aria-label*="profile"]',
    icon: User,
    shortcut: 'Alt+4',
    description: 'Jump to user profile section'
  },
  {
    id: 'cart',
    label: 'Skip to shopping cart',
    target: '#cart, .cart-section, [aria-label*="cart"]',
    icon: ShoppingCart,
    shortcut: 'Alt+5',
    description: 'Jump to shopping cart'
  },
  {
    id: 'footer',
    label: 'Skip to footer',
    target: 'footer, [role="contentinfo"], #footer',
    icon: ArrowDown,
    shortcut: 'Alt+9',
    description: 'Jump to the footer section'
  }
]

// ===== MAIN COMPONENT =====

export const SkipLinks: React.FC<SkipLinksProps> = ({
  links = defaultSkipLinks,
  className = '',
  showOnFocus = true,
  autoHide = true,
  autoHideDelay = 5000
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [focusedIndex, setFocusedIndex] = useState(-1)

  // ===== VISIBILITY MANAGEMENT =====

  useEffect(() => {
    if (!showOnFocus) {
      setIsVisible(true)
      return
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      // Show skip links on Tab key
      if (event.key === 'Tab' && !event.shiftKey) {
        setIsVisible(true)
      }
    }

    const handleFocusIn = (event: FocusEvent) => {
      const target = event.target as HTMLElement
      if (target.closest('.skip-links')) {
        setIsVisible(true)
      }
    }

    const handleFocusOut = (event: FocusEvent) => {
      const target = event.target as HTMLElement
      const relatedTarget = event.relatedTarget as HTMLElement
      
      if (!target.closest('.skip-links') && !relatedTarget?.closest('.skip-links')) {
        if (autoHide) {
          setTimeout(() => setIsVisible(false), autoHideDelay)
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('focusin', handleFocusIn)
    document.addEventListener('focusout', handleFocusOut)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('focusin', handleFocusIn)
      document.removeEventListener('focusout', handleFocusOut)
    }
  }, [showOnFocus, autoHide, autoHideDelay])

  // ===== NAVIGATION HANDLERS =====

  const handleSkipToTarget = useCallback((targetSelector: string, label: string) => {
    // Try multiple selectors
    const selectors = targetSelector.split(', ')
    let targetElement: HTMLElement | null = null

    for (const selector of selectors) {
      targetElement = document.querySelector(selector.trim()) as HTMLElement
      if (targetElement) break
    }

    if (targetElement) {
      // Make element focusable if it isn't already
      const originalTabIndex = targetElement.getAttribute('tabindex')
      if (!targetElement.hasAttribute('tabindex')) {
        targetElement.setAttribute('tabindex', '-1')
      }

      // Focus the element
      targetElement.focus()

      // Scroll into view
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })

      // Announce to screen readers
      const announcement = `Skipped to ${label.toLowerCase().replace('skip to ', '')}`
      announceToScreenReader(announcement)

      // Restore original tabindex after a delay
      if (!originalTabIndex) {
        setTimeout(() => {
          targetElement?.removeAttribute('tabindex')
        }, 1000)
      }

      // Hide skip links
      if (autoHide) {
        setIsVisible(false)
      }
    } else {
      // Announce that target wasn't found
      announceToScreenReader(`Could not find ${label.toLowerCase().replace('skip to ', '')} section`)
    }
  }, [autoHide])

  const handleKeyDown = useCallback((event: React.KeyboardEvent, index: number) => {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        setFocusedIndex((prev) => (prev + 1) % links.length)
        break
      case 'ArrowUp':
        event.preventDefault()
        setFocusedIndex((prev) => prev === 0 ? links.length - 1 : prev - 1)
        break
      case 'Home':
        event.preventDefault()
        setFocusedIndex(0)
        break
      case 'End':
        event.preventDefault()
        setFocusedIndex(links.length - 1)
        break
      case 'Escape':
        event.preventDefault()
        setIsVisible(false)
        setFocusedIndex(-1)
        break
    }
  }, [links.length])

  // ===== UTILITY FUNCTIONS =====

  const announceToScreenReader = (message: string) => {
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', 'assertive')
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = message
    
    document.body.appendChild(announcement)
    
    setTimeout(() => {
      document.body.removeChild(announcement)
    }, 1000)
  }

  // ===== RENDER =====

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.nav
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
          className={`skip-links fixed top-0 left-0 right-0 z-[9999] ${className}`}
          role="navigation"
          aria-label="Skip navigation links"
        >
          <div className="bg-gray-900 border-b border-accent-500 shadow-lg">
            <div className="container mx-auto px-4 py-2">
              <div className="flex flex-wrap gap-2 items-center">
                <span className="text-sm text-gray-300 font-medium mr-4">
                  Skip to:
                </span>
                
                {links.map((link, index) => {
                  const Icon = link.icon
                  const isFocused = index === focusedIndex
                  
                  return (
                    <button
                      key={link.id}
                      onClick={() => handleSkipToTarget(link.target, link.label)}
                      onKeyDown={(e) => handleKeyDown(e, index)}
                      onFocus={() => setFocusedIndex(index)}
                      onBlur={() => setFocusedIndex(-1)}
                      className={`
                        skip-link-button
                        inline-flex items-center space-x-2 px-3 py-1.5 rounded-md
                        text-sm font-medium transition-all duration-200
                        border border-transparent
                        focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-900
                        ${isFocused
                          ? 'bg-accent-500 text-white border-accent-400'
                          : 'bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white'
                        }
                      `}
                      aria-describedby={link.description ? `${link.id}-desc` : undefined}
                      title={link.description}
                    >
                      {Icon && (
                        <Icon 
                          size={16} 
                          className={isFocused ? 'text-white' : 'text-gray-400'} 
                        />
                      )}
                      <span>{link.label}</span>
                      {link.shortcut && (
                        <kbd className="hidden sm:inline-block px-1.5 py-0.5 bg-gray-700 text-gray-300 rounded text-xs font-mono">
                          {link.shortcut}
                        </kbd>
                      )}
                    </button>
                  )
                })}
                
                <button
                  onClick={() => setIsVisible(false)}
                  className="ml-auto p-1.5 text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-900 rounded"
                  aria-label="Hide skip links"
                  title="Hide skip links (Escape)"
                >
                  <Settings size={16} />
                </button>
              </div>
              
              {/* Instructions */}
              <div className="mt-2 text-xs text-gray-500">
                Use arrow keys to navigate, Enter to select, Escape to hide
              </div>
            </div>
          </div>
          
          {/* Hidden descriptions for screen readers */}
          {links.map((link) => (
            link.description && (
              <div
                key={`${link.id}-desc`}
                id={`${link.id}-desc`}
                className="sr-only"
              >
                {link.description}
              </div>
            )
          ))}
        </motion.nav>
      )}
    </AnimatePresence>
  )
}

// ===== SKIP LINKS PROVIDER =====

export const SkipLinksProvider: React.FC<{
  children: React.ReactNode
  customLinks?: SkipLink[]
}> = ({ children, customLinks }) => {
  return (
    <>
      <SkipLinks links={customLinks} />
      {children}
    </>
  )
}

export default SkipLinks
