/**
 * Dynamic Route Error Boundary Component
 * 
 * Specialized error boundary for dynamic routes with enhanced error handling,
 * parameter validation feedback, and recovery mechanisms.
 * 
 * Features:
 * - Route-specific error handling
 * - Parameter validation error display
 * - User-friendly error messages
 * - Recovery suggestions
 * - Error reporting integration
 * - Responsive design
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { Component, ReactNode } from 'react'
import { motion } from 'framer-motion'
import { 
  AlertTriangle, 
  RefreshCw, 
  ArrowLeft, 
  Home, 
  Search,
  Bug,
  Shield
} from 'lucide-react'
import Link from 'next/link'
import { RouteValidationError } from '@/lib/utils/routeValidation'

// ===== TYPES =====

interface DynamicRouteErrorBoundaryProps {
  children: ReactNode
  routePath: string
  fallbackRoute?: string
  showReportButton?: boolean
  customErrorMessage?: string
}

interface DynamicRouteErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  validationError: RouteValidationError | null
}

// ===== ERROR BOUNDARY COMPONENT =====

export class DynamicRouteErrorBoundary extends Component<
  DynamicRouteErrorBoundaryProps,
  DynamicRouteErrorBoundaryState
> {
  constructor(props: DynamicRouteErrorBoundaryProps) {
    super(props)
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      validationError: null
    }
  }

  static getDerivedStateFromError(error: Error): Partial<DynamicRouteErrorBoundaryState> {
    // Check if this is a validation error
    const validationError = (error as any).validationError as RouteValidationError | undefined
    
    return {
      hasError: true,
      error,
      validationError: validationError || null
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      errorInfo
    })

    // Log error for monitoring
    console.error('Dynamic Route Error Boundary caught an error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      routePath: this.props.routePath,
      timestamp: new Date().toISOString()
    })

    // Report to error tracking service (if available)
    if (typeof window !== 'undefined' && (window as any).reportError) {
      (window as any).reportError(error, {
        context: 'DynamicRouteErrorBoundary',
        routePath: this.props.routePath,
        errorInfo
      })
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      validationError: null
    })
    
    // Reload the page to retry
    window.location.reload()
  }

  handleGoBack = () => {
    if (typeof window !== 'undefined' && window.history.length > 1) {
      window.history.back()
    } else {
      window.location.href = this.props.fallbackRoute || '/'
    }
  }

  getErrorTitle(): string {
    const { validationError } = this.state
    
    if (validationError) {
      switch (validationError.code) {
        case 'MISSING_PARAMETER':
          return 'Missing Parameter'
        case 'INVALID_PARAMETER_FORMAT':
          return 'Invalid Parameter Format'
        case 'PARAMETER_TOO_SHORT':
        case 'PARAMETER_TOO_LONG':
          return 'Invalid Parameter Length'
        case 'INVALID_CHARACTERS':
          return 'Invalid Characters'
        default:
          return 'Parameter Validation Error'
      }
    }
    
    return 'Page Error'
  }

  getErrorMessage(): string {
    const { validationError, error } = this.state
    const { customErrorMessage } = this.props
    
    if (customErrorMessage) {
      return customErrorMessage
    }
    
    if (validationError) {
      return validationError.message
    }
    
    if (error?.message.includes('404') || error?.message.includes('not found')) {
      return 'The requested page could not be found. It may have been moved or deleted.'
    }
    
    return 'An unexpected error occurred while loading this page. Please try again.'
  }

  getRecoveryActions(): Array<{ label: string; action: () => void; icon: ReactNode; primary?: boolean }> {
    const actions = [
      {
        label: 'Try Again',
        action: this.handleRetry,
        icon: <RefreshCw className="w-4 h-4" />,
        primary: true
      },
      {
        label: 'Go Back',
        action: this.handleGoBack,
        icon: <ArrowLeft className="w-4 h-4" />
      }
    ]

    // Add route-specific actions
    if (this.props.routePath.includes('/shop/')) {
      actions.push({
        label: 'Browse Shop',
        action: () => window.location.href = '/shop',
        icon: <Search className="w-4 h-4" />
      })
    } else if (this.props.routePath.includes('/profile/')) {
      actions.push({
        label: 'Go to Profile',
        action: () => window.location.href = '/profile',
        icon: <Home className="w-4 h-4" />
      })
    } else if (this.props.routePath.includes('/admin/')) {
      actions.push({
        label: 'Admin Dashboard',
        action: () => window.location.href = '/admin',
        icon: <Shield className="w-4 h-4" />
      })
    }

    return actions
  }

  render() {
    if (!this.state.hasError) {
      return this.props.children
    }

    const errorTitle = this.getErrorTitle()
    const errorMessage = this.getErrorMessage()
    const recoveryActions = this.getRecoveryActions()
    const { validationError } = this.state

    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md w-full bg-gray-800 rounded-lg border border-gray-700 p-6 text-center"
        >
          {/* Error Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="mx-auto w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4"
          >
            <AlertTriangle className="w-8 h-8 text-red-400" />
          </motion.div>

          {/* Error Title */}
          <h1 className="text-xl font-bold text-white mb-2">
            {errorTitle}
          </h1>

          {/* Error Message */}
          <p className="text-gray-300 mb-6">
            {errorMessage}
          </p>

          {/* Validation Error Details */}
          {validationError && (
            <div className="bg-gray-700/50 rounded-lg p-3 mb-6 text-left">
              <div className="text-sm text-gray-400 mb-1">Error Details:</div>
              <div className="text-sm text-red-400">
                <strong>Field:</strong> {validationError.field}
              </div>
              <div className="text-sm text-red-400">
                <strong>Code:</strong> {validationError.code}
              </div>
              <div className="text-sm text-red-400">
                <strong>Severity:</strong> {validationError.severity}
              </div>
            </div>
          )}

          {/* Recovery Actions */}
          <div className="space-y-3">
            {recoveryActions.map((action, index) => (
              <motion.button
                key={action.label}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                onClick={action.action}
                className={`w-full flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  action.primary
                    ? 'bg-purple-600 hover:bg-purple-700 text-white'
                    : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                }`}
              >
                {action.icon}
                {action.label}
              </motion.button>
            ))}
          </div>

          {/* Report Error Button */}
          {this.props.showReportButton && (
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
              onClick={() => {
                // Implement error reporting logic
                console.log('Report error clicked')
              }}
              className="mt-4 text-sm text-gray-400 hover:text-gray-300 flex items-center justify-center gap-1 mx-auto"
            >
              <Bug className="w-3 h-3" />
              Report this error
            </motion.button>
          )}

          {/* Route Information */}
          <div className="mt-6 pt-4 border-t border-gray-700">
            <div className="text-xs text-gray-500">
              Route: {this.props.routePath}
            </div>
          </div>
        </motion.div>
      </div>
    )
  }
}

// ===== HOOK FOR EASY USAGE =====

export function withDynamicRouteErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  routePath: string,
  options: {
    fallbackRoute?: string
    showReportButton?: boolean
    customErrorMessage?: string
  } = {}
) {
  return function WrappedComponent(props: P) {
    return (
      <DynamicRouteErrorBoundary
        routePath={routePath}
        fallbackRoute={options.fallbackRoute}
        showReportButton={options.showReportButton}
        customErrorMessage={options.customErrorMessage}
      >
        <Component {...props} />
      </DynamicRouteErrorBoundary>
    )
  }
}

export default DynamicRouteErrorBoundary
