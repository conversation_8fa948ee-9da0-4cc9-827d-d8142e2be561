'use client'

import React, { useEffect, useState } from 'react'
import Link from 'next/link'
import { AnimatePresence } from 'framer-motion'
import { PageTransition } from '@/components/transitions/PageTransition'
import { Toaster } from 'react-hot-toast'
import Header from './Header'
import Footer from './Footer'
import SimpleErrorBoundary from '@/components/error/SimpleErrorBoundary'
import PWAInstallPrompt from '@/components/pwa/PWAInstallPrompt'
import MobileCartDrawer from '@/components/cart/MobileCartDrawer'
import FloatingCartButton from '@/components/cart/FloatingCartButton'
import { GlobalAchievementNotifications } from '@/components/gamification/notifications/GlobalAchievementNotifications'
// Removed popup reminder imports: UserFlowGuide and QuickActions
import { NotificationProvider } from '@/lib/useNotifications'
import { ThemeProvider } from '@/contexts/ThemeContext'
import { GlobalModalSettingsProvider } from '@/hooks/useGlobalModalSettings'
import { reportWebVitals, addResourceHints, PerformanceTracker } from '@/lib/performance/performanceUtils'
import { initPerformanceMonitoring } from '@/lib/performance/webVitals'
import { useMobileCartDrawer } from '@/hooks/useMobileCartDrawer'
import { initializeAllMonitoring } from '@/lib/firebase/init-monitoring'
import { NavigationProvider } from '@/lib/navigation/NavigationProvider'
import { useUser } from '@/lib/useUser'
import { useWishlistStore } from '@/store/wishlistStore'
import { UserProfile } from '@/types/profile'

interface ClientLayoutProps {
  children: React.ReactNode
}

const ClientLayout: React.FC<ClientLayoutProps> = ({ children }) => {
  const [hasMounted, setHasMounted] = useState(false)
  const { isOpen, closeDrawer } = useMobileCartDrawer()
  const { user } = useUser()
  const { items } = useWishlistStore()

  useEffect(() => {
    setHasMounted(true)

    // All monitoring and service workers completely disabled to prevent crashes
    console.log('ClientLayout initialized without monitoring to prevent crashes')

    // TODO: Re-enable monitoring systems one by one after identifying crash source:
    // - initializeAllMonitoring (performance tracking)
    // - Service Worker registration (PWA functionality)
    // - Web Vitals monitoring
    // - Resource hints

    // Initialize Web Vitals monitoring
    if (typeof window !== 'undefined') {
      import('web-vitals').then((webVitals) => {
        // Use the correct API for web-vitals v3+
        if (webVitals.onCLS) webVitals.onCLS(reportWebVitals)
        if (webVitals.onINP) webVitals.onINP(reportWebVitals) // INP replaces FID
        if (webVitals.onFCP) webVitals.onFCP(reportWebVitals)
        if (webVitals.onLCP) webVitals.onLCP(reportWebVitals)
        if (webVitals.onTTFB) webVitals.onTTFB(reportWebVitals)
      }).catch(error => {
        console.warn('Failed to load web-vitals:', error)
      })
    }

    PerformanceTracker.measure('app-initialization', 'App Lifecycle')
  }, [])

  // Prevent hydration mismatch by not rendering until client-side
  if (!hasMounted) {
    return (
      <ThemeProvider defaultTheme="dark">
        <div className="flex flex-col min-h-screen">
          <SimpleErrorBoundary
            fallback={
              <header className="fixed top-0 left-0 right-0 z-50 bg-gray-900 border-b border-gray-700">
                <div className="container mx-auto px-4 py-4">
                  <div className="flex items-center justify-between">
                    <a href="/" className="flex items-center space-x-3">
                      <img src="/logo.png" alt="Syndicaps Logo" className="h-8 w-auto" />
                      <span className="text-xl font-bold text-white">Syndicaps</span>
                    </a>
                    <nav className="hidden md:flex space-x-6">
                      <a href="/" className="text-gray-300 hover:text-white">Home</a>
                      <a href="/shop" className="text-gray-300 hover:text-white">Shop</a>
                      <a href="/community" className="text-gray-300 hover:text-white">Community</a>
                      <a href="/auth" className="bg-accent-600 hover:bg-accent-700 px-4 py-2 rounded text-white">Login</a>
                    </nav>
                  </div>
                </div>
              </header>
            }
          >
            <Header />
          </SimpleErrorBoundary>
          <main className="flex-grow pt-20">
            {children}
          </main>
          <Footer />
        </div>
      </ThemeProvider>
    )
  }

  return (
    <ThemeProvider defaultTheme="dark">
      <GlobalModalSettingsProvider>
        <NotificationProvider>
          <NavigationProvider
            profile={user as UserProfile | null}
            wishlistItemCount={items.length}
          >
        <div className="flex flex-col min-h-screen">
        <SimpleErrorBoundary
          fallback={
            <header className="fixed top-0 left-0 right-0 z-50 bg-gray-900 border-b border-gray-700">
              <div className="container mx-auto px-4 py-4">
                <div className="flex items-center justify-between">
                  <a href="/" className="flex items-center space-x-3">
                    <img src="/logo.png" alt="Syndicaps Logo" className="h-8 w-auto" />
                    <span className="text-xl font-bold text-white">Syndicaps</span>
                  </a>
                  <nav className="hidden md:flex space-x-6">
                    <a href="/" className="text-gray-300 hover:text-white">Home</a>
                    <a href="/shop" className="text-gray-300 hover:text-white">Shop</a>
                    <a href="/community" className="text-gray-300 hover:text-white">Community</a>
                    <a href="/auth" className="bg-accent-600 hover:bg-accent-700 px-4 py-2 rounded text-white">Login</a>
                  </nav>
                </div>
              </div>
            </header>
          }
        >
          <Header />
        </SimpleErrorBoundary>
        <main className="flex-grow pt-20">
          <AnimatePresence mode="wait">
            <PageTransition>
              {children}
            </PageTransition>
          </AnimatePresence>
        </main>
        <Footer />
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#1f2937',
              color: '#f9fafb',
              border: '1px solid #374151'
            },
            success: {
              iconTheme: {
                primary: '#8b5cf6',
                secondary: '#f9fafb'
              }
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#f9fafb'
              }
            }
          }}
        />
        <PWAInstallPrompt />

        {/* Mobile Cart Components */}
        <MobileCartDrawer isOpen={isOpen} onClose={closeDrawer} />
        <FloatingCartButton />

        {/* Global Achievement Notifications */}
        <GlobalAchievementNotifications />

        {/* Removed popup reminders: UserFlowGuide and QuickActions */}
        </div>
          </NavigationProvider>
        </NotificationProvider>
      </GlobalModalSettingsProvider>
    </ThemeProvider>
  )
}

export default ClientLayout
