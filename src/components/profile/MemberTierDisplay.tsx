'use client'

/**
 * Member Tier Display Component
 * 
 * Shows comprehensive tier information including benefits,
 * progress, and tier comparison.
 */

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Star,
  Gift,
  Shield,
  Crown,
  Truck,
  Clock,
  Percent,
  Users,
  Award,
  Gem,
  Zap,
  ChevronRight,
  Info
} from 'lucide-react'
import {
  getTierInfoByPoints,
  getTierProgress,
  TIER_DEFINITIONS,
  getTierBenefits,
  getTierDiscount,
  hasFreeShipping,
  getEarlyAccessHours,
  type MemberTier
} from '@/lib/memberTiers'

interface MemberTierDisplayProps {
  points: number
  showComparison?: boolean
  compact?: boolean
}

export default function MemberTierDisplay({ 
  points, 
  showComparison = false, 
  compact = false 
}: MemberTierDisplayProps) {
  const [showDetails, setShowDetails] = useState(false)
  const tierInfo = getTierInfoByPoints(points)
  const tierProgress = getTierProgress(points)

  const getBenefitIcon = (benefit: string) => {
    if (benefit.includes('discount')) return <Percent size={16} className="text-green-400" />
    if (benefit.includes('shipping')) return <Truck size={16} className="text-blue-400" />
    if (benefit.includes('support')) return <Shield size={16} className="text-purple-400" />
    if (benefit.includes('access')) return <Clock size={16} className="text-yellow-400" />
    if (benefit.includes('raffle')) return <Gift size={16} className="text-pink-400" />
    if (benefit.includes('events')) return <Users size={16} className="text-indigo-400" />
    return <Star size={16} className="text-gray-400" />
  }

  if (compact) {
    return (
      <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium border backdrop-blur-sm ${tierInfo.bgColor} ${tierInfo.color} ${tierInfo.borderColor}`}>
        <span>{tierInfo.icon}</span>
        <span>{tierInfo.name}</span>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Current Tier Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`p-6 rounded-lg border ${tierInfo.bgColor} ${tierInfo.borderColor} backdrop-blur-sm`}
      >
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">{tierInfo.icon}</span>
            <div>
              <h3 className={`text-lg font-semibold ${tierInfo.color}`}>
                {tierInfo.name}
              </h3>
              <p className="text-gray-400 text-sm">
                {points.toLocaleString()} points
              </p>
            </div>
          </div>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <Info size={20} />
          </button>
        </div>

        {/* Progress to Next Tier */}
        {tierProgress.nextTier && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-300">
                Progress to {tierProgress.nextTier.toUpperCase()}
              </span>
              <span className="text-gray-400">
                {tierProgress.pointsNeeded} points needed
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-500 bg-gradient-to-r ${tierInfo.gradientColors}`}
                style={{ width: `${tierProgress.progress}%` }}
              />
            </div>
          </div>
        )}

        {/* Current Tier Benefits */}
        <AnimatePresence>
          {showDetails && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 pt-4 border-t border-gray-700"
            >
              <h4 className="text-white font-medium mb-3">Your Benefits</h4>
              <div className="space-y-2">
                {tierInfo.benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    {getBenefitIcon(benefit)}
                    <span className="text-gray-300 text-sm">{benefit}</span>
                  </div>
                ))}
              </div>

              {/* Quick Benefits Summary */}
              <div className="mt-4 grid grid-cols-2 gap-4">
                {getTierDiscount(tierInfo.tier) > 0 && (
                  <div className="text-center p-2 bg-green-500/10 rounded-lg border border-green-500/20">
                    <Percent size={20} className="mx-auto mb-1 text-green-400" />
                    <div className="text-green-300 font-medium">
                      {getTierDiscount(tierInfo.tier)}% Discount
                    </div>
                  </div>
                )}
                
                {hasFreeShipping(tierInfo.tier) && (
                  <div className="text-center p-2 bg-blue-500/10 rounded-lg border border-blue-500/20">
                    <Truck size={20} className="mx-auto mb-1 text-blue-400" />
                    <div className="text-blue-300 font-medium">Free Shipping</div>
                  </div>
                )}
                
                {getEarlyAccessHours(tierInfo.tier) > 0 && (
                  <div className="text-center p-2 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                    <Clock size={20} className="mx-auto mb-1 text-yellow-400" />
                    <div className="text-yellow-300 font-medium">
                      {getEarlyAccessHours(tierInfo.tier)}h Early Access
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Tier Comparison */}
      {showComparison && (
        <div className="space-y-3">
          <h4 className="text-white font-medium">All Membership Tiers</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.values(TIER_DEFINITIONS).map((tier) => {
              const isCurrentTier = tier.tier === tierInfo.tier
              const isUnlocked = points >= tier.minPoints
              
              return (
                <motion.div
                  key={tier.tier}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`p-4 rounded-lg border transition-all duration-200 ${
                    isCurrentTier
                      ? `${tier.bgColor} ${tier.borderColor}`
                      : isUnlocked
                      ? 'bg-gray-800/50 border-gray-600'
                      : 'bg-gray-800/30 border-gray-700 opacity-60'
                  }`}
                >
                  <div className="text-center">
                    <div className="mb-2 flex justify-center">
                      {React.createElement(tier.iconComponent, {
                        size: 32,
                        className: isCurrentTier ? tier.color : 'text-gray-300'
                      })}
                    </div>
                    <h5 className={`font-medium mb-1 ${
                      isCurrentTier ? tier.color : 'text-gray-300'
                    }`}>
                      {tier.name}
                    </h5>
                    <p className="text-xs text-gray-400 mb-3">
                      {tier.minPoints.toLocaleString()}+ points
                    </p>
                    
                    {isCurrentTier && (
                      <div className="bg-accent-500/20 text-accent-300 px-2 py-1 rounded text-xs font-medium mb-2">
                        Current Tier
                      </div>
                    )}
                    
                    {!isUnlocked && (
                      <div className="bg-gray-600/20 text-gray-400 px-2 py-1 rounded text-xs mb-2">
                        {(tier.minPoints - points).toLocaleString()} points needed
                      </div>
                    )}

                    <div className="space-y-1">
                      {getTierDiscount(tier.tier) > 0 && (
                        <div className="text-xs text-green-400">
                          {getTierDiscount(tier.tier)}% discount
                        </div>
                      )}
                      {hasFreeShipping(tier.tier) && (
                        <div className="text-xs text-blue-400">Free shipping</div>
                      )}
                      {getEarlyAccessHours(tier.tier) > 0 && (
                        <div className="text-xs text-yellow-400">
                          {getEarlyAccessHours(tier.tier)}h early access
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
