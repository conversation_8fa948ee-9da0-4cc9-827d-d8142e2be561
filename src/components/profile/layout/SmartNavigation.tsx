/**
 * Smart Navigation Component
 * 
 * Enhanced navigation system with intelligent features:
 * - Recently visited pages tracking
 * - Contextual badges and notifications
 * - Quick access shortcuts
 * - Adaptive menu ordering
 * - Keyboard shortcuts support
 * 
 * Features:
 * - Auto-reordering based on usage patterns
 * - Context-aware badges (new achievements, pending actions)
 * - Recently visited quick access
 * - Keyboard navigation (Arrow keys, Enter, Escape)
 * - Search within navigation
 * - Customizable favorites
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

'use client'

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  User,
  Settings,
  Package,
  Trophy,
  Mail,
  Lock,
  CreditCard,
  Shield,
  Activity,
  Star,
  BarChart3,
  Users,
  Heart,
  Search,
  Clock,
  Bookmark,
  ChevronRight,
  Command,
  Zap,
  TrendingUp,
  Bell
} from 'lucide-react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { UserProfile } from '@/types/profile'
import { NAVIGATION_CONSTANTS } from '@/lib/navigation/constants'

interface NavigationItem {
  id: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  label: string
  href: string
  description: string
  badge?: number | string
  badgeType?: 'notification' | 'count' | 'status' | 'new'
  keywords?: string[]
  category: string
  priority?: number
}

interface SmartNavigationProps {
  profile: UserProfile | null
  wishlistItemCount: number
  loading: boolean
  className?: string
}

interface VisitHistory {
  href: string
  timestamp: number
  count: number
}

interface NavigationState {
  searchQuery: string
  showSearch: boolean
  recentlyVisited: VisitHistory[]
  favorites: string[]
  keyboardNavIndex: number
}

/**
 * Custom hook for navigation state management
 */
const useNavigationState = () => {
  const [state, setState] = useState<NavigationState>({
    searchQuery: '',
    showSearch: false,
    recentlyVisited: [],
    favorites: [],
    keyboardNavIndex: -1
  })
  
  const pathname = usePathname()

  // Load state from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedHistory = localStorage.getItem('navigation-history')
      const savedFavorites = localStorage.getItem('navigation-favorites')
      
      setState(prev => ({
        ...prev,
        recentlyVisited: savedHistory ? JSON.parse(savedHistory) : [],
        favorites: savedFavorites ? JSON.parse(savedFavorites) : []
      }))
    }
  }, [])

  // Track page visits
  useEffect(() => {
    if (pathname && typeof window !== 'undefined') {
      setState(prev => {
        const existing = prev.recentlyVisited.find(item => item.href === pathname)
        const now = Date.now()
        
        let newHistory
        if (existing) {
          newHistory = prev.recentlyVisited.map(item =>
            item.href === pathname
              ? { ...item, timestamp: now, count: item.count + 1 }
              : item
          )
        } else {
          newHistory = [
            { href: pathname, timestamp: now, count: 1 },
            ...prev.recentlyVisited
          ].slice(0, 10) // Keep only last 10
        }
        
        // Sort by recency and frequency
        newHistory.sort((a, b) => {
          const scoreA = a.timestamp * 0.7 + a.count * 0.3
          const scoreB = b.timestamp * 0.7 + b.count * 0.3
          return scoreB - scoreA
        })
        
        localStorage.setItem('navigation-history', JSON.stringify(newHistory))
        
        return {
          ...prev,
          recentlyVisited: newHistory
        }
      })
    }
  }, [pathname])

  return [state, setState] as const
}

/**
 * Get contextual badges for navigation items
 */
const getContextualBadges = (profile: UserProfile | null, wishlistItemCount: number) => {
  const badges: Record<string, { badge?: number | string; badgeType?: string }> = {}

  if (profile) {
    // Points badge
    if (profile.points) {
      badges['points'] = {
        badge: profile.points.toLocaleString(),
        badgeType: 'count'
      }
    }

    // Achievements badge
    if (profile.achievements?.length) {
      badges['achievements'] = {
        badge: profile.achievements.length,
        badgeType: 'count'
      }
    }

    // Wishlist badge
    if (wishlistItemCount > 0) {
      badges['wishlist'] = {
        badge: wishlistItemCount,
        badgeType: 'notification'
      }
    }

    // Check for new achievements (mock logic)
    const hasNewAchievements = profile.achievements?.some(a => 
      Date.now() - (a.unlockedAt as any) < 24 * 60 * 60 * 1000
    )
    if (hasNewAchievements) {
      badges['achievements'] = {
        ...badges['achievements'],
        badgeType: 'new'
      }
    }

    // Profile completion status
    const completion = calculateProfileCompletion(profile)
    if (completion < 80) {
      badges['account'] = {
        badge: 'Incomplete',
        badgeType: 'status'
      }
    }
  }

  return badges
}

/**
 * Calculate profile completion
 */
const calculateProfileCompletion = (profile: UserProfile | null): number => {
  if (!profile) return 0

  let completed = 0
  const total = 8
  const fields = ['firstName', 'lastName', 'displayName', 'avatar', 'bio', 'phone', 'address', 'dateOfBirth']
  
  fields.forEach(field => {
    if (profile[field as keyof UserProfile]) completed++
  })

  return Math.round((completed / total) * 100)
}

/**
 * Category Information and Metadata
 */
const getCategoryInfo = (category: string) => {
  const categoryMap: Record<string, { icon: React.ComponentType<any>, description: string }> = {
    'Account & Personal': {
      icon: User,
      description: 'Personal information and account management'
    },
    'Orders & Activity': {
      icon: Package,
      description: 'Shopping history and user activity'
    },
    'Rewards & Social': {
      icon: Trophy,
      description: 'Rewards, achievements, and social features'
    },
    'Analytics & Settings': {
      icon: Settings,
      description: 'Data insights and system preferences'
    }
  }

  return categoryMap[category] || { icon: Settings, description: '' }
}

/**
 * Optimized 4-Category Navigation Configuration
 *
 * Restructured from 9 fragmented categories to 4 logical groups:
 * 1. Account & Personal - Personal info and account management
 * 2. Orders & Activity - Shopping history and user activity
 * 3. Rewards & Social - Rewards, achievements, and social features
 * 4. Analytics & Settings - Data insights and system preferences
 */
const getNavigationItems = (profile: UserProfile | null, wishlistItemCount: number): NavigationItem[] => {
  const badges = getContextualBadges(profile, wishlistItemCount)

  return [
    // ===== CATEGORY 1: ACCOUNT & PERSONAL =====
    // Main dashboard and personal information management
    {
      id: 'account',
      icon: User,
      label: 'Account Details',
      href: '/profile/account',
      description: 'Personal information and main dashboard',
      keywords: ['profile', 'personal', 'details', 'info', 'dashboard'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.account.label,
      priority: 10,
      ...badges['account']
    },
    {
      id: 'contact',
      icon: User,
      label: 'Contact Information',
      href: '/profile/contact',
      description: 'Personal details and address management',
      keywords: ['contact', 'address', 'phone', 'details', 'personal'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.account.label,
      priority: 8
    },
    {
      id: 'privacy',
      icon: Shield,
      label: 'Privacy Settings',
      href: '/profile/privacy',
      description: 'Manage your privacy and data sharing preferences',
      keywords: ['privacy', 'data', 'sharing', 'visibility', 'permissions'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.account.label,
      priority: 6
    },
    {
      id: 'security',
      icon: Lock,
      label: 'Password & Security',
      href: '/profile/security',
      description: 'Update password and security settings',
      keywords: ['password', 'security', 'authentication', 'login'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.account.label,
      priority: 6
    },
    {
      id: 'edit',
      icon: User,
      label: 'Edit Profile',
      href: '/profile/edit',
      description: 'Update your profile information',
      keywords: ['edit', 'update', 'profile', 'modify'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.account.label,
      priority: 7
    },

    // ===== CATEGORY 2: ORDERS & ACTIVITY =====
    // Shopping history and user activity tracking
    {
      id: 'orders',
      icon: Package,
      label: 'Orders',
      href: '/profile/orders',
      description: 'Track your orders and purchase history',
      keywords: ['orders', 'purchases', 'history', 'tracking'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.orders.label,
      priority: 9
    },
    {
      id: 'wishlist',
      icon: Heart,
      label: 'Wishlist',
      href: '/profile/wishlist',
      description: 'View your saved products and favorites',
      keywords: ['wishlist', 'saved', 'favorites', 'products'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.orders.label,
      priority: 8,
      ...badges['wishlist']
    },
    {
      id: 'raffles',
      icon: Trophy,
      label: 'Raffle Entries',
      href: '/profile/raffle-entries',
      description: 'View your raffle entries and wins',
      keywords: ['raffle', 'entries', 'wins', 'lottery', 'gaming'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.orders.label,
      priority: 7
    },
    {
      id: 'activity-log',
      icon: Activity,
      label: 'Activity Timeline',
      href: '/profile/activity',
      description: 'View your account activity and timeline',
      keywords: ['activity', 'log', 'history', 'actions', 'timeline'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.orders.label,
      priority: 6
    },

    // ===== CATEGORY 3: REWARDS & SOCIAL =====
    // Rewards, achievements, and social features
    {
      id: 'points',
      icon: Star,
      label: 'Points & Rewards',
      href: '/profile/points',
      description: 'Track your points, rewards, and tier status',
      keywords: ['points', 'rewards', 'history', 'earnings', 'tier'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.rewards.label,
      priority: 8,
      ...badges['points']
    },
    {
      id: 'achievements',
      icon: Trophy,
      label: 'Achievements',
      href: '/profile/achievements',
      description: 'View your badges, milestones, and accomplishments',
      keywords: ['achievements', 'badges', 'milestones', 'rewards'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.rewards.label,
      priority: 8,
      ...badges['achievements']
    },
    {
      id: 'social',
      icon: Users,
      label: 'Social Profile',
      href: '/profile/social',
      description: 'Your social presence and community connections',
      keywords: ['social', 'community', 'friends', 'following', 'profile'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.rewards.label,
      priority: 7
    },
    {
      id: 'gamification',
      icon: Trophy,
      label: 'Gamification',
      href: '/profile/gamification',
      description: 'Gaming features and interactive elements',
      keywords: ['gamification', 'gaming', 'interactive', 'features'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.rewards.label,
      priority: 6
    },

    // ===== CATEGORY 4: ANALYTICS & SETTINGS =====
    // Data insights and system preferences
    {
      id: 'analytics',
      icon: BarChart3,
      label: 'Analytics',
      href: '/profile/analytics',
      description: 'Personal insights, statistics, and data analysis',
      keywords: ['analytics', 'stats', 'insights', 'data', 'reports'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.settings.label,
      priority: 6
    },
    {
      id: 'preferences',
      icon: Settings,
      label: 'Preferences',
      href: '/profile/preferences',
      description: 'Language, currency, and general preferences',
      keywords: ['preferences', 'settings', 'language', 'currency'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.settings.label,
      priority: 5
    },
    {
      id: 'notifications',
      icon: Bell,
      label: 'Notifications',
      href: '/profile/notifications',
      description: 'Manage notification preferences and settings',
      keywords: ['notifications', 'alerts', 'preferences', 'settings'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.settings.label,
      priority: 5
    },
    {
      id: 'email',
      icon: Mail,
      label: 'Email Settings',
      href: '/profile/email',
      description: 'Email preferences and communication settings',
      keywords: ['email', 'notifications', 'preferences', 'communication'],
      category: NAVIGATION_CONSTANTS.CATEGORIES.settings.label,
      priority: 4
    }
  ]
}

/**
 * Badge component
 */
const NavigationBadge: React.FC<{ 
  badge: number | string
  type?: string 
}> = ({ badge, type = 'count' }) => {
  const badgeClasses = {
    notification: 'bg-red-500 text-white',
    count: 'bg-accent-500 text-white',
    status: 'bg-yellow-500 text-gray-900',
    new: 'bg-green-500 text-white animate-pulse'
  }

  return (
    <span className={`text-xs px-2 py-1 rounded-full font-medium ${badgeClasses[type as keyof typeof badgeClasses] || badgeClasses.count}`}>
      {badge}
    </span>
  )
}

/**
 * Search component
 */
const NavigationSearch: React.FC<{
  searchQuery: string
  onSearchChange: (query: string) => void
  onClose: () => void
}> = ({ searchQuery, onSearchChange, onClose }) => {
  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      className="p-4 border-b border-gray-700"
    >
      <div className="relative">
        <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          placeholder="Search navigation..."
          className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-500"
          autoFocus
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              onClose()
            }
          }}
        />
      </div>
    </motion.div>
  )
}

/**
 * Recently visited section
 */
const RecentlyVisited: React.FC<{
  items: NavigationItem[]
  recentlyVisited: VisitHistory[]
  onItemClick: (href: string) => void
}> = ({ items, recentlyVisited, onItemClick }) => {
  const recentItems = useMemo(() => {
    return recentlyVisited
      .map(visit => items.find(item => item.href === visit.href))
      .filter(Boolean)
      .slice(0, 3)
  }, [items, recentlyVisited])

  if (recentItems.length === 0) return null

  return (
    <div className="px-4 py-3 border-b border-gray-700">
      <div className="flex items-center space-x-2 mb-3">
        <Clock size={14} className="text-gray-400" />
        <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider">
          Recently Visited
        </h3>
      </div>
      <div className="space-y-1">
        {recentItems.map((item, index) => {
          const IconComponent = item!.icon
          return (
            <button
              key={item!.id}
              onClick={() => onItemClick(item!.href)}
              className="w-full flex items-center space-x-3 px-2 py-2 text-sm text-gray-300 hover:bg-gray-700 rounded-lg transition-colors"
            >
              <IconComponent size={16} className="text-gray-400" />
              <span className="truncate">{item!.label}</span>
              <ChevronRight size={12} className="text-gray-500 ml-auto" />
            </button>
          )
        })}
      </div>
    </div>
  )
}

/**
 * Main Smart Navigation Component
 */
const SmartNavigation: React.FC<SmartNavigationProps> = ({
  profile,
  wishlistItemCount,
  loading,
  className = ""
}) => {
  const [state, setState] = useNavigationState()
  const pathname = usePathname()
  const router = useRouter()

  const navigationItems = useMemo(() => 
    getNavigationItems(profile, wishlistItemCount), 
    [profile, wishlistItemCount]
  )

  // Filter items based on search
  const filteredItems = useMemo(() => {
    if (!state.searchQuery) {
      // Sort by priority and recent usage
      return navigationItems.sort((a, b) => {
        const aRecent = state.recentlyVisited.find(v => v.href === a.href)
        const bRecent = state.recentlyVisited.find(v => v.href === b.href)
        
        const aScore = (a.priority || 0) + (aRecent ? aRecent.count * 2 : 0)
        const bScore = (b.priority || 0) + (bRecent ? bRecent.count * 2 : 0)
        
        return bScore - aScore
      })
    }

    const query = state.searchQuery.toLowerCase()
    return navigationItems.filter(item =>
      item.label.toLowerCase().includes(query) ||
      item.description.toLowerCase().includes(query) ||
      item.keywords?.some(keyword => keyword.includes(query))
    )
  }, [navigationItems, state.searchQuery, state.recentlyVisited])

  // Group items by optimized 4-category structure
  const groupedItems = useMemo(() => {
    // Define category order for consistent display
    const categoryOrder = [
      'Account & Personal',
      'Orders & Activity',
      'Rewards & Social',
      'Analytics & Settings'
    ]

    const groups: Record<string, NavigationItem[]> = {}

    // Initialize groups in correct order
    categoryOrder.forEach(category => {
      groups[category] = []
    })

    // Group items by category
    filteredItems.forEach(item => {
      if (!groups[item.category]) {
        groups[item.category] = []
      }
      groups[item.category].push(item)
    })

    // Sort items within each category by priority (highest first)
    Object.keys(groups).forEach(category => {
      groups[category].sort((a, b) => b.priority - a.priority)
    })

    // Return only non-empty categories in correct order
    const orderedGroups: Record<string, NavigationItem[]> = {}
    categoryOrder.forEach(category => {
      if (groups[category] && groups[category].length > 0) {
        orderedGroups[category] = groups[category]
      }
    })

    return orderedGroups
  }, [filteredItems])

  const isActive = (href: string) => pathname === href

  const handleItemClick = useCallback((href: string) => {
    router.push(href)
  }, [router])

  const handleSearchToggle = useCallback(() => {
    setState(prev => ({
      ...prev,
      showSearch: !prev.showSearch,
      searchQuery: prev.showSearch ? '' : prev.searchQuery
    }))
  }, [setState])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K to toggle search
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        handleSearchToggle()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleSearchToggle])

  if (loading) {
    return (
      <div className={`bg-gray-800 rounded-lg shadow-xl border border-gray-700 overflow-hidden ${className}`}>
        <div className="p-4 space-y-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="space-y-2">
              <div className="h-4 bg-gray-700 rounded w-1/2 animate-pulse"></div>
              <div className="h-8 bg-gray-700 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className={`bg-gray-800 rounded-lg shadow-xl border border-gray-700 overflow-hidden ${className}`}
    >
      {/* Header with search toggle */}
      <div className="px-4 py-3 bg-gray-700 flex items-center justify-between">
        <h2 className="text-sm font-semibold text-gray-300">Navigation</h2>
        <button
          onClick={handleSearchToggle}
          className="p-1 text-gray-400 hover:text-white transition-colors rounded"
          title="Search navigation (Cmd+K)"
        >
          <Search size={16} />
        </button>
      </div>

      {/* Search */}
      <AnimatePresence>
        {state.showSearch && (
          <NavigationSearch
            searchQuery={state.searchQuery}
            onSearchChange={(query) => setState(prev => ({ ...prev, searchQuery: query }))}
            onClose={() => setState(prev => ({ ...prev, showSearch: false, searchQuery: '' }))}
          />
        )}
      </AnimatePresence>

      {/* Recently Visited */}
      {!state.searchQuery && (
        <RecentlyVisited
          items={navigationItems}
          recentlyVisited={state.recentlyVisited}
          onItemClick={handleItemClick}
        />
      )}

      {/* Navigation Items */}
      <nav className="space-y-1">
        {Object.entries(groupedItems).map(([category, items]) => {
          // Get category metadata
          const categoryInfo = getCategoryInfo(category)

          return (
            <div key={category}>
              {/* Enhanced Category Header */}
              <div className="px-4 py-3 bg-gray-750 border-b border-gray-600">
                <div className="flex items-center space-x-2">
                  <categoryInfo.icon size={16} className="text-accent-400" />
                  <h3 className="text-xs font-semibold text-gray-300 uppercase tracking-wider">
                    {category}
                  </h3>
                </div>
                {categoryInfo.description && (
                  <p className="text-xs text-gray-500 mt-1">
                    {categoryInfo.description}
                  </p>
                )}
              </div>

            {/* Category Items */}
            <div className="space-y-1">
              {items.map((item) => (
                <Link
                  key={item.id}
                  href={item.href}
                  className={`
                    flex items-center space-x-3 px-3 sm:px-4 py-3 sm:py-4 text-sm transition-colors group
                    ${isActive(item.href)
                      ? 'bg-accent-900/20 text-accent-400 border-r-2 border-accent-500'
                      : 'text-gray-300 hover:bg-gray-700'
                    }
                  `}
                >
                  <item.icon
                    size={18}
                    className={`${isActive(item.href) ? 'text-accent-500' : 'text-gray-400 group-hover:text-gray-300'} transition-colors`}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="font-medium truncate">{item.label}</span>
                      {item.badge && (
                        <NavigationBadge badge={item.badge} type={item.badgeType} />
                      )}
                    </div>
                    <p className="text-xs text-gray-400 mt-1 truncate">
                      {item.description}
                    </p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
          )
        })}
      </nav>

      {/* Admin Section */}
      {(profile?.role === 'admin' || profile?.role === 'superadmin') && (
        <div className="border-t border-gray-700">
          <div className="px-4 py-2 bg-gray-750">
            <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Administration
            </h3>
          </div>
          <Link
            href="/admin/dashboard"
            className="flex items-center space-x-3 px-4 py-3 text-blue-400 hover:bg-blue-900/20 transition-colors"
          >
            <Shield size={18} />
            <div className="flex-1">
              <span className="font-medium">Admin Dashboard</span>
              <p className="text-xs text-blue-400 mt-1">
                Manage the platform
              </p>
            </div>
          </Link>
        </div>
      )}

      {/* Keyboard shortcuts hint */}
      <div className="px-4 py-2 bg-gray-750 border-t border-gray-700">
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <Command size={12} />
          <span>Press Cmd+K to search</span>
        </div>
      </div>
    </motion.div>
  )
}

export default SmartNavigation