'use client'

import React, { useState, useEffect } from 'react'
import { User as UserIcon } from 'lucide-react'
import { useUser } from '@/lib/useUser'
// import { GamificationErrorBoundary } from '../gamification/error/GamificationErrorBoundary'
import { UserProfile } from '@/types/profile'
import { User } from 'firebase/auth'
import { useWishlistStore } from '@/store/wishlistStore'
import { ConsolidatedNavigation } from '@/components/navigation/ConsolidatedNavigation'
// Legacy imports - kept for backward compatibility during transition
// import SmartNavigation from './layout/SmartNavigation'
// import ProfileNavigation from './layout/ProfileNavigation'
// import UnifiedNavigation from './layout/UnifiedNavigation'
// import KeyboardShortcuts from './layout/KeyboardShortcuts'
// import { SkipLinks, LiveRegion } from '@/components/accessibility/AccessibilityEnhancements'
import dynamic from 'next/dynamic'

// Use dynamic import to prevent webpack issues
const EnhancedWelcomeModal = dynamic(() => import('./layout/EnhancedWelcomeModal'), {
  ssr: false,
  loading: () => null
})
import ProfileSkeleton from './layout/ProfileSkeleton'
// import ProfileBottomNav, { useShowBottomNav } from './layout/ProfileBottomNav'
// import MobileOptimizations from './layout/MobileOptimizations'
import { useFeatureFlag } from '@/lib/featureFlags/profileOptimizationFlags'
import { useProfileOptimizationAnalytics } from '@/lib/analytics/profileOptimizationAnalytics'

interface ProfileLayoutProps {
  children: React.ReactNode
  variant?: 'full' | 'basic' | 'minimal'
  navigation?: 'smart' | 'simple' | 'mobile' | 'unified' | 'consolidated'
  showHeader?: boolean
  showWelcome?: boolean
  showSkeleton?: boolean
  features?: string[]
  className?: string
}

/**
 * Create a proper display name with better fallback logic
 */
const getDisplayName = (user: User | null, profile: UserProfile | null) => {
  if (!profile) return 'User'
  
  // If displayName exists and is not just "test" or empty, use it
  if (profile.displayName && profile.displayName.trim() && profile.displayName.toLowerCase() !== 'test') {
    return profile.displayName
  }

  // Try to construct from first and last name
  if (profile.firstName || profile.lastName) {
    const firstName = profile.firstName || ''
    const lastName = profile.lastName || ''
    const fullName = `${firstName} ${lastName}`.trim()
    if (fullName) {
      return fullName
    }
  }

  // Fallback to email username if user exists
  if (user?.email) {
    const emailUsername = user.email.split('@')[0]
    // Make it more readable (capitalize first letter, replace dots/underscores)
    return emailUsername
      .replace(/[._]/g, ' ')
      .split(' ')
      .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  return 'User'
}

function ProfileLayout({
  children,
  variant = 'full',
  navigation = 'unified',
  showHeader = true,
  showWelcome = true,
  showSkeleton = true,
  className = ''
}: ProfileLayoutProps) {
  const { user, profile, loading } = useUser()
  const [isClient, setIsClient] = useState(false)
  const [showWelcomeModal, setShowWelcomeModal] = useState(false)
  const [isFirstLogin, setIsFirstLogin] = useState(false)
  const { getItemCount } = useWishlistStore()
  // const showBottomNav = useShowBottomNav()
  const showBottomNav = false
  const analytics = useProfileOptimizationAnalytics()

  // Feature flags for A/B testing
  const useConsolidatedLayouts = useFeatureFlag('layout.consolidatedLayouts', user?.uid)
  const useFourCategoryNav = useFeatureFlag('navigation.fourCategoryStructure', user?.uid)
  const useBottomNavigation = useFeatureFlag('mobile.bottomNavigation', user?.uid)
  const useEnhancedSwipes = useFeatureFlag('mobile.enhancedSwipeGestures', user?.uid)

  // Prevent hydration mismatch by ensuring we're on the client side
  useEffect(() => {
    setIsClient(true)

    // Track A/B test assignments
    if (user?.uid) {
      analytics.trackFeatureAdoption('consolidated_layouts', useConsolidatedLayouts)
      analytics.trackFeatureAdoption('four_category_navigation', useFourCategoryNav)
      analytics.trackFeatureAdoption('bottom_navigation', useBottomNavigation)
      analytics.trackFeatureAdoption('enhanced_swipes', useEnhancedSwipes)
    }
  }, [user?.uid, useConsolidatedLayouts, useFourCategoryNav, useBottomNavigation, useEnhancedSwipes, analytics])

  useEffect(() => {
    // Enhanced welcome experience with first-time user detection
    if (user && profile && isClient) {
      const hasShown = localStorage.getItem('profileWelcomeShown')
      const currentTime = Date.now().toString()

      // Detect first-time user (profile created recently)
      const createdAtTime = profile.createdAt instanceof Date
        ? profile.createdAt.getTime()
        : profile.createdAt?.toMillis?.() || 0
      const profileAge = createdAtTime ? Date.now() - createdAtTime : 0
      const isNewUser = profileAge < 24 * 60 * 60 * 1000 // Less than 24 hours old
      
      if (!hasShown || isNewUser) {
        setIsFirstLogin(isNewUser)
        setShowWelcomeModal(true)
        localStorage.setItem('profileWelcomeShown', 'true')
        localStorage.setItem('lastLoginTime', currentTime)
      }
    }
  }, [user, profile, isClient])

  // Handle loading state to prevent hydration mismatch
  const isLoading = loading || !isClient
  const wishlistItemCount = getItemCount()
  const displayName = getDisplayName(user, profile)

  // Show loading skeleton while initializing
  if (isLoading && showSkeleton) {
    return (
      <div>
        <ProfileSkeleton />
      </div>
    )
  }

  // Basic variant - simplified layout without complex features
  if (variant === 'basic') {
    return (
      <div className={`min-h-screen bg-gray-950 ${className}`}>
        <div className="max-w-7xl mx-auto mobile-container py-4 sm:py-6 lg:py-8">
          {showHeader && (
            <div className="mb-4 sm:mb-6 lg:mb-8">
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <UserIcon size={24} className="text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-1">
                    {user?.displayName || profile?.firstName || 'User'}
                  </h3>
                  <p className="text-gray-400 text-sm">{user?.email}</p>
                </div>
              </div>
            </div>
          )}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            {children}
          </div>
        </div>
      </div>
    )
  }

  // Minimal variant - content only
  if (variant === 'minimal') {
    return (
      <div className={`min-h-screen bg-gray-950 ${className}`}>
        <div className="max-w-7xl mx-auto mobile-container py-4">
          {children}
        </div>
      </div>
    )
  }

  // Full variant - complete layout with all features
  return (
    <div className={`min-h-screen bg-gray-950 ${className}`}>
      <div className="max-w-7xl mx-auto mobile-container py-4 sm:py-6 lg:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
          <nav
            id="profile-navigation"
            className="lg:col-span-1"
            aria-label="Profile navigation"
            role="navigation"
          >
            {navigation === 'consolidated' ? (
              <ConsolidatedNavigation
                profile={profile}
                wishlistItemCount={wishlistItemCount}
                loading={false}
                variant="desktop"
                showSearch={true}
                showQuickSettings={true}
                showBreadcrumbs={true}
                onNavigate={(href) => {
                  analytics.trackNavigation({
                    category: 'consolidated_nav',
                    page: href,
                    timeToFind: 0,
                    navigationStyle: 'optimized',
                    searchUsed: false,
                    clickDepth: 1,
                    taskCompleted: true
                  })
                }}
              />
            ) : (
              <div>Navigation (temporarily simplified)</div>
            )}
          </nav>
          <main
            id="main-content"
            className="lg:col-span-3"
            role="main"
            aria-label="Profile content"
          >
            <div id="settings-content">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}

// Named export for better import consistency
export { ProfileLayout }

// Default export for backward compatibility
export default ProfileLayout
