/**
 * Unified Order Status Type Definitions
 * 
 * This file standardizes order status definitions across all components
 * to resolve inconsistencies identified in the UI/UX analysis.
 * 
 * Previously, order statuses were defined differently across:
 * - types/order.ts: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled'
 * - types/global.ts: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
 * - app/profile/orders/page.tsx: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
 */

import { Clock, Package, Truck, CheckCircle, XCircle, RefreshCw } from 'lucide-react';

/**
 * Unified order status type - standardized across all components
 */
export type OrderStatus = 
  | 'pending' 
  | 'processing' 
  | 'shipped' 
  | 'delivered' 
  | 'cancelled' 
  | 'refunded';

/**
 * Order status configuration with user-friendly labels, descriptions,
 * visual styling, and available actions for both users and admins
 */
export const ORDER_STATUS_CONFIG = {
  pending: {
    label: 'Order Placed',
    description: 'Your order has been received and is being prepared',
    userDescription: 'We\'ve received your order and will begin processing it soon.',
    adminDescription: 'Order received, awaiting processing',
    color: 'bg-yellow-600',
    textColor: 'text-yellow-500',
    bgColor: 'bg-yellow-600/10',
    borderColor: 'border-yellow-600/30',
    icon: Clock,
    progress: 20,
    userActions: ['cancel', 'view'],
    adminActions: ['process', 'cancel', 'edit'],
    nextStatuses: ['processing', 'cancelled'],
    isActive: true,
    isCancellable: true
  },
  processing: {
    label: 'Processing',
    description: 'Your order is being prepared for shipment',
    userDescription: 'We\'re carefully preparing your items for shipment.',
    adminDescription: 'Order is being processed and prepared',
    color: 'bg-blue-600',
    textColor: 'text-blue-500',
    bgColor: 'bg-blue-600/10',
    borderColor: 'border-blue-600/30',
    icon: Package,
    progress: 40,
    userActions: ['view'],
    adminActions: ['ship', 'cancel', 'edit'],
    nextStatuses: ['shipped', 'cancelled'],
    isActive: true,
    isCancellable: false
  },
  shipped: {
    label: 'Shipped',
    description: 'Your order is on its way to you',
    userDescription: 'Your order has been shipped and is on its way to you.',
    adminDescription: 'Order has been shipped to customer',
    color: 'bg-purple-600',
    textColor: 'text-purple-500',
    bgColor: 'bg-purple-600/10',
    borderColor: 'border-purple-600/30',
    icon: Truck,
    progress: 70,
    userActions: ['track', 'view'],
    adminActions: ['deliver', 'view'],
    nextStatuses: ['delivered'],
    isActive: true,
    isCancellable: false
  },
  delivered: {
    label: 'Delivered',
    description: 'Your order has been successfully delivered',
    userDescription: 'Your order has been delivered. Enjoy your new keycaps!',
    adminDescription: 'Order successfully delivered to customer',
    color: 'bg-green-600',
    textColor: 'text-green-500',
    bgColor: 'bg-green-600/10',
    borderColor: 'border-green-600/30',
    icon: CheckCircle,
    progress: 100,
    userActions: ['review', 'reorder', 'view'],
    adminActions: ['view'],
    nextStatuses: [],
    isActive: false,
    isCancellable: false
  },
  cancelled: {
    label: 'Cancelled',
    description: 'This order has been cancelled',
    userDescription: 'This order was cancelled. If you have questions, please contact support.',
    adminDescription: 'Order was cancelled',
    color: 'bg-red-600',
    textColor: 'text-red-500',
    bgColor: 'bg-red-600/10',
    borderColor: 'border-red-600/30',
    icon: XCircle,
    progress: 0,
    userActions: ['reorder', 'view'],
    adminActions: ['view'],
    nextStatuses: [],
    isActive: false,
    isCancellable: false
  },
  refunded: {
    label: 'Refunded',
    description: 'This order has been refunded',
    userDescription: 'Your refund has been processed and should appear in your account soon.',
    adminDescription: 'Order refund has been processed',
    color: 'bg-gray-600',
    textColor: 'text-gray-500',
    bgColor: 'bg-gray-600/10',
    borderColor: 'border-gray-600/30',
    icon: RefreshCw,
    progress: 0,
    userActions: ['view'],
    adminActions: ['view'],
    nextStatuses: [],
    isActive: false,
    isCancellable: false
  }
} as const;

/**
 * Get order status configuration
 */
export const getOrderStatusConfig = (status: OrderStatus) => {
  return ORDER_STATUS_CONFIG[status];
};

/**
 * Get all available order statuses
 */
export const getAllOrderStatuses = (): OrderStatus[] => {
  return Object.keys(ORDER_STATUS_CONFIG) as OrderStatus[];
};

/**
 * Get active order statuses (orders that are still in progress)
 */
export const getActiveOrderStatuses = (): OrderStatus[] => {
  return getAllOrderStatuses().filter(status => 
    ORDER_STATUS_CONFIG[status].isActive
  );
};

/**
 * Get cancellable order statuses
 */
export const getCancellableOrderStatuses = (): OrderStatus[] => {
  return getAllOrderStatuses().filter(status => 
    ORDER_STATUS_CONFIG[status].isCancellable
  );
};

/**
 * Get next possible statuses for a given status
 */
export const getNextStatuses = (currentStatus: OrderStatus): OrderStatus[] => {
  return ORDER_STATUS_CONFIG[currentStatus].nextStatuses;
};

/**
 * Check if an order status transition is valid
 */
export const isValidStatusTransition = (
  fromStatus: OrderStatus, 
  toStatus: OrderStatus
): boolean => {
  return ORDER_STATUS_CONFIG[fromStatus].nextStatuses.includes(toStatus);
};

/**
 * Get order progress percentage
 */
export const getOrderProgress = (status: OrderStatus): number => {
  return ORDER_STATUS_CONFIG[status].progress;
};

/**
 * Get available actions for a user based on order status
 */
export const getUserActions = (status: OrderStatus): string[] => {
  return ORDER_STATUS_CONFIG[status].userActions;
};

/**
 * Get available actions for an admin based on order status
 */
export const getAdminActions = (status: OrderStatus): string[] => {
  return ORDER_STATUS_CONFIG[status].adminActions;
};

/**
 * Order status display utilities
 */
export const OrderStatusUtils = {
  getConfig: getOrderStatusConfig,
  getAllStatuses: getAllOrderStatuses,
  getActiveStatuses: getActiveOrderStatuses,
  getCancellableStatuses: getCancellableOrderStatuses,
  getNextStatuses,
  isValidTransition: isValidStatusTransition,
  getProgress: getOrderProgress,
  getUserActions,
  getAdminActions
};

/**
 * Type guard to check if a string is a valid OrderStatus
 */
export const isValidOrderStatus = (status: string): status is OrderStatus => {
  return status in ORDER_STATUS_CONFIG;
};
