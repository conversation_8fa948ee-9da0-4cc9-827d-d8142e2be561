/**
 * Global Type Definitions
 * 
 * Comprehensive type definitions for the Syndicaps application
 * including user management, products, orders, and system types.
 * 
 * <AUTHOR> Team
 */

/**
 * Base entity interface with common fields
 */
export interface BaseEntity {
  /** Unique identifier */
  id: string;
  /** Creation timestamp */
  createdAt: Date;
  /** Last update timestamp */
  updatedAt: Date;
}

/**
 * User role enumeration
 */
export type UserRole = 'user' | 'admin' | 'super_admin' | 'moderator' | 'analyst' | 'support';

/**
 * User status enumeration
 */
export type UserStatus = 'active' | 'inactive' | 'banned' | 'pending';

/**
 * User interface with complete profile information
 */
export interface User extends BaseEntity {
  /** User's email address */
  email: string;
  /** User's display name */
  displayName?: string;
  /** User's first name */
  firstName?: string;
  /** User's last name */
  lastName?: string;
  /** User's profile photo URL */
  photoURL?: string;
  /** User's role in the system */
  role: UserRole;
  /** User's account status */
  status: UserStatus;
  /** Email verification status */
  emailVerified: boolean;
  /** User's total points */
  totalPoints: number;
  /** User's current level */
  level: number;
  /** User's achievements */
  achievements: string[];
  /** User's preferences */
  preferences: UserPreferences;
  /** User's profile completion percentage */
  profileCompletion: number;
  /** Last login timestamp */
  lastLoginAt?: Date;
}

/**
 * User preferences interface
 */
export interface UserPreferences {
  /** Email notification preferences */
  emailNotifications: {
    marketing: boolean;
    orderUpdates: boolean;
    raffleNotifications: boolean;
    achievementNotifications: boolean;
  };
  /** Privacy settings */
  privacy: {
    profileVisibility: 'public' | 'private' | 'friends';
    showAchievements: boolean;
    showPoints: boolean;
  };
  /** Theme preferences */
  theme: 'dark' | 'light' | 'auto';
  /** Language preference */
  language: string;
}

/**
 * Product category enumeration
 */
export type ProductCategory = 'artisan' | 'keycap-set' | 'switch' | 'accessory' | 'keyboard';

/**
 * Product status enumeration
 */
export type ProductStatus = 'active' | 'inactive' | 'out-of-stock' | 'discontinued';

/**
 * Product interface
 */
export interface Product extends BaseEntity {
  /** Product name */
  name: string;
  /** Product description */
  description: string;
  /** Product price in USD */
  price: number;
  /** Product category */
  category: ProductCategory;
  /** Product status */
  status: ProductStatus;
  /** Product images */
  images: string[];
  /** Main product image */
  image: string;
  /** Product tags */
  tags: string[];
  /** Stock quantity */
  stockQuantity: number;
  /** Product SKU */
  sku: string;
  /** Product weight in grams */
  weight?: number;
  /** Product dimensions */
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  /** SEO metadata */
  seo: {
    title: string;
    description: string;
    keywords: string[];
  };
  /** Product variants */
  variants?: ProductVariant[];
}

/**
 * Product variant interface
 */
export interface ProductVariant {
  /** Variant ID */
  id: string;
  /** Variant name */
  name: string;
  /** Variant price (if different from base) */
  price?: number;
  /** Variant stock quantity */
  stockQuantity: number;
  /** Variant SKU */
  sku: string;
  /** Variant attributes */
  attributes: Record<string, string>;
}

/**
 * Cart item interface
 */
export interface CartItem {
  /** Product reference */
  product: Product;
  /** Quantity in cart */
  quantity: number;
  /** Selected variant ID */
  variantId?: string;
  /** Date added to cart */
  addedAt: Date;
}

/**
 * Order status enumeration - now imported from unified orderStatus.ts
 */
export { OrderStatus } from './orderStatus';

/**
 * Payment status enumeration
 */
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

/**
 * Payment method enumeration
 */
export type PaymentMethod = 'paypal' | 'stripe' | 'points';

/**
 * Order interface
 */
export interface Order extends BaseEntity {
  /** User ID who placed the order */
  userId: string;
  /** Order items */
  items: OrderItem[];
  /** Total amount */
  totalAmount: number;
  /** Order status */
  status: OrderStatus;
  /** Payment information */
  payment: {
    method: PaymentMethod;
    status: PaymentStatus;
    transactionId?: string;
    paidAt?: Date;
  };
  /** Shipping address */
  shippingAddress: ShippingAddress;
  /** Billing address */
  billingAddress?: ShippingAddress;
  /** Tracking information */
  tracking?: {
    carrier: string;
    trackingNumber: string;
    trackingUrl?: string;
  };
  /** Points earned from this order */
  pointsEarned: number;
  /** Order notes */
  notes?: string;
}

/**
 * Order item interface
 */
export interface OrderItem {
  /** Product ID */
  productId: string;
  /** Product name at time of order */
  productName: string;
  /** Price at time of order */
  price: number;
  /** Quantity ordered */
  quantity: number;
  /** Variant ID if applicable */
  variantId?: string;
  /** Variant name if applicable */
  variantName?: string;
}

/**
 * Shipping address interface
 */
export interface ShippingAddress {
  /** Address ID */
  id: string;
  /** User ID */
  userId: string;
  /** Recipient name */
  name: string;
  /** Street address */
  address: string;
  /** City */
  city: string;
  /** State/Province */
  state: string;
  /** ZIP/Postal code */
  zipCode: string;
  /** Country */
  country: string;
  /** Phone number */
  phone?: string;
  /** Whether this is the default address */
  isDefault: boolean;
}

/**
 * API response wrapper interface
 */
export interface ApiResponse<T = any> {
  /** Whether the request was successful */
  success: boolean;
  /** Response data */
  data?: T;
  /** Error message if unsuccessful */
  error?: string;
  /** Additional metadata */
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    hasMore?: boolean;
  };
}

/**
 * Pagination parameters interface
 */
export interface PaginationParams {
  /** Page number (1-based) */
  page: number;
  /** Items per page */
  limit: number;
  /** Sort field */
  sortBy?: string;
  /** Sort direction */
  sortOrder?: 'asc' | 'desc';
}

/**
 * Search parameters interface
 */
export interface SearchParams extends PaginationParams {
  /** Search query */
  query?: string;
  /** Category filter */
  category?: ProductCategory;
  /** Price range filter */
  priceRange?: {
    min: number;
    max: number;
  };
  /** Status filter */
  status?: ProductStatus;
  /** Tags filter */
  tags?: string[];
}

/**
 * Form validation error interface
 */
export interface ValidationError {
  /** Field name */
  field: string;
  /** Error message */
  message: string;
  /** Error code */
  code?: string;
}

/**
 * Loading state interface
 */
export interface LoadingState {
  /** Whether currently loading */
  isLoading: boolean;
  /** Loading message */
  message?: string;
  /** Loading progress (0-100) */
  progress?: number;
}

/**
 * Error state interface
 */
export interface ErrorState {
  /** Whether there's an error */
  hasError: boolean;
  /** Error message */
  message?: string;
  /** Error code */
  code?: string;
  /** Error details */
  details?: any;
}

/**
 * Component props with children
 */
export interface PropsWithChildren {
  /** Child components */
  children: React.ReactNode;
}

/**
 * Component props with className
 */
export interface PropsWithClassName {
  /** CSS class name */
  className?: string;
}

/**
 * Common component props
 */
export interface CommonProps extends PropsWithChildren, PropsWithClassName {
  /** Component ID */
  id?: string;
  /** Test ID for testing */
  testId?: string;
}
