import { Timestamp } from 'firebase/firestore';
import { OrderStatus } from './orderStatus';

export interface OrderItem {
  id: string
  productId?: string
  productName?: string
  name: string
  price: number
  quantity: number
  image?: string
  variant?: string
}

export interface OrderData {
  id: string
  userId: string
  items: OrderItem[]
  total: number
  totalAmount: number // Alias for total
  paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded'
  status: OrderStatus // Now uses unified OrderStatus type
  shippingAddress: {
    fullName: string
    address: string
    city: string
    state: string
    zipCode: string
    country: string
    phone: string
  }
  billing?: {
    fullName: string
    address: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  shippingDetails?: {
    trackingNumber?: string
    carrier?: string
    estimatedDelivery?: Date
  }
  createdAt: Timestamp | Date
  updatedAt: Timestamp | Date
}

export interface Order extends OrderData {}
