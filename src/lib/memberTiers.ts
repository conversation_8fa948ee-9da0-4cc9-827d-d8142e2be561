/**
 * Member Tier System
 *
 * Centralized tier management system for consistent member status
 * across all components and features.
 */

import React from 'react'
import { Shield, Star, Award, Crown, Gem } from 'lucide-react'

export type MemberTier = 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'

export interface TierInfo {
  tier: MemberTier
  name: string
  minPoints: number
  maxPoints: number | null
  color: string
  bgColor: string
  borderColor: string
  gradientColors: string
  benefits: string[]
  icon: string
  iconComponent: React.ComponentType<{ size?: number; className?: string }>
}

export interface TierProgress {
  currentTier: MemberTier
  nextTier: MemberTier | null
  pointsNeeded: number
  progress: number // percentage to next tier
  tierInfo: TierInfo
}

/**
 * Tier definitions with consistent thresholds
 */
export const TIER_DEFINITIONS: Record<MemberTier, TierInfo> = {
  bronze: {
    tier: 'bronze',
    name: 'Bronze Member',
    minPoints: 0,
    maxPoints: 999,
    color: 'tier-bronze-text',
    bgColor: 'tier-bronze-bg',
    borderColor: 'tier-bronze-border',
    gradientColors: 'from-orange-400 to-amber-600',
    benefits: [
      'Standard shipping rates',
      'Basic customer support',
      'Access to public raffles',
      'Product reviews and ratings'
    ],
    icon: 'shield',
    iconComponent: Shield
  },
  silver: {
    tier: 'silver',
    name: 'Silver Member',
    minPoints: 1000,
    maxPoints: 4999,
    color: 'tier-silver-text',
    bgColor: 'tier-silver-bg',
    borderColor: 'tier-silver-border',
    gradientColors: 'from-gray-300 to-gray-500',
    benefits: [
      'All Bronze benefits',
      '5% discount on all purchases',
      'Priority customer support',
      'Access to silver-tier raffles',
      'Early access to sales (12 hours)'
    ],
    icon: 'star',
    iconComponent: Star
  },
  gold: {
    tier: 'gold',
    name: 'Gold Member',
    minPoints: 5000,
    maxPoints: 14999,
    color: 'tier-gold-text',
    bgColor: 'tier-gold-bg',
    borderColor: 'tier-gold-border',
    gradientColors: 'from-yellow-300 to-yellow-500',
    benefits: [
      'All Silver benefits',
      '10% discount on all purchases',
      'Free standard shipping',
      'VIP customer support',
      'Access to gold-tier raffles',
      'Early access to sales (24 hours)',
      'Exclusive monthly deals'
    ],
    icon: 'award',
    iconComponent: Award
  },
  platinum: {
    tier: 'platinum',
    name: 'Platinum Member',
    minPoints: 15000,
    maxPoints: 49999,
    color: 'tier-platinum-text',
    bgColor: 'tier-platinum-bg',
    borderColor: 'tier-platinum-border',
    gradientColors: 'from-slate-300 to-slate-500',
    benefits: [
      'All Gold benefits',
      '15% discount on all purchases',
      'Free express shipping',
      'Dedicated account manager',
      'Access to platinum-tier raffles',
      'Early access to sales (48 hours)',
      'Exclusive member events',
      'Custom keycap design service',
      'Birthday month special offers'
    ],
    icon: 'crown',
    iconComponent: Crown
  },
  diamond: {
    tier: 'diamond',
    name: 'Diamond Member',
    minPoints: 50000,
    maxPoints: null,
    color: 'tier-diamond-text',
    bgColor: 'tier-diamond-bg',
    borderColor: 'tier-diamond-border',
    gradientColors: 'from-cyan-300 to-blue-400',
    benefits: [
      'All Platinum benefits',
      '20% discount on all purchases',
      'Free premium shipping with white-glove delivery',
      'Personal concierge support (2-hour response)',
      'Access to Diamond-exclusive collection',
      'First access to all new releases',
      'Triple points earning opportunities',
      'Complimentary custom design consultation',
      'VIP events and exclusive meetups',
      'Annual Diamond member gift',
      'Priority product development input'
    ],
    icon: 'gem',
    iconComponent: Gem
  }
}

/**
 * Get user tier based on points
 */
export function getUserTier(points: number): MemberTier {
  if (points >= 50000) return 'diamond'
  if (points >= 15000) return 'platinum'
  if (points >= 5000) return 'gold'
  if (points >= 1000) return 'silver'
  return 'bronze'
}

/**
 * Get tier information
 */
export function getTierInfo(tier: MemberTier): TierInfo {
  return TIER_DEFINITIONS[tier]
}

/**
 * Get tier info by points
 */
export function getTierInfoByPoints(points: number): TierInfo {
  const tier = getUserTier(points)
  return getTierInfo(tier)
}

/**
 * Calculate progress to next tier
 */
export function getTierProgress(points: number): TierProgress {
  const currentTier = getUserTier(points)
  const currentTierInfo = getTierInfo(currentTier)
  
  let nextTier: MemberTier | null = null
  let pointsNeeded = 0
  let progress = 100 // Default to 100% if at max tier
  
  switch (currentTier) {
    case 'bronze':
      nextTier = 'silver'
      pointsNeeded = 1000 - points
      progress = (points / 1000) * 100
      break
    case 'silver':
      nextTier = 'gold'
      pointsNeeded = 5000 - points
      progress = ((points - 1000) / 4000) * 100
      break
    case 'gold':
      nextTier = 'platinum'
      pointsNeeded = 15000 - points
      progress = ((points - 5000) / 10000) * 100
      break
    case 'platinum':
      nextTier = 'diamond'
      pointsNeeded = 50000 - points
      progress = ((points - 15000) / 35000) * 100
      break
    case 'diamond':
      nextTier = null
      pointsNeeded = 0
      progress = 100
      break
  }
  
  return {
    currentTier,
    nextTier,
    pointsNeeded,
    progress,
    tierInfo: currentTierInfo
  }
}

/**
 * Get tier styling classes
 */
export function getTierStyles(tier: MemberTier): string {
  const tierInfo = getTierInfo(tier)
  return `${tierInfo.bgColor} ${tierInfo.color} ${tierInfo.borderColor}`
}

/**
 * Get all tier benefits for a specific tier
 */
export function getTierBenefits(tier: MemberTier): string[] {
  const tierInfo = getTierInfo(tier)
  return tierInfo.benefits
}

/**
 * Check if user has access to a feature based on tier
 */
export function hasFeatureAccess(userPoints: number, requiredTier: MemberTier): boolean {
  const userTier = getUserTier(userPoints)
  const tierOrder: MemberTier[] = ['bronze', 'silver', 'gold', 'platinum', 'diamond']

  const userTierIndex = tierOrder.indexOf(userTier)
  const requiredTierIndex = tierOrder.indexOf(requiredTier)

  return userTierIndex >= requiredTierIndex
}

/**
 * Get discount percentage for tier
 */
export function getTierDiscount(tier: MemberTier): number {
  switch (tier) {
    case 'silver': return 5
    case 'gold': return 10
    case 'platinum': return 15
    case 'diamond': return 20
    default: return 0
  }
}

/**
 * Check if tier gets free shipping
 */
export function hasFreeShipping(tier: MemberTier): boolean {
  return tier === 'gold' || tier === 'platinum' || tier === 'diamond'
}

/**
 * Get early access hours for tier
 */
export function getEarlyAccessHours(tier: MemberTier): number {
  switch (tier) {
    case 'silver': return 12
    case 'gold': return 24
    case 'platinum': return 48
    case 'diamond': return 72
    default: return 0
  }
}
