/**
 * Unified Navigation Constants and Labels
 * 
 * This file standardizes navigation terminology across all components
 * to resolve inconsistencies identified in the UI/UX analysis.
 * 
 * Previously, navigation labels were inconsistent across:
 * - UnifiedNavigation.tsx: 'Account & Personal' | 'Orders & Activity' | 'Rewards & Social' | 'Settings & Support'
 * - ProfileBottomNav.tsx: 'Account' | 'Orders' | 'Rewards' | 'Settings'
 * - SmartNavigation.tsx: 'Account & Personal' | 'Orders & Activity' | 'Rewards & Gamification' | 'Settings & Support'
 */

import { 
  User, 
  Package, 
  Trophy, 
  Settings, 
  Home, 
  ShoppingBag, 
  Users, 
  Heart,
  ShoppingCart,
  Bell,
  Search,
  HelpCircle,
  Shield,
  CreditCard,
  MapPin,
  Phone,
  Mail,
  Link as LinkIcon,
  Eye,
  Star,
  Gift,
  Award,
  Target,
  Calendar,
  Clock,
  FileText,
  Download,
  Trash2,
  Edit,
  Plus
} from 'lucide-react';

/**
 * Main navigation categories with consistent 4-category structure
 * Following Syndicaps brand personality: collaborative, playful, edgy
 */
export const NAVIGATION_CATEGORIES = {
  account: {
    id: 'account',
    label: 'Account & Personal',
    shortLabel: 'Account',
    description: 'Personal information and account management',
    icon: User,
    priority: 'high',
    color: 'accent',
    emoji: '👤'
  },
  orders: {
    id: 'orders',
    label: 'Orders & Activity',
    shortLabel: 'Orders',
    description: 'Shopping history and user activity',
    icon: Package,
    priority: 'high',
    color: 'blue',
    emoji: '📦'
  },
  rewards: {
    id: 'rewards',
    label: 'Rewards & Social',
    shortLabel: 'Rewards',
    description: 'Rewards, achievements, and social features',
    icon: Trophy,
    priority: 'medium',
    color: 'yellow',
    emoji: '🏆'
  },
  settings: {
    id: 'settings',
    label: 'Settings & Support',
    shortLabel: 'Settings',
    description: 'Preferences and help resources',
    icon: Settings,
    priority: 'medium',
    color: 'gray',
    emoji: '⚙️'
  }
} as const;

/**
 * Global navigation items for main application navigation
 */
export const GLOBAL_NAVIGATION = {
  home: {
    id: 'home',
    label: 'Home',
    href: '/',
    icon: Home,
    description: 'Return to homepage',
    showInMobile: true,
    showInDesktop: true
  },
  shop: {
    id: 'shop',
    label: 'Shop',
    href: '/shop',
    icon: ShoppingBag,
    description: 'Browse keycap products',
    showInMobile: true,
    showInDesktop: true
  },
  community: {
    id: 'community',
    label: 'Community',
    href: '/community',
    icon: Users,
    description: 'Join the keycap community',
    showInMobile: true,
    showInDesktop: true
  },
  wishlist: {
    id: 'wishlist',
    label: 'Kapsul Collection',
    shortLabel: 'Wishlist',
    href: '/wishlist',
    icon: Heart,
    description: 'Your saved keycap collections',
    showInMobile: true,
    showInDesktop: true,
    requiresAuth: true
  },
  cart: {
    id: 'cart',
    label: 'Cart',
    href: '/cart',
    icon: ShoppingCart,
    description: 'Shopping cart',
    showInMobile: true,
    showInDesktop: true,
    showBadge: true
  }
} as const;

/**
 * Profile navigation items organized by category
 */
export const PROFILE_NAVIGATION = {
  // Account & Personal Category
  account: {
    profile: {
      id: 'profile',
      label: 'Profile Overview',
      href: '/profile',
      icon: User,
      description: 'View and edit your profile information',
      category: 'account',
      priority: 1
    },
    personal: {
      id: 'personal',
      label: 'Personal Information',
      href: '/profile/account',
      icon: Edit,
      description: 'Manage personal details and contact information',
      category: 'account',
      priority: 2
    },
    contact: {
      id: 'contact',
      label: 'Contact & Location',
      href: '/profile/contact',
      icon: MapPin,
      description: 'Update contact details and addresses',
      category: 'account',
      priority: 3
    },
    social: {
      id: 'social',
      label: 'Social Links',
      href: '/profile/social',
      icon: LinkIcon,
      description: 'Connect your social media accounts',
      category: 'account',
      priority: 4
    }
  },
  
  // Orders & Activity Category
  orders: {
    history: {
      id: 'order-history',
      label: 'Order History',
      href: '/profile/orders',
      icon: Package,
      description: 'View your past and current orders',
      category: 'orders',
      priority: 1
    },
    tracking: {
      id: 'order-tracking',
      label: 'Track Orders',
      href: '/profile/orders/tracking',
      icon: Clock,
      description: 'Track your active shipments',
      category: 'orders',
      priority: 2
    },
    activity: {
      id: 'activity',
      label: 'Adventure Log',
      shortLabel: 'Activity',
      href: '/profile/activity',
      icon: Calendar,
      description: 'Your activity history and engagement',
      category: 'orders',
      priority: 3
    },
    downloads: {
      id: 'downloads',
      label: 'Downloads',
      href: '/profile/downloads',
      icon: Download,
      description: 'Access your digital purchases',
      category: 'orders',
      priority: 4
    }
  },
  
  // Rewards & Social Category
  rewards: {
    overview: {
      id: 'rewards-overview',
      label: 'Treasure Vault',
      shortLabel: 'Rewards',
      href: '/profile/rewards',
      icon: Trophy,
      description: 'View your points, rewards, and achievements',
      category: 'rewards',
      priority: 1
    },
    points: {
      id: 'points',
      label: 'Collaboration Points',
      shortLabel: 'Points',
      href: '/profile/points',
      icon: Star,
      description: 'Manage your loyalty points',
      category: 'rewards',
      priority: 2
    },
    achievements: {
      id: 'achievements',
      label: 'Shared Achievements',
      shortLabel: 'Achievements',
      href: '/profile/achievements',
      icon: Award,
      description: 'View your unlocked achievements',
      category: 'rewards',
      priority: 3
    },
    challenges: {
      id: 'challenges',
      label: 'Community Challenges',
      shortLabel: 'Challenges',
      href: '/profile/challenges',
      icon: Target,
      description: 'Participate in community challenges',
      category: 'rewards',
      priority: 4
    }
  },
  
  // Settings & Support Category
  settings: {
    preferences: {
      id: 'preferences',
      label: 'Preferences',
      href: '/profile/preferences',
      icon: Settings,
      description: 'Customize your experience',
      category: 'settings',
      priority: 1
    },
    privacy: {
      id: 'privacy',
      label: 'Privacy Settings',
      href: '/profile/privacy',
      icon: Shield,
      description: 'Manage your privacy and data settings',
      category: 'settings',
      priority: 2
    },
    notifications: {
      id: 'notifications',
      label: 'Notifications',
      href: '/profile/notifications',
      icon: Bell,
      description: 'Configure notification preferences',
      category: 'settings',
      priority: 3
    },
    billing: {
      id: 'billing',
      label: 'Billing & Payment',
      href: '/profile/billing',
      icon: CreditCard,
      description: 'Manage payment methods and billing',
      category: 'settings',
      priority: 4
    },
    support: {
      id: 'support',
      label: 'Help & Support',
      href: '/support',
      icon: HelpCircle,
      description: 'Get help and contact support',
      category: 'settings',
      priority: 5
    }
  }
} as const;

/**
 * Admin navigation labels (consistent with user-facing terminology)
 */
export const ADMIN_NAVIGATION = {
  orders: {
    label: 'Order Management',
    shortLabel: 'Orders',
    description: 'Manage customer orders and fulfillment',
    icon: Package
  },
  users: {
    label: 'User Management',
    shortLabel: 'Users',
    description: 'Manage user accounts and permissions',
    icon: User
  },
  community: {
    label: 'Community Hub',
    shortLabel: 'Community',
    description: 'Manage community features and content',
    icon: Users
  }
} as const;

/**
 * Utility functions for navigation
 */
export const NavigationUtils = {
  /**
   * Get navigation items by category
   */
  getItemsByCategory: (category: keyof typeof NAVIGATION_CATEGORIES) => {
    return Object.values(PROFILE_NAVIGATION[category]);
  },
  
  /**
   * Get all navigation items flattened
   */
  getAllItems: () => {
    return Object.values(PROFILE_NAVIGATION).flatMap(category => 
      Object.values(category)
    );
  },
  
  /**
   * Get navigation item by ID
   */
  getItemById: (id: string) => {
    return NavigationUtils.getAllItems().find(item => item.id === id);
  },
  
  /**
   * Get category by ID
   */
  getCategoryById: (id: string) => {
    return NAVIGATION_CATEGORIES[id as keyof typeof NAVIGATION_CATEGORIES];
  },
  
  /**
   * Check if item requires authentication
   */
  requiresAuth: (item: any) => {
    return item.requiresAuth === true;
  }
};

/**
 * Navigation constants for consistent usage
 */
export const NAVIGATION_CONSTANTS = {
  CATEGORIES: NAVIGATION_CATEGORIES,
  GLOBAL: GLOBAL_NAVIGATION,
  PROFILE: PROFILE_NAVIGATION,
  ADMIN: ADMIN_NAVIGATION,
  UTILS: NavigationUtils
} as const;

/**
 * Type definitions for navigation items
 */
export type NavigationCategoryId = keyof typeof NAVIGATION_CATEGORIES;
export type GlobalNavigationId = keyof typeof GLOBAL_NAVIGATION;
export type ProfileNavigationId = string;

export type NavigationItem = {
  id: string;
  label: string;
  shortLabel?: string;
  href: string;
  icon: any;
  description: string;
  category?: string;
  priority?: number;
  requiresAuth?: boolean;
  showBadge?: boolean;
  showInMobile?: boolean;
  showInDesktop?: boolean;
};

export type NavigationCategory = {
  id: string;
  label: string;
  shortLabel?: string;
  description: string;
  icon: any;
  priority: 'high' | 'medium' | 'low';
  color: string;
  emoji: string;
};
