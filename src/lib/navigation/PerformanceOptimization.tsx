/**
 * Performance Optimization Utilities for Navigation System
 * 
 * Provides comprehensive performance optimization features:
 * - Bundle size optimization with code splitting
 * - Lazy loading for navigation components
 * - Performance monitoring and metrics
 * - Memory leak prevention
 * - Render optimization
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { lazy, Suspense, memo, useMemo, useCallback } from 'react'
import { NavigationVariant } from './NavigationFactory'

// ===== LAZY LOADING =====

/**
 * Lazy-loaded navigation components for code splitting
 */
export const LazyNavigationComponents = {
  ConsolidatedNavigation: lazy(() => 
    import('../components/navigation/ConsolidatedNavigation').then(module => ({
      default: module.ConsolidatedNavigation
    }))
  ),
  
  DesktopNavigation: lazy(() => 
    import('../components/navigation/ConsolidatedNavigation').then(module => ({
      default: module.DesktopNavigation
    }))
  ),
  
  MobileNavigation: lazy(() => 
    import('../components/navigation/ConsolidatedNavigation').then(module => ({
      default: module.MobileNavigation
    }))
  ),
  
  NavigationSearch: lazy(() => 
    import('../components/navigation/SearchComponents').then(module => ({
      default: module.NavigationSearch
    }))
  ),
  
  ContextualSuggestionsPanel: lazy(() => 
    import('../components/navigation/ContextualSuggestionsPanel').then(module => ({
      default: module.ContextualSuggestionsPanel
    }))
  ),
  
  SkipLinks: lazy(() => 
    import('../components/navigation/SkipLinks').then(module => ({
      default: module.SkipLinks
    }))
  )
}

/**
 * Loading fallback component for lazy-loaded navigation
 */
const NavigationSkeleton: React.FC<{ variant?: NavigationVariant }> = ({ variant = 'desktop' }) => (
  <div 
    className={`
      animate-pulse bg-gray-800 rounded-lg
      ${variant === 'mobile' ? 'h-16 w-full' : 'h-screen w-64'}
    `}
    role="progressbar"
    aria-label="Loading navigation"
  >
    <div className="p-4 space-y-3">
      {Array.from({ length: variant === 'mobile' ? 4 : 8 }).map((_, i) => (
        <div 
          key={i} 
          className="h-10 bg-gray-700 rounded animate-pulse"
          style={{ animationDelay: `${i * 100}ms` }}
        />
      ))}
    </div>
  </div>
)

/**
 * High-order component for lazy loading with suspense
 */
export const withLazyLoading = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<Partial<P>>
) => {
  const LazyComponent = lazy(() => Promise.resolve({ default: Component }))
  
  return memo((props: P) => (
    <Suspense fallback={fallback ? <fallback {...props} /> : <NavigationSkeleton />}>
      <LazyComponent {...props} />
    </Suspense>
  ))
}

// ===== PERFORMANCE MONITORING =====

interface PerformanceMetrics {
  renderTime: number
  bundleSize: number
  memoryUsage: number
  interactionTime: number
  searchLatency: number
  componentCount: number
}

interface PerformanceThresholds {
  renderTime: number // ms
  bundleSize: number // KB
  memoryUsage: number // MB
  interactionTime: number // ms
  searchLatency: number // ms
}

const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  renderTime: 100, // 100ms
  bundleSize: 50, // 50KB
  memoryUsage: 10, // 10MB
  interactionTime: 50, // 50ms
  searchLatency: 150 // 150ms
}

/**
 * Performance monitoring class for navigation components
 */
export class NavigationPerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {}
  private thresholds: PerformanceThresholds
  private observers: PerformanceObserver[] = []

  constructor(thresholds: Partial<PerformanceThresholds> = {}) {
    this.thresholds = { ...DEFAULT_THRESHOLDS, ...thresholds }
    this.initializeObservers()
  }

  private initializeObservers() {
    // Performance observer for navigation timing
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name.includes('navigation')) {
            this.metrics.renderTime = entry.duration
          }
        }
      })
      
      observer.observe({ entryTypes: ['measure', 'navigation'] })
      this.observers.push(observer)
    }
  }

  /**
   * Start performance measurement
   */
  startMeasurement(name: string) {
    if (typeof window !== 'undefined' && 'performance' in window) {
      performance.mark(`${name}-start`)
    }
  }

  /**
   * End performance measurement
   */
  endMeasurement(name: string): number {
    if (typeof window !== 'undefined' && 'performance' in window) {
      performance.mark(`${name}-end`)
      performance.measure(name, `${name}-start`, `${name}-end`)
      
      const measure = performance.getEntriesByName(name)[0]
      return measure?.duration || 0
    }
    return 0
  }

  /**
   * Measure component render time
   */
  measureRenderTime<T>(component: () => T): T {
    this.startMeasurement('navigation-render')
    const result = component()
    this.metrics.renderTime = this.endMeasurement('navigation-render')
    return result
  }

  /**
   * Measure search latency
   */
  measureSearchLatency(searchFn: () => Promise<any>): Promise<any> {
    this.startMeasurement('navigation-search')
    
    return searchFn().then(result => {
      this.metrics.searchLatency = this.endMeasurement('navigation-search')
      return result
    })
  }

  /**
   * Get current memory usage
   */
  getMemoryUsage(): number {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
      const memory = (performance as any).memory
      return Math.round(memory.usedJSHeapSize / 1024 / 1024) // MB
    }
    return 0
  }

  /**
   * Get performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return {
      renderTime: this.metrics.renderTime || 0,
      bundleSize: this.metrics.bundleSize || 0,
      memoryUsage: this.getMemoryUsage(),
      interactionTime: this.metrics.interactionTime || 0,
      searchLatency: this.metrics.searchLatency || 0,
      componentCount: this.metrics.componentCount || 0
    }
  }

  /**
   * Check if metrics exceed thresholds
   */
  checkThresholds(): { passed: boolean; violations: string[] } {
    const metrics = this.getMetrics()
    const violations: string[] = []

    Object.entries(this.thresholds).forEach(([key, threshold]) => {
      const value = metrics[key as keyof PerformanceMetrics]
      if (value > threshold) {
        violations.push(`${key}: ${value} exceeds threshold ${threshold}`)
      }
    })

    return {
      passed: violations.length === 0,
      violations
    }
  }

  /**
   * Generate performance report
   */
  generateReport(): string {
    const metrics = this.getMetrics()
    const { passed, violations } = this.checkThresholds()

    return `
Navigation Performance Report
============================
Render Time: ${metrics.renderTime}ms
Bundle Size: ${metrics.bundleSize}KB
Memory Usage: ${metrics.memoryUsage}MB
Interaction Time: ${metrics.interactionTime}ms
Search Latency: ${metrics.searchLatency}ms
Component Count: ${metrics.componentCount}

Status: ${passed ? '✅ PASSED' : '❌ FAILED'}
${violations.length > 0 ? `\nViolations:\n${violations.map(v => `- ${v}`).join('\n')}` : ''}
    `.trim()
  }

  /**
   * Cleanup observers
   */
  cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// ===== RENDER OPTIMIZATION =====

/**
 * Memoized navigation item component
 */
export const OptimizedNavigationItem = memo<{
  icon: React.ComponentType<any>
  label: string
  href: string
  active: boolean
  badge?: number | string
  onClick: () => void
}>(({ icon: Icon, label, href, active, badge, onClick }) => {
  const handleClick = useCallback(() => {
    onClick()
  }, [onClick])

  const badgeElement = useMemo(() => {
    if (!badge) return null
    return (
      <span className="nav-badge nav-badge-count">
        {badge}
      </span>
    )
  }, [badge])

  return (
    <button
      onClick={handleClick}
      className={`nav-item ${active ? 'nav-item-active' : ''}`}
      aria-current={active ? 'page' : undefined}
    >
      <Icon size={20} className="nav-item-icon" />
      <span className="nav-item-text">{label}</span>
      {badgeElement}
    </button>
  )
})

OptimizedNavigationItem.displayName = 'OptimizedNavigationItem'

/**
 * Virtualized navigation list for large datasets
 */
export const VirtualizedNavigationList: React.FC<{
  items: Array<{ id: string; [key: string]: any }>
  renderItem: (item: any, index: number) => React.ReactNode
  height: number
  itemHeight: number
}> = memo(({ items, renderItem, height, itemHeight }) => {
  const [scrollTop, setScrollTop] = React.useState(0)
  
  const visibleStart = Math.floor(scrollTop / itemHeight)
  const visibleEnd = Math.min(visibleStart + Math.ceil(height / itemHeight) + 1, items.length)
  
  const visibleItems = useMemo(() => 
    items.slice(visibleStart, visibleEnd),
    [items, visibleStart, visibleEnd]
  )

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  return (
    <div
      style={{ height, overflow: 'auto' }}
      onScroll={handleScroll}
      className="virtualized-nav-list"
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${visibleStart * itemHeight}px)` }}>
          {visibleItems.map((item, index) => (
            <div key={item.id} style={{ height: itemHeight }}>
              {renderItem(item, visibleStart + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
})

VirtualizedNavigationList.displayName = 'VirtualizedNavigationList'

// ===== BUNDLE SIZE OPTIMIZATION =====

/**
 * Tree-shakable navigation utilities
 */
export const NavigationUtils = {
  // Only import what's needed
  debounce: (fn: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout
    return (...args: any[]) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => fn.apply(null, args), delay)
    }
  },

  throttle: (fn: Function, limit: number) => {
    let inThrottle: boolean
    return (...args: any[]) => {
      if (!inThrottle) {
        fn.apply(null, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  memoize: <T extends (...args: any[]) => any>(fn: T): T => {
    const cache = new Map()
    return ((...args: any[]) => {
      const key = JSON.stringify(args)
      if (cache.has(key)) {
        return cache.get(key)
      }
      const result = fn(...args)
      cache.set(key, result)
      return result
    }) as T
  }
}

// ===== EXPORT PERFORMANCE MONITOR INSTANCE =====

export const navigationPerformanceMonitor = new NavigationPerformanceMonitor()

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    navigationPerformanceMonitor.cleanup()
  })
}
