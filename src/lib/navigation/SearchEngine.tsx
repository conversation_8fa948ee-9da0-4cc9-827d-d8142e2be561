/**
 * Navigation Search Engine
 * 
 * Advanced search functionality for navigation components with fuzzy matching,
 * category filtering, recent items tracking, and intelligent suggestions.
 * 
 * Features:
 * - Fuzzy string matching with configurable threshold
 * - Category-based filtering and grouping
 * - Recent items tracking with decay scoring
 * - Keyboard navigation support
 * - Search analytics and performance optimization
 * - Contextual search suggestions
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import { NavigationItem, NavigationCategory } from './NavigationProvider'

// ===== TYPES =====

export interface SearchResult {
  item: NavigationItem
  category: NavigationCategory
  score: number
  matchType: 'exact' | 'fuzzy' | 'category' | 'description' | 'recent'
  highlightRanges: Array<{ start: number; end: number }>
}

export interface SearchOptions {
  query: string
  categories?: string[]
  maxResults?: number
  includeRecent?: boolean
  fuzzyThreshold?: number
  boostRecent?: boolean
  contextualBoost?: boolean
}

export interface SearchAnalytics {
  totalSearches: number
  averageResultCount: number
  popularQueries: Record<string, number>
  clickThroughRates: Record<string, number>
  searchToNavigationTime: number[]
}

// ===== FUZZY MATCHING UTILITIES =====

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))
  
  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,     // deletion
        matrix[j - 1][i] + 1,     // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      )
    }
  }
  
  return matrix[str2.length][str1.length]
}

/**
 * Calculate fuzzy match score (0-1, higher is better)
 */
function fuzzyMatchScore(query: string, target: string): number {
  if (!query || !target) return 0
  
  const queryLower = query.toLowerCase()
  const targetLower = target.toLowerCase()
  
  // Exact match gets highest score
  if (targetLower === queryLower) return 1.0
  
  // Starts with query gets high score
  if (targetLower.startsWith(queryLower)) return 0.9
  
  // Contains query gets medium score
  if (targetLower.includes(queryLower)) return 0.7
  
  // Fuzzy matching for partial matches
  const distance = levenshteinDistance(queryLower, targetLower)
  const maxLength = Math.max(queryLower.length, targetLower.length)
  const similarity = 1 - (distance / maxLength)
  
  return Math.max(0, similarity - 0.3) // Threshold to filter out poor matches
}

/**
 * Find highlight ranges for matched text
 */
function findHighlightRanges(query: string, text: string): Array<{ start: number; end: number }> {
  if (!query || !text) return []
  
  const queryLower = query.toLowerCase()
  const textLower = text.toLowerCase()
  const ranges: Array<{ start: number; end: number }> = []
  
  let startIndex = 0
  while (true) {
    const index = textLower.indexOf(queryLower, startIndex)
    if (index === -1) break
    
    ranges.push({
      start: index,
      end: index + queryLower.length
    })
    
    startIndex = index + queryLower.length
  }
  
  return ranges
}

// ===== SEARCH ENGINE CLASS =====

export class NavigationSearchEngine {
  private recentItems: Array<{ item: NavigationItem; timestamp: number; frequency: number }> = []
  private analytics: SearchAnalytics = {
    totalSearches: 0,
    averageResultCount: 0,
    popularQueries: {},
    clickThroughRates: {},
    searchToNavigationTime: []
  }
  private searchStartTime: number = 0

  /**
   * Perform search across navigation items
   */
  search(
    items: NavigationItem[],
    categories: NavigationCategory[],
    options: SearchOptions
  ): SearchResult[] {
    this.searchStartTime = performance.now()
    this.analytics.totalSearches++
    
    const {
      query,
      categories: categoryFilter,
      maxResults = 10,
      includeRecent = true,
      fuzzyThreshold = 0.3,
      boostRecent = true,
      contextualBoost = true
    } = options

    // Track popular queries
    this.analytics.popularQueries[query] = (this.analytics.popularQueries[query] || 0) + 1

    if (!query.trim()) {
      return includeRecent ? this.getRecentResults(categories, maxResults) : []
    }

    const results: SearchResult[] = []
    const categoryMap = new Map(categories.map(cat => [cat.id, cat]))

    // Search through all items
    for (const item of items) {
      const category = categoryMap.get(item.categoryId)
      if (!category) continue

      // Apply category filter
      if (categoryFilter && categoryFilter.length > 0 && !categoryFilter.includes(category.id)) {
        continue
      }

      // Calculate match scores
      const labelScore = fuzzyMatchScore(query, item.label)
      const descriptionScore = fuzzyMatchScore(query, item.description || '') * 0.7
      const categoryScore = fuzzyMatchScore(query, category.label) * 0.5
      
      const baseScore = Math.max(labelScore, descriptionScore, categoryScore)
      
      if (baseScore < fuzzyThreshold) continue

      let finalScore = baseScore
      let matchType: SearchResult['matchType'] = 'fuzzy'

      // Determine match type
      if (item.label.toLowerCase() === query.toLowerCase()) {
        matchType = 'exact'
        finalScore *= 1.2
      } else if (item.label.toLowerCase().includes(query.toLowerCase())) {
        matchType = 'fuzzy'
        finalScore *= 1.1
      } else if (item.description?.toLowerCase().includes(query.toLowerCase())) {
        matchType = 'description'
        finalScore *= 0.9
      } else if (category.label.toLowerCase().includes(query.toLowerCase())) {
        matchType = 'category'
        finalScore *= 0.8
      }

      // Boost recent items
      if (boostRecent) {
        const recentItem = this.recentItems.find(r => r.item.id === item.id)
        if (recentItem) {
          const recencyBoost = this.calculateRecencyBoost(recentItem)
          finalScore *= (1 + recencyBoost)
          if (recencyBoost > 0.3) matchType = 'recent'
        }
      }

      // Contextual boost (could be enhanced with user behavior data)
      if (contextualBoost) {
        // Boost items in current category or related categories
        // This could be enhanced with user behavior analytics
        finalScore *= 1.0 // Placeholder for contextual logic
      }

      // Find highlight ranges
      const highlightRanges = findHighlightRanges(query, item.label)

      results.push({
        item,
        category,
        score: finalScore,
        matchType,
        highlightRanges
      })
    }

    // Sort by score (descending) and limit results
    const sortedResults = results
      .sort((a, b) => b.score - a.score)
      .slice(0, maxResults)

    // Update analytics
    this.analytics.averageResultCount = 
      (this.analytics.averageResultCount * (this.analytics.totalSearches - 1) + sortedResults.length) / 
      this.analytics.totalSearches

    return sortedResults
  }

  /**
   * Get recent search results when no query is provided
   */
  private getRecentResults(categories: NavigationCategory[], maxResults: number): SearchResult[] {
    const categoryMap = new Map(categories.map(cat => [cat.id, cat]))
    
    return this.recentItems
      .sort((a, b) => {
        const aScore = this.calculateRecencyBoost(a)
        const bScore = this.calculateRecencyBoost(b)
        return bScore - aScore
      })
      .slice(0, maxResults)
      .map(recentItem => {
        const category = categoryMap.get(recentItem.item.categoryId)!
        return {
          item: recentItem.item,
          category,
          score: this.calculateRecencyBoost(recentItem),
          matchType: 'recent' as const,
          highlightRanges: []
        }
      })
  }

  /**
   * Calculate recency boost score
   */
  private calculateRecencyBoost(recentItem: { timestamp: number; frequency: number }): number {
    const now = Date.now()
    const timeDiff = now - recentItem.timestamp
    const hoursSince = timeDiff / (1000 * 60 * 60)
    
    // Decay function: more recent = higher boost
    const recencyScore = Math.exp(-hoursSince / 24) // 24-hour half-life
    
    // Frequency boost: more frequently accessed = higher boost
    const frequencyScore = Math.min(recentItem.frequency / 10, 1) // Cap at 10 visits
    
    return (recencyScore * 0.7) + (frequencyScore * 0.3)
  }

  /**
   * Track navigation to an item (for recent items and analytics)
   */
  trackNavigation(item: NavigationItem, searchQuery?: string): void {
    const now = Date.now()
    
    // Update recent items
    const existingIndex = this.recentItems.findIndex(r => r.item.id === item.id)
    if (existingIndex >= 0) {
      this.recentItems[existingIndex].timestamp = now
      this.recentItems[existingIndex].frequency++
    } else {
      this.recentItems.push({
        item,
        timestamp: now,
        frequency: 1
      })
    }

    // Keep only last 50 recent items
    this.recentItems = this.recentItems
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 50)

    // Track search-to-navigation time
    if (searchQuery && this.searchStartTime > 0) {
      const navigationTime = performance.now() - this.searchStartTime
      this.analytics.searchToNavigationTime.push(navigationTime)
      
      // Track click-through rate
      this.analytics.clickThroughRates[searchQuery] = 
        (this.analytics.clickThroughRates[searchQuery] || 0) + 1
    }
  }

  /**
   * Get search analytics
   */
  getAnalytics(): SearchAnalytics {
    return { ...this.analytics }
  }

  /**
   * Get search suggestions based on popular queries and recent items
   */
  getSuggestions(partialQuery: string, limit: number = 5): string[] {
    const suggestions = new Set<string>()
    
    // Add popular queries that start with partial query
    Object.keys(this.analytics.popularQueries)
      .filter(query => query.toLowerCase().startsWith(partialQuery.toLowerCase()))
      .sort((a, b) => this.analytics.popularQueries[b] - this.analytics.popularQueries[a])
      .slice(0, limit)
      .forEach(query => suggestions.add(query))
    
    // Add recent item labels that match
    this.recentItems
      .filter(r => r.item.label.toLowerCase().includes(partialQuery.toLowerCase()))
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit - suggestions.size)
      .forEach(r => suggestions.add(r.item.label))
    
    return Array.from(suggestions).slice(0, limit)
  }

  /**
   * Clear recent items and analytics
   */
  clearHistory(): void {
    this.recentItems = []
    this.analytics = {
      totalSearches: 0,
      averageResultCount: 0,
      popularQueries: {},
      clickThroughRates: {},
      searchToNavigationTime: []
    }
  }
}

// ===== SINGLETON INSTANCE =====

export const navigationSearchEngine = new NavigationSearchEngine()

export default NavigationSearchEngine
