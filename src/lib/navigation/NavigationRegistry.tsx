/**
 * Navigation Registry
 * 
 * Central registry for all navigation variants and configurations.
 * Provides a plugin-like system for extending navigation functionality.
 * 
 * Features:
 * - Component registration and discovery
 * - Configuration validation
 * - Performance optimization through lazy loading
 * - Plugin system for custom navigation variants
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { lazy, ComponentType } from 'react'
import { NavigationConfig, NavigationFactoryProps } from './NavigationFactory'

// ===== TYPES =====

export interface NavigationVariantDefinition {
  id: string
  name: string
  description: string
  component: ComponentType<NavigationFactoryProps>
  defaultConfig: Partial<NavigationConfig>
  supportedFeatures: string[]
  responsive: boolean
  lazyLoad?: boolean
}

export interface NavigationPlugin {
  id: string
  name: string
  version: string
  variants: NavigationVariantDefinition[]
  hooks?: {
    beforeRender?: (props: NavigationFactoryProps) => NavigationFactoryProps
    afterRender?: (element: React.ReactElement) => React.ReactElement
  }
}

// ===== LAZY LOADED COMPONENTS =====

const LazyDesktopNavigation = lazy(() => 
  import('./NavigationFactory').then(module => ({ 
    default: module.DesktopSidebarNavigation 
  }))
)

const LazyMobileNavigation = lazy(() => 
  import('./NavigationFactory').then(module => ({ 
    default: module.MobileBottomNavigation 
  }))
)

const LazyTabletNavigation = lazy(() => 
  import('./NavigationFactory').then(module => ({ 
    default: module.TabletCompactNavigation 
  }))
)

// ===== CORE VARIANTS =====

export const CORE_NAVIGATION_VARIANTS: NavigationVariantDefinition[] = [
  {
    id: 'desktop-sidebar',
    name: 'Desktop Sidebar',
    description: 'Full-featured desktop sidebar navigation with search and quick settings',
    component: LazyDesktopNavigation,
    defaultConfig: {
      variant: 'desktop',
      showSearch: true,
      showQuickSettings: true,
      showBreadcrumbs: false,
      showLabels: true,
      compact: false
    },
    supportedFeatures: ['search', 'quickSettings', 'breadcrumbs', 'keyboard', 'categories'],
    responsive: false,
    lazyLoad: true
  },
  {
    id: 'mobile-bottom',
    name: 'Mobile Bottom Navigation',
    description: 'Touch-optimized bottom navigation for mobile devices',
    component: LazyMobileNavigation,
    defaultConfig: {
      variant: 'mobile',
      showSearch: false,
      showQuickSettings: false,
      showBreadcrumbs: false,
      showLabels: true,
      compact: false
    },
    supportedFeatures: ['touchTargets', 'badges', 'hapticFeedback'],
    responsive: false,
    lazyLoad: true
  },
  {
    id: 'tablet-compact',
    name: 'Tablet Compact',
    description: 'Compact navigation optimized for tablet devices',
    component: LazyTabletNavigation,
    defaultConfig: {
      variant: 'tablet',
      showSearch: false,
      showQuickSettings: false,
      showBreadcrumbs: false,
      showLabels: true,
      compact: true
    },
    supportedFeatures: ['compact', 'responsive', 'categories'],
    responsive: true,
    lazyLoad: true
  }
]

// ===== REGISTRY CLASS =====

class NavigationRegistry {
  private variants = new Map<string, NavigationVariantDefinition>()
  private plugins = new Map<string, NavigationPlugin>()

  constructor() {
    // Register core variants
    CORE_NAVIGATION_VARIANTS.forEach(variant => {
      this.registerVariant(variant)
    })
  }

  /**
   * Register a navigation variant
   */
  registerVariant(variant: NavigationVariantDefinition): void {
    this.variants.set(variant.id, variant)
  }

  /**
   * Register a navigation plugin
   */
  registerPlugin(plugin: NavigationPlugin): void {
    this.plugins.set(plugin.id, plugin)
    
    // Register plugin variants
    plugin.variants.forEach(variant => {
      this.registerVariant(variant)
    })
  }

  /**
   * Get a navigation variant by ID
   */
  getVariant(id: string): NavigationVariantDefinition | undefined {
    return this.variants.get(id)
  }

  /**
   * Get all registered variants
   */
  getAllVariants(): NavigationVariantDefinition[] {
    return Array.from(this.variants.values())
  }

  /**
   * Get variants by feature support
   */
  getVariantsByFeature(feature: string): NavigationVariantDefinition[] {
    return this.getAllVariants().filter(variant => 
      variant.supportedFeatures.includes(feature)
    )
  }

  /**
   * Get responsive variants
   */
  getResponsiveVariants(): NavigationVariantDefinition[] {
    return this.getAllVariants().filter(variant => variant.responsive)
  }

  /**
   * Create navigation component from variant ID
   */
  createNavigation(variantId: string, config?: Partial<NavigationConfig>) {
    const variant = this.getVariant(variantId)
    if (!variant) {
      throw new Error(`Navigation variant '${variantId}' not found`)
    }

    const mergedConfig = {
      ...variant.defaultConfig,
      ...config
    } as NavigationConfig

    // Apply plugin hooks
    const NavigationComponent: React.FC<Omit<NavigationFactoryProps, keyof NavigationConfig>> = (props) => {
      let finalProps = { ...mergedConfig, ...props }

      // Apply beforeRender hooks
      for (const plugin of this.plugins.values()) {
        if (plugin.hooks?.beforeRender) {
          finalProps = plugin.hooks.beforeRender(finalProps)
        }
      }

      let element = React.createElement(variant.component, finalProps)

      // Apply afterRender hooks
      for (const plugin of this.plugins.values()) {
        if (plugin.hooks?.afterRender) {
          element = plugin.hooks.afterRender(element)
        }
      }

      return element
    }

    NavigationComponent.displayName = `Navigation${variant.name.replace(/\s+/g, '')}`
    
    return NavigationComponent
  }

  /**
   * Validate navigation configuration
   */
  validateConfig(variantId: string, config: NavigationConfig): boolean {
    const variant = this.getVariant(variantId)
    if (!variant) return false

    // Check if requested features are supported
    const requestedFeatures = Object.keys(config).filter(key => 
      config[key as keyof NavigationConfig] === true
    )

    return requestedFeatures.every(feature => 
      variant.supportedFeatures.includes(feature)
    )
  }

  /**
   * Get optimal variant for device/context
   */
  getOptimalVariant(context: {
    isMobile?: boolean
    isTablet?: boolean
    isDesktop?: boolean
    hasTouch?: boolean
    screenWidth?: number
    features?: string[]
  }): NavigationVariantDefinition | undefined {
    const variants = this.getAllVariants()

    // Score variants based on context
    const scored = variants.map(variant => {
      let score = 0

      // Device type matching
      if (context.isMobile && variant.id.includes('mobile')) score += 10
      if (context.isTablet && variant.id.includes('tablet')) score += 10
      if (context.isDesktop && variant.id.includes('desktop')) score += 10

      // Feature matching
      if (context.features) {
        const matchingFeatures = context.features.filter(feature =>
          variant.supportedFeatures.includes(feature)
        )
        score += matchingFeatures.length * 2
      }

      // Responsive bonus
      if (variant.responsive) score += 1

      return { variant, score }
    })

    // Return highest scoring variant
    const best = scored.reduce((prev, current) => 
      current.score > prev.score ? current : prev
    )

    return best.score > 0 ? best.variant : undefined
  }
}

// ===== SINGLETON INSTANCE =====

export const navigationRegistry = new NavigationRegistry()

// ===== HOOKS =====

/**
 * Hook to get navigation variant
 */
export const useNavigationVariant = (variantId: string) => {
  return navigationRegistry.getVariant(variantId)
}

/**
 * Hook to get optimal navigation variant
 */
export const useOptimalNavigationVariant = (context: Parameters<NavigationRegistry['getOptimalVariant']>[0]) => {
  return navigationRegistry.getOptimalVariant(context)
}

/**
 * Hook to create navigation component
 */
export const useNavigationComponent = (variantId: string, config?: Partial<NavigationConfig>) => {
  return React.useMemo(() => {
    return navigationRegistry.createNavigation(variantId, config)
  }, [variantId, config])
}

// ===== EXPORTS =====

export default navigationRegistry
export type { NavigationVariantDefinition, NavigationPlugin }
