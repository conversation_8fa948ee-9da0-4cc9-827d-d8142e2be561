/**
 * Contextual Navigation Suggestions Engine
 * 
 * Intelligent navigation suggestions based on user behavior, current page context,
 * completion status, and behavioral analytics.
 * 
 * Features:
 * - User behavior tracking and analysis
 * - Context-aware suggestions based on current page
 * - Completion status-based recommendations
 * - Time-based suggestion patterns
 * - Personalized navigation flows
 * - A/B testing support for suggestion algorithms
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import { NavigationItem, NavigationCategory } from './NavigationProvider'

// ===== TYPES =====

export interface UserBehavior {
  userId?: string
  sessionId: string
  visitedPages: PageVisit[]
  navigationPatterns: NavigationPattern[]
  completionStatus: CompletionStatus
  preferences: UserPreferences
  timeSpent: Record<string, number> // page -> total time in ms
  lastActivity: number
}

export interface PageVisit {
  href: string
  title: string
  timestamp: number
  duration: number
  exitMethod: 'navigation' | 'close' | 'refresh' | 'back'
  referrer?: string
}

export interface NavigationPattern {
  sequence: string[] // array of hrefs in order
  frequency: number
  lastUsed: number
  avgDuration: number
  completionRate: number // 0-1, how often this pattern leads to task completion
}

export interface CompletionStatus {
  profileCompletion: number // 0-1
  onboardingSteps: Record<string, boolean>
  achievementProgress: Record<string, number>
  orderHistory: {
    totalOrders: number
    lastOrderDate?: number
    averageOrderValue: number
  }
  engagementLevel: 'new' | 'casual' | 'active' | 'power'
}

export interface UserPreferences {
  preferredCategories: string[]
  timeOfDayPatterns: Record<string, number> // hour -> activity score
  devicePreferences: 'mobile' | 'desktop' | 'tablet'
  featureUsage: Record<string, number>
}

export interface ContextualSuggestion {
  item: NavigationItem
  category: NavigationCategory
  score: number
  reason: SuggestionReason
  priority: 'low' | 'medium' | 'high' | 'urgent'
  timing: 'immediate' | 'soon' | 'later'
  metadata: {
    confidence: number
    expectedBenefit: number
    userSegment: string
    testVariant?: string
  }
}

export type SuggestionReason = 
  | 'completion_next_step'
  | 'behavior_pattern'
  | 'time_based'
  | 'context_related'
  | 'onboarding_flow'
  | 'engagement_boost'
  | 'feature_discovery'
  | 'task_continuation'

export interface SuggestionContext {
  currentPage: string
  currentCategory?: string
  timeOfDay: number
  dayOfWeek: number
  sessionDuration: number
  pagesVisitedInSession: number
  lastAction?: string
  userSegment: string
}

// ===== SUGGESTION ALGORITHMS =====

/**
 * Calculate completion-based suggestions
 */
function getCompletionSuggestions(
  behavior: UserBehavior,
  items: NavigationItem[],
  categories: NavigationCategory[]
): ContextualSuggestion[] {
  const suggestions: ContextualSuggestion[] = []
  const { completionStatus } = behavior
  
  // Profile completion suggestions
  if (completionStatus.profileCompletion < 0.8) {
    const profileItems = items.filter(item => 
      item.href.includes('/profile') && 
      !behavior.visitedPages.some(visit => visit.href === item.href)
    )
    
    profileItems.forEach(item => {
      const category = categories.find(cat => cat.id === item.categoryId)!
      suggestions.push({
        item,
        category,
        score: (1 - completionStatus.profileCompletion) * 0.9,
        reason: 'completion_next_step',
        priority: completionStatus.profileCompletion < 0.5 ? 'high' : 'medium',
        timing: 'immediate',
        metadata: {
          confidence: 0.85,
          expectedBenefit: 0.7,
          userSegment: completionStatus.engagementLevel
        }
      })
    })
  }
  
  // Onboarding flow suggestions
  const incompleteOnboarding = Object.entries(completionStatus.onboardingSteps)
    .filter(([_, completed]) => !completed)
    .map(([step]) => step)
  
  if (incompleteOnboarding.length > 0) {
    const onboardingItems = items.filter(item =>
      incompleteOnboarding.some(step => item.href.includes(step))
    )
    
    onboardingItems.forEach(item => {
      const category = categories.find(cat => cat.id === item.categoryId)!
      suggestions.push({
        item,
        category,
        score: 0.95,
        reason: 'onboarding_flow',
        priority: 'urgent',
        timing: 'immediate',
        metadata: {
          confidence: 0.9,
          expectedBenefit: 0.8,
          userSegment: 'new'
        }
      })
    })
  }
  
  return suggestions
}

/**
 * Calculate behavior pattern suggestions
 */
function getBehaviorPatternSuggestions(
  behavior: UserBehavior,
  items: NavigationItem[],
  categories: NavigationCategory[],
  context: SuggestionContext
): ContextualSuggestion[] {
  const suggestions: ContextualSuggestion[] = []
  
  // Find common navigation patterns
  const relevantPatterns = behavior.navigationPatterns
    .filter(pattern => pattern.frequency > 2 && pattern.completionRate > 0.6)
    .sort((a, b) => b.frequency * b.completionRate - a.frequency * a.completionRate)
  
  relevantPatterns.slice(0, 3).forEach(pattern => {
    const currentIndex = pattern.sequence.indexOf(context.currentPage)
    if (currentIndex >= 0 && currentIndex < pattern.sequence.length - 1) {
      const nextHref = pattern.sequence[currentIndex + 1]
      const nextItem = items.find(item => item.href === nextHref)
      
      if (nextItem) {
        const category = categories.find(cat => cat.id === nextItem.categoryId)!
        suggestions.push({
          item: nextItem,
          category,
          score: pattern.frequency * pattern.completionRate * 0.01,
          reason: 'behavior_pattern',
          priority: pattern.completionRate > 0.8 ? 'high' : 'medium',
          timing: 'soon',
          metadata: {
            confidence: Math.min(pattern.frequency * 0.1, 0.9),
            expectedBenefit: pattern.completionRate,
            userSegment: behavior.completionStatus.engagementLevel
          }
        })
      }
    }
  })
  
  return suggestions
}

/**
 * Calculate time-based suggestions
 */
function getTimeBasedSuggestions(
  behavior: UserBehavior,
  items: NavigationItem[],
  categories: NavigationCategory[],
  context: SuggestionContext
): ContextualSuggestion[] {
  const suggestions: ContextualSuggestion[] = []
  const { preferences } = behavior
  
  // Time of day patterns
  const currentHourActivity = preferences.timeOfDayPatterns[context.timeOfDay] || 0
  if (currentHourActivity > 0.7) {
    // Suggest items commonly used at this time
    const timeBasedItems = items.filter(item => {
      const itemUsage = preferences.featureUsage[item.href] || 0
      return itemUsage > 0.5 && !behavior.visitedPages.some(visit => 
        visit.href === item.href && Date.now() - visit.timestamp < 3600000 // 1 hour
      )
    })
    
    timeBasedItems.slice(0, 2).forEach(item => {
      const category = categories.find(cat => cat.id === item.categoryId)!
      suggestions.push({
        item,
        category,
        score: currentHourActivity * (preferences.featureUsage[item.href] || 0),
        reason: 'time_based',
        priority: 'medium',
        timing: 'immediate',
        metadata: {
          confidence: 0.6,
          expectedBenefit: 0.5,
          userSegment: behavior.completionStatus.engagementLevel
        }
      })
    })
  }
  
  return suggestions
}

/**
 * Calculate context-related suggestions
 */
function getContextRelatedSuggestions(
  behavior: UserBehavior,
  items: NavigationItem[],
  categories: NavigationCategory[],
  context: SuggestionContext
): ContextualSuggestion[] {
  const suggestions: ContextualSuggestion[] = []
  
  // Related items in same category
  if (context.currentCategory) {
    const relatedItems = items.filter(item => 
      item.categoryId === context.currentCategory &&
      item.href !== context.currentPage &&
      !behavior.visitedPages.some(visit => visit.href === item.href)
    )
    
    relatedItems.slice(0, 2).forEach(item => {
      const category = categories.find(cat => cat.id === item.categoryId)!
      suggestions.push({
        item,
        category,
        score: 0.6,
        reason: 'context_related',
        priority: 'low',
        timing: 'later',
        metadata: {
          confidence: 0.5,
          expectedBenefit: 0.4,
          userSegment: behavior.completionStatus.engagementLevel
        }
      })
    })
  }
  
  // Feature discovery for power users
  if (behavior.completionStatus.engagementLevel === 'power') {
    const unusedFeatures = items.filter(item => 
      !preferences.featureUsage[item.href] &&
      item.keywords?.includes('advanced')
    )
    
    unusedFeatures.slice(0, 1).forEach(item => {
      const category = categories.find(cat => cat.id === item.categoryId)!
      suggestions.push({
        item,
        category,
        score: 0.7,
        reason: 'feature_discovery',
        priority: 'medium',
        timing: 'later',
        metadata: {
          confidence: 0.6,
          expectedBenefit: 0.6,
          userSegment: 'power'
        }
      })
    })
  }
  
  return suggestions
}

// ===== MAIN SUGGESTION ENGINE =====

export class ContextualSuggestionsEngine {
  private userBehavior: Map<string, UserBehavior> = new Map()
  private suggestionCache: Map<string, { suggestions: ContextualSuggestion[]; timestamp: number }> = new Map()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  /**
   * Generate contextual suggestions for a user
   */
  generateSuggestions(
    sessionId: string,
    items: NavigationItem[],
    categories: NavigationCategory[],
    context: SuggestionContext
  ): ContextualSuggestion[] {
    const cacheKey = `${sessionId}-${context.currentPage}-${context.timeOfDay}`
    
    // Check cache first
    const cached = this.suggestionCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.suggestions
    }
    
    const behavior = this.userBehavior.get(sessionId)
    if (!behavior) {
      return this.getDefaultSuggestions(items, categories, context)
    }
    
    // Generate suggestions using different algorithms
    const allSuggestions: ContextualSuggestion[] = [
      ...getCompletionSuggestions(behavior, items, categories),
      ...getBehaviorPatternSuggestions(behavior, items, categories, context),
      ...getTimeBasedSuggestions(behavior, items, categories, context),
      ...getContextRelatedSuggestions(behavior, items, categories, context)
    ]
    
    // Deduplicate and rank suggestions
    const uniqueSuggestions = this.deduplicateAndRank(allSuggestions)
    
    // Cache results
    this.suggestionCache.set(cacheKey, {
      suggestions: uniqueSuggestions,
      timestamp: Date.now()
    })
    
    return uniqueSuggestions
  }

  /**
   * Track user behavior for future suggestions
   */
  trackBehavior(sessionId: string, event: {
    type: 'page_visit' | 'navigation' | 'completion' | 'preference_change'
    data: any
  }): void {
    let behavior = this.userBehavior.get(sessionId)
    
    if (!behavior) {
      behavior = this.initializeUserBehavior(sessionId)
      this.userBehavior.set(sessionId, behavior)
    }
    
    switch (event.type) {
      case 'page_visit':
        this.trackPageVisit(behavior, event.data)
        break
      case 'navigation':
        this.trackNavigation(behavior, event.data)
        break
      case 'completion':
        this.trackCompletion(behavior, event.data)
        break
      case 'preference_change':
        this.trackPreferenceChange(behavior, event.data)
        break
    }
    
    behavior.lastActivity = Date.now()
  }

  /**
   * Get default suggestions for new users
   */
  private getDefaultSuggestions(
    items: NavigationItem[],
    categories: NavigationCategory[],
    context: SuggestionContext
  ): ContextualSuggestion[] {
    // Default onboarding flow
    const onboardingItems = items.filter(item => 
      item.href.includes('/profile') || 
      item.href.includes('/getting-started') ||
      item.href.includes('/tutorial')
    )
    
    return onboardingItems.slice(0, 3).map(item => {
      const category = categories.find(cat => cat.id === item.categoryId)!
      return {
        item,
        category,
        score: 0.8,
        reason: 'onboarding_flow',
        priority: 'high' as const,
        timing: 'immediate' as const,
        metadata: {
          confidence: 0.7,
          expectedBenefit: 0.6,
          userSegment: 'new'
        }
      }
    })
  }

  /**
   * Initialize user behavior tracking
   */
  private initializeUserBehavior(sessionId: string): UserBehavior {
    return {
      sessionId,
      visitedPages: [],
      navigationPatterns: [],
      completionStatus: {
        profileCompletion: 0,
        onboardingSteps: {},
        achievementProgress: {},
        orderHistory: {
          totalOrders: 0,
          averageOrderValue: 0
        },
        engagementLevel: 'new'
      },
      preferences: {
        preferredCategories: [],
        timeOfDayPatterns: {},
        devicePreferences: 'desktop',
        featureUsage: {}
      },
      timeSpent: {},
      lastActivity: Date.now()
    }
  }

  /**
   * Deduplicate and rank suggestions
   */
  private deduplicateAndRank(suggestions: ContextualSuggestion[]): ContextualSuggestion[] {
    const seen = new Set<string>()
    const unique = suggestions.filter(suggestion => {
      if (seen.has(suggestion.item.id)) return false
      seen.add(suggestion.item.id)
      return true
    })
    
    return unique
      .sort((a, b) => {
        // Sort by priority first, then score
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority]
        if (priorityDiff !== 0) return priorityDiff
        
        return b.score - a.score
      })
      .slice(0, 5) // Limit to top 5 suggestions
  }

  /**
   * Track page visit behavior
   */
  private trackPageVisit(behavior: UserBehavior, data: PageVisit): void {
    behavior.visitedPages.push(data)
    behavior.timeSpent[data.href] = (behavior.timeSpent[data.href] || 0) + data.duration
    
    // Keep only last 50 visits
    if (behavior.visitedPages.length > 50) {
      behavior.visitedPages = behavior.visitedPages.slice(-50)
    }
  }

  /**
   * Track navigation patterns
   */
  private trackNavigation(behavior: UserBehavior, data: { from: string; to: string }): void {
    // Update navigation patterns logic here
    // This would analyze sequences of navigation to build patterns
  }

  /**
   * Track completion events
   */
  private trackCompletion(behavior: UserBehavior, data: { type: string; value: any }): void {
    switch (data.type) {
      case 'profile':
        behavior.completionStatus.profileCompletion = data.value
        break
      case 'onboarding':
        behavior.completionStatus.onboardingSteps[data.value.step] = data.value.completed
        break
    }
  }

  /**
   * Track preference changes
   */
  private trackPreferenceChange(behavior: UserBehavior, data: { key: string; value: any }): void {
    // Update user preferences based on their actions
  }
}

// ===== SINGLETON INSTANCE =====

export const contextualSuggestionsEngine = new ContextualSuggestionsEngine()

export default ContextualSuggestionsEngine
