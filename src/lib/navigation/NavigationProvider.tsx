/**
 * Navigation Provider Context
 * 
 * Centralized state management for all navigation components.
 * Provides shared state, actions, and utilities for consistent navigation behavior.
 * 
 * Features:
 * - Unified state management across all navigation variants
 * - Search functionality with fuzzy matching
 * - Recent navigation tracking
 * - Keyboard shortcuts management
 * - Badge and notification state
 * - Responsive navigation detection
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { createContext, useContext, useReducer, useEffect, useCallback, useMemo } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { UserProfile } from '@/types/profile'
import { NAVIGATION_CONSTANTS } from './constants'
import { NavigationSearchEngine, navigationSearchEngine, SearchResult } from './SearchEngine'
import {
  ContextualSuggestionsEngine,
  contextualSuggestionsEngine,
  ContextualSuggestion,
  SuggestionContext
} from './ContextualSuggestions'

// ===== TYPES =====

export interface NavigationItem {
  id: string
  label: string
  href: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  description: string
  category: string
  priority: number
  badge?: {
    count: number | string
    type: 'notification' | 'count' | 'info' | 'warning'
  }
  keywords?: string[]
  requiresAuth?: boolean
  isNew?: boolean
  isPopular?: boolean
}

export interface NavigationCategory {
  id: string
  label: string
  shortLabel: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  items: NavigationItem[]
  priority: 'high' | 'medium' | 'low'
  badge?: {
    count: number | string
    type: 'notification' | 'count' | 'info' | 'warning'
  }
}

export interface RecentVisit {
  href: string
  timestamp: number
  count: number
  title: string
}

export interface NavigationState {
  // Search state
  searchQuery: string
  showSearch: boolean
  searchResults: SearchResult[]
  searchEngine: NavigationSearchEngine
  searchSuggestions: string[]
  selectedSearchIndex: number

  // Contextual suggestions state
  contextualSuggestions: ContextualSuggestion[]
  suggestionsEngine: ContextualSuggestionsEngine
  showSuggestions: boolean
  suggestionContext: SuggestionContext | null

  // Navigation state
  activeCategory: string
  recentlyVisited: RecentVisit[]
  keyboardNavIndex: number

  // UI state
  isMobile: boolean
  isTablet: boolean
  showQuickSettings: boolean
  showBreadcrumbs: boolean

  // Data state
  categories: NavigationCategory[]
  allItems: NavigationItem[]
  loading: boolean
}

export type NavigationAction =
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'TOGGLE_SEARCH'; payload?: boolean }
  | { type: 'SET_SEARCH_RESULTS'; payload: SearchResult[] }
  | { type: 'SET_SEARCH_SUGGESTIONS'; payload: string[] }
  | { type: 'SET_SELECTED_SEARCH_INDEX'; payload: number }
  | { type: 'PERFORM_SEARCH'; payload: { query: string; options?: any } }
  | { type: 'SET_CONTEXTUAL_SUGGESTIONS'; payload: ContextualSuggestion[] }
  | { type: 'TOGGLE_SUGGESTIONS'; payload?: boolean }
  | { type: 'UPDATE_SUGGESTION_CONTEXT'; payload: SuggestionContext }
  | { type: 'GENERATE_SUGGESTIONS'; payload: { sessionId: string; context: SuggestionContext } }
  | { type: 'SET_ACTIVE_CATEGORY'; payload: string }
  | { type: 'ADD_RECENT_VISIT'; payload: { href: string; title: string } }
  | { type: 'SET_KEYBOARD_NAV_INDEX'; payload: number }
  | { type: 'SET_RESPONSIVE_STATE'; payload: { isMobile: boolean; isTablet: boolean } }
  | { type: 'TOGGLE_QUICK_SETTINGS'; payload?: boolean }
  | { type: 'TOGGLE_BREADCRUMBS'; payload?: boolean }
  | { type: 'SET_CATEGORIES'; payload: NavigationCategory[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'RESET_SEARCH' }

export interface NavigationContextValue {
  state: NavigationState
  dispatch: React.Dispatch<NavigationAction>

  // Actions
  setSearchQuery: (query: string) => void
  toggleSearch: (show?: boolean) => void
  performSearch: (query: string, options?: any) => void
  setSelectedSearchIndex: (index: number) => void
  toggleSuggestions: (show?: boolean) => void
  generateSuggestions: (sessionId: string, context: SuggestionContext) => void
  setActiveCategory: (category: string) => void
  addRecentVisit: (href: string, title: string) => void
  resetSearch: () => void

  // Utilities
  searchItems: (query: string) => SearchResult[]
  getItemsByCategory: (categoryId: string) => NavigationItem[]
  getSuggestions: (query: string) => string[]
  trackNavigation: (item: NavigationItem, searchQuery?: string) => void
  trackBehavior: (event: { type: string; data: any }) => void
  isActive: (href: string) => boolean
  getCategoryBadge: (categoryId: string) => { count: number | string; type: string } | undefined
  
  // Navigation
  navigate: (href: string, title?: string) => void
}

// ===== REDUCER =====

const initialState: NavigationState = {
  searchQuery: '',
  showSearch: false,
  searchResults: [],
  searchEngine: navigationSearchEngine,
  searchSuggestions: [],
  selectedSearchIndex: 0,
  contextualSuggestions: [],
  suggestionsEngine: contextualSuggestionsEngine,
  showSuggestions: true,
  suggestionContext: null,
  activeCategory: '',
  recentlyVisited: [],
  keyboardNavIndex: -1,
  isMobile: false,
  isTablet: false,
  showQuickSettings: false,
  showBreadcrumbs: false,
  categories: [],
  allItems: [],
  loading: true
}

function navigationReducer(state: NavigationState, action: NavigationAction): NavigationState {
  switch (action.type) {
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload }
      
    case 'TOGGLE_SEARCH':
      return { 
        ...state, 
        showSearch: action.payload !== undefined ? action.payload : !state.showSearch,
        searchQuery: action.payload === false ? '' : state.searchQuery
      }
      
    case 'SET_SEARCH_RESULTS':
      return { ...state, searchResults: action.payload }

    case 'SET_SEARCH_SUGGESTIONS':
      return { ...state, searchSuggestions: action.payload }

    case 'SET_SELECTED_SEARCH_INDEX':
      return { ...state, selectedSearchIndex: action.payload }

    case 'PERFORM_SEARCH': {
      const { query, options = {} } = action.payload
      const results = state.searchEngine.search(state.allItems, state.categories, {
        query,
        maxResults: 8,
        includeRecent: true,
        fuzzyThreshold: 0.3,
        boostRecent: true,
        contextualBoost: true,
        ...options
      })
      return {
        ...state,
        searchResults: results,
        selectedSearchIndex: 0
      }
    }

    case 'SET_CONTEXTUAL_SUGGESTIONS':
      return { ...state, contextualSuggestions: action.payload }

    case 'TOGGLE_SUGGESTIONS':
      return {
        ...state,
        showSuggestions: action.payload !== undefined ? action.payload : !state.showSuggestions
      }

    case 'UPDATE_SUGGESTION_CONTEXT':
      return { ...state, suggestionContext: action.payload }

    case 'GENERATE_SUGGESTIONS': {
      const { sessionId, context } = action.payload
      const suggestions = state.suggestionsEngine.generateSuggestions(
        sessionId,
        state.allItems,
        state.categories,
        context
      )
      return {
        ...state,
        contextualSuggestions: suggestions,
        suggestionContext: context
      }
    }

    case 'SET_ACTIVE_CATEGORY':
      return { ...state, activeCategory: action.payload }
      
    case 'ADD_RECENT_VISIT': {
      const existing = state.recentlyVisited.find(v => v.href === action.payload.href)
      const updated = existing
        ? state.recentlyVisited.map(v => 
            v.href === action.payload.href 
              ? { ...v, count: v.count + 1, timestamp: Date.now() }
              : v
          )
        : [
            { ...action.payload, timestamp: Date.now(), count: 1 },
            ...state.recentlyVisited.slice(0, 9) // Keep last 10
          ]
      
      return { ...state, recentlyVisited: updated }
    }
    
    case 'SET_KEYBOARD_NAV_INDEX':
      return { ...state, keyboardNavIndex: action.payload }
      
    case 'SET_RESPONSIVE_STATE':
      return { ...state, ...action.payload }
      
    case 'TOGGLE_QUICK_SETTINGS':
      return { 
        ...state, 
        showQuickSettings: action.payload !== undefined ? action.payload : !state.showQuickSettings 
      }
      
    case 'TOGGLE_BREADCRUMBS':
      return { 
        ...state, 
        showBreadcrumbs: action.payload !== undefined ? action.payload : !state.showBreadcrumbs 
      }
      
    case 'SET_CATEGORIES':
      return { 
        ...state, 
        categories: action.payload,
        allItems: action.payload.flatMap(cat => cat.items)
      }
      
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
      
    case 'RESET_SEARCH':
      return {
        ...state,
        searchQuery: '',
        showSearch: false,
        searchResults: [],
        searchSuggestions: [],
        selectedSearchIndex: 0,
        keyboardNavIndex: -1
      }
      
    default:
      return state
  }
}

// ===== CONTEXT =====

const NavigationContext = createContext<NavigationContextValue | undefined>(undefined)

// ===== PROVIDER =====

interface NavigationProviderProps {
  children: React.ReactNode
  profile: UserProfile | null
  wishlistItemCount: number
  initialCategories?: NavigationCategory[]
}

export const NavigationProvider: React.FC<NavigationProviderProps> = ({
  children,
  profile,
  wishlistItemCount,
  initialCategories = []
}) => {
  const [state, dispatch] = useReducer(navigationReducer, {
    ...initialState,
    categories: initialCategories
  })
  
  const pathname = usePathname()
  const router = useRouter()

  // Actions
  const setSearchQuery = useCallback((query: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query })
  }, [])

  const toggleSearch = useCallback((show?: boolean) => {
    dispatch({ type: 'TOGGLE_SEARCH', payload: show })
  }, [])

  const setActiveCategory = useCallback((category: string) => {
    dispatch({ type: 'SET_ACTIVE_CATEGORY', payload: category })
  }, [])

  const addRecentVisit = useCallback((href: string, title: string) => {
    dispatch({ type: 'ADD_RECENT_VISIT', payload: { href, title } })
  }, [])

  const resetSearch = useCallback(() => {
    dispatch({ type: 'RESET_SEARCH' })
  }, [])

  const performSearch = useCallback((query: string, options?: any) => {
    dispatch({ type: 'PERFORM_SEARCH', payload: { query, options } })
  }, [])

  const setSelectedSearchIndex = useCallback((index: number) => {
    dispatch({ type: 'SET_SELECTED_SEARCH_INDEX', payload: index })
  }, [])

  const toggleSuggestions = useCallback((show?: boolean) => {
    dispatch({ type: 'TOGGLE_SUGGESTIONS', payload: show })
  }, [])

  const generateSuggestions = useCallback((sessionId: string, context: SuggestionContext) => {
    dispatch({ type: 'GENERATE_SUGGESTIONS', payload: { sessionId, context } })
  }, [])

  // Utilities
  const searchItems = useCallback((query: string): SearchResult[] => {
    if (!query.trim()) return []

    return state.searchEngine.search(state.allItems, state.categories, {
      query,
      maxResults: 8,
      includeRecent: true,
      fuzzyThreshold: 0.3,
      boostRecent: true,
      contextualBoost: true
    })
  }, [state.allItems, state.categories, state.searchEngine])

  const getSuggestions = useCallback((query: string): string[] => {
    return state.searchEngine.getSuggestions(query, 5)
  }, [state.searchEngine])

  const trackNavigation = useCallback((item: NavigationItem, searchQuery?: string) => {
    state.searchEngine.trackNavigation(item, searchQuery)
  }, [state.searchEngine])

  const trackBehavior = useCallback((event: { type: string; data: any }) => {
    // Generate a session ID if not available
    const sessionId = 'session-' + Date.now()
    state.suggestionsEngine.trackBehavior(sessionId, event)
  }, [state.suggestionsEngine])

  const getItemsByCategory = useCallback((categoryId: string): NavigationItem[] => {
    const category = state.categories.find(cat => cat.id === categoryId)
    return category?.items || []
  }, [state.categories])

  const isActive = useCallback((href: string): boolean => {
    return pathname === href
  }, [pathname])

  const getCategoryBadge = useCallback((categoryId: string) => {
    const category = state.categories.find(cat => cat.id === categoryId)
    return category?.badge
  }, [state.categories])

  const navigate = useCallback((href: string, title?: string) => {
    if (title) {
      addRecentVisit(href, title)
    }
    router.push(href)
  }, [router, addRecentVisit])

  // Load recent visits from localStorage
  useEffect(() => {
    const stored = localStorage.getItem('recentNavigationVisits')
    if (stored) {
      try {
        const parsed = JSON.parse(stored)
        parsed.forEach((visit: RecentVisit) => {
          dispatch({ type: 'ADD_RECENT_VISIT', payload: { href: visit.href, title: visit.title } })
        })
      } catch (error) {
        console.warn('Failed to parse recent visits:', error)
      }
    }
  }, [])

  // Save recent visits to localStorage
  useEffect(() => {
    localStorage.setItem('recentNavigationVisits', JSON.stringify(state.recentlyVisited))
  }, [state.recentlyVisited])

  // Update categories when profile or wishlist changes
  useEffect(() => {
    // Generate categories from constants with dynamic data
    const categories = Object.values(NAVIGATION_CONSTANTS.CATEGORIES).map(category => ({
      ...category,
      items: Object.values(NAVIGATION_CONSTANTS.PROFILE[category.id as keyof typeof NAVIGATION_CONSTANTS.PROFILE] || {}),
      badge: category.id === 'orders' && wishlistItemCount > 0
        ? { count: wishlistItemCount, type: 'notification' as const }
        : undefined
    }))

    dispatch({ type: 'SET_CATEGORIES', payload: categories })
    dispatch({ type: 'SET_LOADING', payload: false })
  }, [profile, wishlistItemCount])

  // Update active category based on current path
  useEffect(() => {
    const activeCategory = state.categories.find(cat =>
      cat.items.some(item => pathname.startsWith(item.href))
    )?.id || ''

    if (activeCategory !== state.activeCategory) {
      dispatch({ type: 'SET_ACTIVE_CATEGORY', payload: activeCategory })
    }
  }, [pathname, state.categories, state.activeCategory])

  // Handle responsive state
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < 768
      const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024
      dispatch({
        type: 'SET_RESPONSIVE_STATE',
        payload: { isMobile, isTablet }
      })
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Update search results when query changes
  useEffect(() => {
    if (state.searchQuery) {
      const results = searchItems(state.searchQuery)
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: results })
    } else {
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] })
    }
  }, [state.searchQuery, searchItems])

  // Context value
  const contextValue: NavigationContextValue = useMemo(() => ({
    state,
    dispatch,
    setSearchQuery,
    toggleSearch,
    performSearch,
    setSelectedSearchIndex,
    toggleSuggestions,
    generateSuggestions,
    setActiveCategory,
    addRecentVisit,
    resetSearch,
    searchItems,
    getItemsByCategory,
    getSuggestions,
    trackNavigation,
    trackBehavior,
    isActive,
    getCategoryBadge,
    navigate
  }), [
    state,
    setSearchQuery,
    toggleSearch,
    performSearch,
    setSelectedSearchIndex,
    toggleSuggestions,
    generateSuggestions,
    setActiveCategory,
    addRecentVisit,
    resetSearch,
    searchItems,
    getItemsByCategory,
    getSuggestions,
    trackNavigation,
    trackBehavior,
    isActive,
    getCategoryBadge,
    navigate
  ])

  return (
    <NavigationContext.Provider value={contextValue}>
      {children}
    </NavigationContext.Provider>
  )
}

// ===== HOOK =====

export const useNavigation = (): NavigationContextValue => {
  const context = useContext(NavigationContext)
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider')
  }
  return context
}

export default NavigationProvider
