/**
 * Route Parameter Validation Utilities
 * 
 * Comprehensive validation utilities for dynamic route parameters
 * with enhanced error handling and security checks.
 * 
 * Features:
 * - Parameter format validation
 * - Security sanitization
 * - Type-safe validation
 * - Detailed error reporting
 * - Performance optimization
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { notFound } from 'next/navigation'

// ===== TYPES =====

export interface RouteValidationResult {
  isValid: boolean
  sanitizedValue?: string
  error?: RouteValidationError
}

export interface RouteValidationError {
  code: string
  message: string
  field: string
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export interface ValidationOptions {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  allowedChars?: string
  sanitize?: boolean
  caseSensitive?: boolean
}

// ===== VALIDATION PATTERNS =====

export const VALIDATION_PATTERNS = {
  // Product ID: alphanumeric, hyphens, underscores (3-50 chars)
  PRODUCT_ID: /^[a-zA-Z0-9_-]{3,50}$/,
  
  // Blog slug: lowercase, hyphens, numbers (3-100 chars)
  BLOG_SLUG: /^[a-z0-9-]{3,100}$/,
  
  // Challenge ID: alphanumeric, hyphens (3-30 chars)
  CHALLENGE_ID: /^[a-zA-Z0-9-]{3,30}$/,
  
  // Order ID: alphanumeric, hyphens, underscores (10-50 chars)
  ORDER_ID: /^[a-zA-Z0-9_-]{10,50}$/,
  
  // User ID: Firebase UID format (28 chars)
  USER_ID: /^[a-zA-Z0-9]{28}$/,
  
  // Generic ID: alphanumeric, hyphens, underscores (1-100 chars)
  GENERIC_ID: /^[a-zA-Z0-9_-]{1,100}$/,
  
  // Numeric ID: positive integers only
  NUMERIC_ID: /^\d+$/,
  
  // UUID format
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
} as const

// ===== VALIDATION FUNCTIONS =====

/**
 * Validates a route parameter with comprehensive checks
 */
export function validateRouteParameter(
  value: string | undefined | null,
  fieldName: string,
  options: ValidationOptions = {}
): RouteValidationResult {
  const {
    required = true,
    minLength = 1,
    maxLength = 100,
    pattern,
    allowedChars,
    sanitize = true,
    caseSensitive = true
  } = options

  // Check if value exists
  if (!value || typeof value !== 'string') {
    if (required) {
      return {
        isValid: false,
        error: {
          code: 'MISSING_PARAMETER',
          message: `${fieldName} parameter is required`,
          field: fieldName,
          severity: 'high'
        }
      }
    }
    return { isValid: true }
  }

  // Sanitize value
  let sanitizedValue = sanitize ? sanitizeParameter(value) : value
  
  // Case sensitivity
  if (!caseSensitive) {
    sanitizedValue = sanitizedValue.toLowerCase()
  }

  // Length validation
  if (sanitizedValue.length < minLength) {
    return {
      isValid: false,
      error: {
        code: 'PARAMETER_TOO_SHORT',
        message: `${fieldName} must be at least ${minLength} characters`,
        field: fieldName,
        severity: 'medium'
      }
    }
  }

  if (sanitizedValue.length > maxLength) {
    return {
      isValid: false,
      error: {
        code: 'PARAMETER_TOO_LONG',
        message: `${fieldName} must be no more than ${maxLength} characters`,
        field: fieldName,
        severity: 'medium'
      }
    }
  }

  // Pattern validation
  if (pattern && !pattern.test(sanitizedValue)) {
    return {
      isValid: false,
      error: {
        code: 'INVALID_PARAMETER_FORMAT',
        message: `${fieldName} contains invalid characters or format`,
        field: fieldName,
        severity: 'high'
      }
    }
  }

  // Allowed characters validation
  if (allowedChars) {
    const allowedPattern = new RegExp(`^[${allowedChars}]+$`)
    if (!allowedPattern.test(sanitizedValue)) {
      return {
        isValid: false,
        error: {
          code: 'INVALID_CHARACTERS',
          message: `${fieldName} contains disallowed characters`,
          field: fieldName,
          severity: 'high'
        }
      }
    }
  }

  return {
    isValid: true,
    sanitizedValue
  }
}

/**
 * Sanitizes a parameter value by removing potentially dangerous characters
 */
export function sanitizeParameter(value: string): string {
  return value
    .trim()
    .replace(/[<>'"&]/g, '') // Remove HTML/script injection chars
    .replace(/\.\./g, '') // Remove path traversal
    .replace(/[^\w\-_.]/g, '') // Keep only word chars, hyphens, underscores, dots
}

/**
 * Validates product ID parameter
 */
export function validateProductId(id: string | undefined): RouteValidationResult {
  return validateRouteParameter(id, 'Product ID', {
    pattern: VALIDATION_PATTERNS.PRODUCT_ID,
    minLength: 3,
    maxLength: 50
  })
}

/**
 * Validates blog slug parameter
 */
export function validateBlogSlug(slug: string | undefined): RouteValidationResult {
  return validateRouteParameter(slug, 'Blog Slug', {
    pattern: VALIDATION_PATTERNS.BLOG_SLUG,
    minLength: 3,
    maxLength: 100,
    caseSensitive: false
  })
}

/**
 * Validates challenge ID parameter
 */
export function validateChallengeId(id: string | undefined): RouteValidationResult {
  return validateRouteParameter(id, 'Challenge ID', {
    pattern: VALIDATION_PATTERNS.CHALLENGE_ID,
    minLength: 3,
    maxLength: 30
  })
}

/**
 * Validates order ID parameter
 */
export function validateOrderId(id: string | undefined): RouteValidationResult {
  return validateRouteParameter(id, 'Order ID', {
    pattern: VALIDATION_PATTERNS.ORDER_ID,
    minLength: 10,
    maxLength: 50
  })
}

/**
 * Validates user ID parameter (Firebase UID format)
 */
export function validateUserId(id: string | undefined): RouteValidationResult {
  return validateRouteParameter(id, 'User ID', {
    pattern: VALIDATION_PATTERNS.USER_ID,
    minLength: 28,
    maxLength: 28
  })
}

/**
 * Validates generic ID parameter
 */
export function validateGenericId(id: string | undefined): RouteValidationResult {
  return validateRouteParameter(id, 'ID', {
    pattern: VALIDATION_PATTERNS.GENERIC_ID,
    minLength: 1,
    maxLength: 100
  })
}

// ===== ROUTE-SPECIFIC VALIDATORS =====

/**
 * Validates and handles route parameter with automatic 404 on failure
 */
export function validateRouteParamOrNotFound(
  value: string | undefined,
  validator: (value: string | undefined) => RouteValidationResult
): string {
  const result = validator(value)
  
  if (!result.isValid) {
    // Log validation error for debugging
    console.warn('Route validation failed:', result.error)
    notFound()
  }
  
  return result.sanitizedValue || value!
}

/**
 * Validates multiple route parameters
 */
export function validateMultipleParams(
  params: Record<string, string | undefined>,
  validators: Record<string, (value: string | undefined) => RouteValidationResult>
): Record<string, string> {
  const validatedParams: Record<string, string> = {}
  
  for (const [key, validator] of Object.entries(validators)) {
    const result = validator(params[key])
    
    if (!result.isValid) {
      console.warn(`Route validation failed for ${key}:`, result.error)
      notFound()
    }
    
    validatedParams[key] = result.sanitizedValue || params[key]!
  }
  
  return validatedParams
}

// ===== ERROR HANDLING =====

/**
 * Creates a standardized route validation error
 */
export function createRouteValidationError(
  code: string,
  message: string,
  field: string,
  severity: RouteValidationError['severity'] = 'medium'
): RouteValidationError {
  return {
    code,
    message,
    field,
    severity
  }
}

/**
 * Logs route validation errors with context
 */
export function logRouteValidationError(
  error: RouteValidationError,
  context: {
    route?: string
    value?: string
    userAgent?: string
    timestamp?: Date
  } = {}
): void {
  const logData = {
    ...error,
    context: {
      route: context.route || 'unknown',
      value: context.value ? '[REDACTED]' : undefined,
      userAgent: context.userAgent,
      timestamp: context.timestamp || new Date()
    }
  }
  
  // Log based on severity
  switch (error.severity) {
    case 'critical':
      console.error('Critical route validation error:', logData)
      break
    case 'high':
      console.error('High severity route validation error:', logData)
      break
    case 'medium':
      console.warn('Route validation warning:', logData)
      break
    case 'low':
      console.info('Route validation info:', logData)
      break
  }
}

export default {
  validateRouteParameter,
  validateProductId,
  validateBlogSlug,
  validateChallengeId,
  validateOrderId,
  validateUserId,
  validateGenericId,
  validateRouteParamOrNotFound,
  validateMultipleParams,
  sanitizeParameter,
  VALIDATION_PATTERNS
}
