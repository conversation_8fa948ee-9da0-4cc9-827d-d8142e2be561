'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { CreditCard, Plus, Edit, Trash2, Shield, Check, Star } from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'

// Mock payment methods data
const mockPaymentMethods = [
  {
    id: 'pm_001',
    type: 'card',
    brand: 'visa',
    last4: '4242',
    expiryMonth: 12,
    expiryYear: 2025,
    isDefault: true,
    nickname: 'Main Card'
  },
  {
    id: 'pm_002',
    type: 'card',
    brand: 'mastercard',
    last4: '8888',
    expiryMonth: 8,
    expiryYear: 2026,
    isDefault: false,
    nickname: 'Backup Card'
  },
  {
    id: 'pm_003',
    type: 'paypal',
    email: '<EMAIL>',
    isDefault: false,
    nickname: 'PayPal Account'
  }
]

export default function PaymentsPage() {
  const { user } = useUser()
  const [paymentMethods, setPaymentMethods] = useState(mockPaymentMethods)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingMethod, setEditingMethod] = useState<string | null>(null)

  if (!user) {
    return (
      <ProfileLayout>
        <div className="text-center py-12">
          <p className="text-gray-400">Please log in to manage your payment methods.</p>
        </div>
      </ProfileLayout>
    )
  }

  const setAsDefault = (id: string) => {
    setPaymentMethods(prev => 
      prev.map(method => ({
        ...method,
        isDefault: method.id === id
      }))
    )
  }

  const deletePaymentMethod = (id: string) => {
    setPaymentMethods(prev => prev.filter(method => method.id !== id))
  }

  const getCardIcon = (brand: string) => {
    const icons = {
      visa: '💳',
      mastercard: '💳',
      amex: '💳',
      discover: '💳'
    }
    return icons[brand as keyof typeof icons] || '💳'
  }

  const formatCardNumber = (last4: string) => `•••• •••• •••• ${last4}`

  return (
    <ProfileLayout>
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <CreditCard className="text-accent-400" size={28} />
              <div>
                <h1 className="text-2xl font-bold text-white">Payment Methods</h1>
                <p className="text-gray-400">Manage your payment methods and billing information</p>
              </div>
            </div>
            
            <button
              onClick={() => setShowAddForm(true)}
              className="flex items-center space-x-2 bg-accent-600 hover:bg-accent-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Plus size={18} />
              <span>Add Payment Method</span>
            </button>
          </div>
        </motion.div>

        {/* Security Notice */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-blue-900/20 border border-blue-700 rounded-lg p-4"
        >
          <div className="flex items-center space-x-3">
            <Shield className="text-blue-400" size={20} />
            <div>
              <h3 className="text-blue-400 font-semibold">Secure Payment Processing</h3>
              <p className="text-blue-300 text-sm">
                Your payment information is encrypted and securely processed by our payment partners. We never store your full card details.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Payment Methods List */}
        <div className="space-y-4">
          {paymentMethods.map((method, index) => (
            <motion.div
              key={method.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 + index * 0.1 }}
              className="bg-gray-800 rounded-lg border border-gray-700 p-6 hover:border-gray-600 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {method.type === 'card' ? (
                    <div className="w-12 h-8 bg-gradient-to-r from-gray-700 to-gray-600 rounded flex items-center justify-center text-lg">
                      {getCardIcon(method.brand ?? '')}
                    </div>
                  ) : (
                    <div className="w-12 h-8 bg-blue-600 rounded flex items-center justify-center text-white text-xs font-bold">
                      PP
                    </div>
                  )}
                  
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="text-white font-semibold">
                        {method.type === 'card' 
                          ? (method.brand
                              ? `${method.brand.charAt(0).toUpperCase() + method.brand.slice(1)} ${formatCardNumber(method.last4)}`
                              : formatCardNumber(method.last4 ?? ''))
                          : 'PayPal Account'
                        }
                      </h3>
                      {method.isDefault && (
                        <span className="flex items-center space-x-1 bg-accent-600 text-white px-2 py-1 rounded text-xs">
                          <Star size={12} />
                          <span>Default</span>
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-400 text-sm">
                      {method.type === 'card' 
                        ? `Expires ${method.expiryMonth}/${method.expiryYear}`
                        : method.email
                      }
                    </p>
                    
                    {method.nickname && (
                      <p className="text-gray-500 text-xs">{method.nickname}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {!method.isDefault && (
                    <button
                      onClick={() => setAsDefault(method.id)}
                      className="text-gray-400 hover:text-accent-400 transition-colors text-sm"
                    >
                      Set as default
                    </button>
                  )}
                  
                  <button
                    onClick={() => setEditingMethod(method.id)}
                    className="p-2 text-gray-400 hover:text-white transition-colors"
                    title="Edit payment method"
                  >
                    <Edit size={16} />
                  </button>
                  
                  <button
                    onClick={() => deletePaymentMethod(method.id)}
                    className="p-2 text-gray-400 hover:text-red-400 transition-colors"
                    title="Delete payment method"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Add Payment Method Form */}
        {showAddForm && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">Add Payment Method</h2>
              <button
                onClick={() => setShowAddForm(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ✕
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-white font-medium mb-2">Card Number</label>
                  <input
                    type="text"
                    placeholder="1234 5678 9012 3456"
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-accent-500"
                  />
                </div>
                
                <div>
                  <label className="block text-white font-medium mb-2">Cardholder Name</label>
                  <input
                    type="text"
                    placeholder="John Doe"
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-accent-500"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-white font-medium mb-2">Expiry Month</label>
                  <select className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500">
                    {Array.from({ length: 12 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {String(i + 1).padStart(2, '0')}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-white font-medium mb-2">Expiry Year</label>
                  <select className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-accent-500">
                    {Array.from({ length: 10 }, (_, i) => (
                      <option key={2024 + i} value={2024 + i}>
                        {2024 + i}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-white font-medium mb-2">CVV</label>
                  <input
                    type="text"
                    placeholder="123"
                    maxLength={4}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-accent-500"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-white font-medium mb-2">Nickname (Optional)</label>
                <input
                  type="text"
                  placeholder="e.g., Main Card, Work Card"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-accent-500"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="setDefault"
                  className="w-4 h-4 text-accent-600 bg-gray-700 border-gray-600 rounded focus:ring-accent-500"
                />
                <label htmlFor="setDefault" className="text-gray-300">
                  Set as default payment method
                </label>
              </div>
              
              <div className="flex space-x-4 pt-4">
                <button
                  onClick={() => setShowAddForm(false)}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    // Add payment method logic here
                    setShowAddForm(false)
                  }}
                  className="flex-1 bg-accent-600 hover:bg-accent-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Add Payment Method
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Billing Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800 rounded-lg border border-gray-700 p-6"
        >
          <h2 className="text-xl font-bold text-white mb-4">Billing Information</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-white mb-3">Billing Address</h3>
              <div className="text-gray-300 space-y-1">
                <p>John Doe</p>
                <p>123 Main Street</p>
                <p>New York, NY 10001</p>
                <p>United States</p>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold text-white mb-3">Tax Information</h3>
              <div className="text-gray-300 space-y-1">
                <p>Tax ID: Not provided</p>
                <p>VAT Number: Not applicable</p>
                <p>Tax Rate: Based on shipping address</p>
              </div>
            </div>
          </div>
          
          <button className="mt-4 text-accent-400 hover:text-accent-300 transition-colors">
            Update Billing Information
          </button>
        </motion.div>
      </div>
    </ProfileLayout>
  )
}
