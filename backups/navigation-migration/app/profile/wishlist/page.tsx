/**
 * Wishlist Page Component
 * 
 * Displays user's saved products in a responsive grid layout with comprehensive
 * wishlist management functionality. Integrates with existing cart system and
 * follows Syndicaps dark theme design patterns.
 * 
 * Features:
 * - Responsive 3-column grid layout on desktop
 * - Product cards with image, name, price, and availability
 * - Add to cart and remove from wishlist functionality
 * - Async cart operations with success modals
 * - Empty state when wishlist is empty
 * - Authentication protection
 * - Loading states and error handling
 * - Social sharing integration
 * - Privacy settings for wishlist visibility
 * 
 * Route: /profile/wishlist
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { Heart, ShoppingCart, Share2, Eye, EyeOff, Loader2, Package } from 'lucide-react'
import { useUser } from '@/lib/useUser'
import { useWishlistStore, useWishlistSummary, useOptimisticWishlist } from '@/store/wishlistStore'
import { useCartStore } from '@/store/cartStore'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { PageHeader } from '@/components/navigation/EnhancedBreadcrumbs'
import { Product } from '@/lib/firestore'
import Image from 'next/image'
import Link from 'next/link'
import toast from 'react-hot-toast'
import SocialShare, { useProductShare } from '@/components/social/SocialShare'
import { PointsSystem } from '@/lib/pointsSystem'
import { autoMigrateWishlistData, getMigrationStatus } from '@/lib/migrations/wishlistMigration'

/**
 * Interface for wishlist privacy settings
 */
interface WishlistSettings {
  isPublic: boolean
  allowSharing: boolean
}

/**
 * Wishlist Page Component
 */
export default function WishlistPage() {
  const router = useRouter()
  const { user, profile, loading: userLoading } = useUser()
  const {
    items,
    removeFromWishlist,
    getItemCount,
    moveToCart,
    setUserId,
    error: wishlistError,
    clearError,
    isLoading: wishlistLoading,
    isSyncing
  } = useWishlistStore()
  const { isLoading: summaryLoading, error: summaryError } = useWishlistSummary()
  const { toggleWishlistOptimistically } = useOptimisticWishlist()
  const addToCart = useCartStore(state => state.addItem)
  const { createWishlistShareContent } = useProductShare()

  // Local state
  const [settings, setSettings] = useState<WishlistSettings>({
    isPublic: true,
    allowSharing: true
  })
  const [isLoading, setIsLoading] = useState(false)
  const [removingItems, setRemovingItems] = useState<Set<string>>(new Set())
  const [addingToCart, setAddingToCart] = useState<Set<string>>(new Set())
  const [migrationStatus, setMigrationStatus] = useState(getMigrationStatus())

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!userLoading && !user) {
      router.push('/auth?redirect=/profile/wishlist')
    }
  }, [user, userLoading, router])

  // Initialize wishlist store and handle migration when user is available
  useEffect(() => {
    if (user && user.uid) {
      // Set user ID in wishlist store (this will trigger auto-load from Firestore)
      setUserId(user.uid)

      // Handle auto-migration of legacy data
      if (migrationStatus.needsMigration) {
        autoMigrateWishlistData(user.uid)
          .then(() => {
            setMigrationStatus(getMigrationStatus())
            toast.success('Wishlist data migrated successfully!')
          })
          .catch((error) => {
            console.error('Migration failed:', error)
            toast.error('Failed to migrate wishlist data')
          })
      }
    }
  }, [user, setUserId, migrationStatus.needsMigration])

  // Clear errors when component unmounts
  useEffect(() => {
    return () => {
      if (wishlistError) {
        clearError()
      }
    }
  }, [wishlistError, clearError])

  // Load wishlist settings from profile or localStorage
  useEffect(() => {
    if (profile) {
      // Load settings from profile or use defaults
      const savedSettings = localStorage.getItem('wishlist-settings')
      if (savedSettings) {
        try {
          setSettings(JSON.parse(savedSettings))
        } catch (error) {
          console.error('Error loading wishlist settings:', error)
        }
      }
    }
  }, [profile])

  /**
   * Toggle wishlist privacy setting
   */
  const togglePrivacy = () => {
    const newSettings = { ...settings, isPublic: !settings.isPublic }
    setSettings(newSettings)
    localStorage.setItem('wishlist-settings', JSON.stringify(newSettings))

    toast.success(
      `Wishlist is now ${newSettings.isPublic ? 'public' : 'private'}`,
      {
        duration: 3000,
        style: {
          background: '#1f2937',
          color: '#fff',
          border: '1px solid #374151'
        }
      }
    )
  }

  /**
   * Handle wishlist sharing with points reward
   */
  const handleShareWishlist = async (platform: string) => {
    if (!user?.uid) return

    try {
      // Award social sharing points
      await PointsSystem.awardSocialSharePoints(user.uid, platform, 'wishlist')

      toast.success(
        `Wishlist shared on ${platform}! You earned 150 points!`,
        {
          duration: 4000,
          style: {
            background: '#1f2937',
            color: '#fff',
            border: '1px solid #374151'
          }
        }
      )
    } catch (error: any) {
      console.error('Error awarding share points:', error)

      // Still show success for sharing, but mention points limit if applicable
      if (error.message.includes('Daily social media sharing limit')) {
        toast.success(
          `Wishlist shared on ${platform}! (Daily points limit reached)`,
          {
            duration: 4000,
            style: {
              background: '#1f2937',
              color: '#fff',
              border: '1px solid #374151'
            }
          }
        )
      } else {
        toast.success(
          `Wishlist shared on ${platform}!`,
          {
            duration: 3000,
            style: {
              background: '#1f2937',
              color: '#fff',
              border: '1px solid #374151'
            }
          }
        )
      }
    }
  }

  /**
   * Handle removing item from wishlist
   */
  const handleRemoveFromWishlist = async (productId: string, productName: string) => {
    setRemovingItems(prev => new Set(prev).add(productId))
    
    try {
      removeFromWishlist(productId)
      
      toast.success(`${productName} removed from wishlist`, {
        duration: 3000,
        style: {
          background: '#1f2937',
          color: '#fff',
          border: '1px solid #374151'
        }
      })
    } catch (error) {
      console.error('Error removing from wishlist:', error)
      toast.error('Failed to remove item from wishlist', {
        duration: 4000,
        style: {
          background: '#1f2937',
          color: '#fff',
          border: '1px solid #374151'
        }
      })
    } finally {
      setRemovingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(productId)
        return newSet
      })
    }
  }

  /**
   * Handle adding item to cart from wishlist
   */
  const handleAddToCart = async (product: Product) => {
    setAddingToCart(prev => new Set(prev).add(product.id))
    
    try {
      addToCart(product)
      
      // Calculate new cart total for success message
      const cartItems = useCartStore.getState().items
      const newTotal = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0)
      
      toast.success(
        `${product.name} added to cart! Cart total: $${newTotal.toFixed(2)}`,
        {
          duration: 4000,
          style: {
            background: '#1f2937',
            color: '#fff',
            border: '1px solid #374151'
          }
        }
      )
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast.error('Failed to add item to cart', {
        duration: 4000,
        style: {
          background: '#1f2937',
          color: '#fff',
          border: '1px solid #374151'
        }
      })
    } finally {
      setAddingToCart(prev => {
        const newSet = new Set(prev)
        newSet.delete(product.id)
        return newSet
      })
    }
  }

  /**
   * Handle moving item from wishlist to cart
   */
  const handleMoveToCart = async (productId: string, productName: string) => {
    const item = items.find(item => item.product.id === productId)
    if (!item) return

    setAddingToCart(prev => new Set(prev).add(productId))
    setRemovingItems(prev => new Set(prev).add(productId))
    
    try {
      moveToCart(productId, addToCart)
      
      // Calculate new cart total for success message
      const cartItems = useCartStore.getState().items
      const newTotal = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0)
      
      toast.success(
        `${productName} moved to cart! Cart total: $${newTotal.toFixed(2)}`,
        {
          duration: 4000,
          style: {
            background: '#1f2937',
            color: '#fff',
            border: '1px solid #374151'
          }
        }
      )
    } catch (error) {
      console.error('Error moving to cart:', error)
      toast.error('Failed to move item to cart', {
        duration: 4000,
        style: {
          background: '#1f2937',
          color: '#fff',
          border: '1px solid #374151'
        }
      })
    } finally {
      setAddingToCart(prev => {
        const newSet = new Set(prev)
        newSet.delete(productId)
        return newSet
      })
      setRemovingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(productId)
        return newSet
      })
    }
  }

  // Show loading state during authentication or wishlist loading
  if (userLoading || !user || wishlistLoading) {
    return (
      <ProfileLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin text-accent-500 mx-auto mb-4" />
            <p className="text-gray-400">
              {userLoading ? 'Loading user data...' :
               wishlistLoading ? 'Loading wishlist...' :
               'Loading...'}
            </p>
            {isSyncing && (
              <p className="text-sm text-accent-400 mt-2">
                Syncing wishlist data...
              </p>
            )}
          </div>
        </div>
      </ProfileLayout>
    )
  }

  const itemCount = getItemCount()

  return (
    <ProfileLayout>
      <div className="space-y-8">
        {/* Error Handling */}
        {(wishlistError || summaryError) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-900/20 border border-red-500/30 rounded-lg p-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
                <div>
                  <h3 className="text-red-400 font-medium">Wishlist Error</h3>
                  <p className="text-red-300 text-sm">
                    {wishlistError || summaryError || 'An error occurred with your wishlist'}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={clearError}
                  className="text-red-400 hover:text-red-300 text-sm underline"
                >
                  Dismiss
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm"
                >
                  Retry
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Migration Status */}
        {migrationStatus.needsMigration && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <Loader2 className="w-4 h-4 text-white animate-spin" />
              </div>
              <div>
                <h3 className="text-blue-400 font-medium">Migrating Wishlist Data</h3>
                <p className="text-blue-300 text-sm">
                  We're upgrading your wishlist to sync across all your devices...
                </p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Sync Status */}
        {isSyncing && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-accent-900/20 border border-accent-500/30 rounded-lg p-3"
          >
            <div className="flex items-center space-x-3">
              <Loader2 className="w-4 h-4 text-accent-400 animate-spin" />
              <span className="text-accent-400 text-sm">Syncing wishlist...</span>
            </div>
          </motion.div>
        )}

        {/* Page Header */}
        <PageHeader
          title="My Wishlist"
          description={`${itemCount} ${itemCount === 1 ? 'item' : 'items'} saved for later`}
          actions={
            <div className="flex items-center space-x-3">
              {/* Privacy Toggle */}
              <button
                onClick={togglePrivacy}
                className={`
                  inline-flex items-center space-x-2 px-4 py-2 rounded-lg transition-all min-h-[44px]
                  ${settings.isPublic 
                    ? 'bg-green-600 hover:bg-green-700 text-white' 
                    : 'bg-gray-600 hover:bg-gray-700 text-white'
                  }
                `}
                aria-label={`Wishlist is currently ${settings.isPublic ? 'public' : 'private'}`}
              >
                {settings.isPublic ? <Eye size={16} /> : <EyeOff size={16} />}
                <span className="hidden sm:inline">
                  {settings.isPublic ? 'Public' : 'Private'}
                </span>
              </button>
              
              {/* Share Button */}
              {settings.isPublic && itemCount > 0 && (
                <SocialShare
                  content={createWishlistShareContent(items.map(item => item.product))}
                  modal={true}
                  onShare={handleShareWishlist}
                  trigger={
                    <button
                      className="inline-flex items-center space-x-2 px-4 py-2 rounded-lg bg-accent-500 hover:bg-accent-600 text-white transition-all min-h-[44px]"
                      aria-label="Share wishlist"
                    >
                      <Share2 size={16} />
                      <span className="hidden sm:inline">Share</span>
                    </button>
                  }
                />
              )}
            </div>
          }
        />

        {/* Wishlist Content */}
        <AnimatePresence mode="wait">
          {itemCount === 0 ? (
            // Empty State
            <motion.div
              key="empty"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="text-center py-16"
            >
              <div className="bg-gray-800 rounded-lg p-8 max-w-md mx-auto">
                <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart size={32} className="text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  Your wishlist is empty
                </h3>
                <p className="text-gray-400 mb-6">
                  Start adding products you love to keep track of them for later.
                </p>
                <Link
                  href="/shop"
                  className="inline-flex items-center space-x-2 px-6 py-3 bg-accent-500 hover:bg-accent-600 text-white rounded-lg transition-colors min-h-[44px]"
                >
                  <Package size={16} />
                  <span>Browse Products</span>
                </Link>
              </div>
            </motion.div>
          ) : (
            // Wishlist Grid
            <motion.div
              key="grid"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {items.map((item, index) => (
                <WishlistCard
                  key={item.product.id}
                  item={item}
                  index={index}
                  isRemoving={removingItems.has(item.product.id)}
                  isAddingToCart={addingToCart.has(item.product.id)}
                  onRemove={() => handleRemoveFromWishlist(item.product.id, item.product.name)}
                  onAddToCart={() => handleAddToCart(item.product)}
                  onMoveToCart={() => handleMoveToCart(item.product.id, item.product.name)}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </ProfileLayout>
  )
}

/**
 * Props for WishlistCard component
 */
interface WishlistCardProps {
  item: {
    product: Product
    addedAt: Date
    note?: string
  }
  index: number
  isRemoving: boolean
  isAddingToCart: boolean
  onRemove: () => void
  onAddToCart: () => void
  onMoveToCart: () => void
}

/**
 * Individual wishlist item card component
 */
const WishlistCard: React.FC<WishlistCardProps> = ({
  item,
  index,
  isRemoving,
  isAddingToCart,
  onRemove,
  onAddToCart,
  onMoveToCart
}) => {
  const { product, addedAt } = item
  const isLoading = isRemoving || isAddingToCart

  return (
    <motion.article
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ delay: index * 0.1 }}
      className={`bg-gray-800 rounded-lg overflow-hidden border border-gray-700 hover:border-gray-600 transition-all duration-200 ${
        isLoading ? 'opacity-75' : ''
      }`}
    >
      {/* Product Image */}
      <Link href={`/shop/${product.id}`} className="block relative">
        <div className="aspect-square relative overflow-hidden bg-gray-900">
          <Image
            src={product.image}
            alt={product.name}
            fill
            className="object-cover transition-transform duration-300 hover:scale-105"
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
          />

          {/* Availability Badge */}
          {product.soldOut && (
            <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded">
              Out of Stock
            </div>
          )}
        </div>
      </Link>

      {/* Product Info */}
      <div className="p-4 space-y-3">
        <div>
          <Link href={`/shop/${product.id}`}>
            <h3 className="font-semibold text-white hover:text-accent-400 transition-colors line-clamp-2">
              {product.name}
            </h3>
          </Link>
          <p className="text-green-400 font-bold text-lg mt-1">
            ${product.price.toFixed(2)}
          </p>
        </div>

        {/* Added Date */}
        <p className="text-xs text-gray-400">
          Added {addedAt.toLocaleDateString()}
        </p>

        {/* Action Buttons */}
        <div className="flex space-x-2">
          <button
            onClick={onAddToCart}
            disabled={product.soldOut || isLoading}
            className={`flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors min-h-[44px] flex items-center justify-center space-x-1 ${
              product.soldOut || isLoading
                ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                : 'bg-accent-500 text-white hover:bg-accent-600'
            }`}
          >
            {isAddingToCart ? (
              <Loader2 size={16} className="animate-spin" />
            ) : (
              <>
                <ShoppingCart size={16} />
                <span className="hidden sm:inline">Add to Cart</span>
                <span className="sm:hidden">Cart</span>
              </>
            )}
          </button>

          <button
            onClick={onRemove}
            disabled={isLoading}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors min-h-[44px] flex items-center justify-center ${
              isLoading
                ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                : 'bg-gray-700 text-gray-300 hover:bg-red-600 hover:text-white'
            }`}
            aria-label="Remove from wishlist"
          >
            {isRemoving ? (
              <Loader2 size={16} className="animate-spin" />
            ) : (
              <Heart size={16} className="fill-current" />
            )}
          </button>
        </div>
      </div>
    </motion.article>
  )
}
