'use client'

/**
 * Enhanced Profile Edit Page
 * 
 * Comprehensive profile management page with advanced features including
 * real-time updates, profile completion tracking, and enhanced editing capabilities.
 * 
 * Features:
 * - Advanced profile information display and editing
 * - Real-time profile completion tracking
 * - Enhanced profile photo management
 * - Account statistics and achievements
 * - Quick actions and settings
 * - Social media integration
 * - Privacy settings management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  User,
  Edit3,
  RefreshCw,
  ArrowLeft,
  Save,
  Mail,
  Phone,
  MapPin,
  Globe,
  CheckCircle
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import ProfileLayout from '@/components/profile/ProfileLayout'
import EnhancedProfileEditor from '@/components/profile/EnhancedProfileEditor'
import ProfileCompletionTracker from '@/components/profile/ProfileCompletionTracker'
import UnifiedProfileHeader from '@/components/profile/UnifiedProfileHeader'
import ProgressiveCompletionFlow from '@/components/profile/ProgressiveCompletionFlow'
import GuidedWorkflowManager from '@/components/profile/GuidedWorkflowManager'
import CompletionCelebration from '@/components/profile/CompletionCelebration'

import { useUser } from '@/lib/useUser'
import UserAvatar from '@/components/ui/UserAvatar'
import { ProfilePhotoService } from '@/lib/profile/profilePhotoService'
import toast from 'react-hot-toast'

export default function EnhancedProfileEditPage() {
  const { user, profile, loading, refreshProfile } = useUser()
  const router = useRouter()
  const [showProfileEditor, setShowProfileEditor] = useState(false)
  const [showGuidedWorkflow, setShowGuidedWorkflow] = useState(false)
  const [showCelebration, setShowCelebration] = useState(false)
  const [completedSection, setCompletedSection] = useState<any>(null)
  const [useProgressiveFlow, setUseProgressiveFlow] = useState(true)
  const [profileStats, setProfileStats] = useState({
    completionPercentage: 0,
    securityScore: 0,
    profileViews: 0,
    lastUpdated: null as Date | null
  })
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  // Calculate profile statistics - trigger on profile changes and refresh trigger
  useEffect(() => {
    if (profile) {
      calculateProfileStats()
    }
  }, [profile, refreshTrigger])

  const calculateProfileStats = () => {
    if (!profile) return

    try {
      // Calculate completion percentage
      const fields = [
        profile.displayName,
        profile.firstName,
        profile.lastName,
        profile.bio,
        profile.avatar,
        profile.phone,
        profile.location,
        profile.emailVerified,
        profile.phoneVerified
      ]

      const completedFields = fields.filter(field => field).length
      const completionPercentage = Math.round((completedFields / fields.length) * 100)

      // Calculate security score with safe property access
      let securityScore = 0
      if (profile.emailVerified) securityScore += 20
      if (profile.phoneVerified) securityScore += 20
      if (profile.mfaEnabled) securityScore += 30
      if (profile.passwordStrength === 'strong') securityScore += 20
      if (profile.avatar) securityScore += 10

      setProfileStats({
        completionPercentage,
        securityScore,
        profileViews: 0, // Would be tracked in real implementation
        lastUpdated: profile.updatedAt ?
          (profile.updatedAt instanceof Date ? profile.updatedAt : profile.updatedAt.toDate()) : null
      })
    } catch (error) {
      console.warn('Error calculating profile stats:', error)
      // Set default stats if calculation fails
      setProfileStats({
        completionPercentage: 0,
        securityScore: 0,
        profileViews: 0,
        lastUpdated: null
      })
    }
  }

  const handleProfileUpdate = async () => {
    try {
      // Refresh profile data from Firestore
      await refreshProfile()

      // Force refresh of all completion calculations
      setRefreshTrigger(prev => prev + 1)

      // Recalculate stats with fresh data
      setTimeout(() => {
        calculateProfileStats()
      }, 300) // Small delay to ensure profile data has updated

      // Show success message
      toast.success('Profile updated successfully!')
    } catch (error) {
      console.error('Error handling profile update:', error)
      toast.error('Profile updated but some features may not be available.')
    }
  }

  // Smart navigation with auto-focus on incomplete sections
  const handleSectionClick = (sectionId: string) => {
    const tabMapping: Record<string, string> = {
      'basic_info': 'basic',
      'profile_photo': 'basic', // Photo upload is in basic tab
      'contact_personal': 'contact',
      'location': 'contact',
      'social_links': 'social',
      'phone_verification': 'contact',
      'security_setup': 'security'
    }

    const targetTab = tabMapping[sectionId]

    if (sectionId === 'profile_photo') {
      // Photo upload is handled on social profile page
      router.push('/profile/social')
      return
    }

    if (sectionId === 'security_setup') {
      router.push('/profile/security')
      return
    }

    if (targetTab) {
      // Open editor with specific tab focused
      setShowProfileEditor(true)
      // Store the target tab for the editor to use
      sessionStorage.setItem('profileEditorTab', targetTab)
    } else {
      // Default to opening the editor
      setShowProfileEditor(true)
    }
  }

  // Smart tab suggestion based on completion status
  const getNextIncompleteSection = () => {
    if (!profile) return 'basic'

    // Priority order for completion
    const completionChecks = [
      { tab: 'basic', check: !!(profile.displayName && profile.bio && profile.bio.length > 10) },
      { tab: 'contact', check: !!(profile.firstName && profile.lastName && profile.phone) },
      { tab: 'social', check: !!(profile.website || profile.socialLinks?.twitter || profile.socialLinks?.instagram) },
      { tab: 'addresses', check: false }, // Always suggest addresses as optional
      { tab: 'privacy', check: true } // Privacy has defaults, so usually complete
    ]

    const incompleteSection = completionChecks.find(section => !section.check)
    return incompleteSection?.tab || 'basic'
  }

  // Enhanced profile editor opening with smart tab selection
  const handleEditProfile = () => {
    const suggestedTab = getNextIncompleteSection()
    sessionStorage.setItem('profileEditorTab', suggestedTab)
    setShowProfileEditor(true)
  }

  // Guided workflow handlers
  const handleStartGuidedWorkflow = () => {
    setShowGuidedWorkflow(true)
  }

  const handleGuidedStepClick = (stepId: string) => {
    // Map step IDs to tab IDs and open editor
    const stepToTabMap: Record<string, string> = {
      'basic_info': 'basic',
      'profile_photo': 'basic',
      'contact_info': 'contact',
      'addresses': 'addresses',
      'social_links': 'social'
    }

    const tabId = stepToTabMap[stepId]
    if (tabId) {
      sessionStorage.setItem('profileEditorTab', tabId)
      setShowProfileEditor(true)
      setShowGuidedWorkflow(false)
    }
  }

  const handleStepComplete = (stepId: string) => {
    // Show celebration for completed step
    const stepInfo = {
      id: stepId,
      title: stepId.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      weight: 20, // Default weight
      category: 'recommended' as const
    }

    setCompletedSection(stepInfo)
    setShowCelebration(true)
  }

  const handleGuidedProfileUpdate = () => {
    // Refresh profile data and trigger celebration if needed
    refreshProfile()
    setRefreshTrigger(prev => prev + 1)
  }

  if (loading) {
    return (
      <ProfileLayout>
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 text-gray-400 animate-spin mx-auto mb-4" />
            <p className="text-gray-400">Loading profile...</p>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  if (!user || !profile) {
    return (
      <ProfileLayout>
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <p className="text-gray-400">Please log in to edit your profile.</p>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout>
      <div className="space-y-6">
        {/* Unified Profile Header */}
        <UnifiedProfileHeader
          user={user}
          profile={profile}
          completionPercentage={profileStats.completionPercentage}
          securityScore={profileStats.securityScore}
          totalPoints={profile.points || 0}
          onEditClick={handleEditProfile}
          onSecurityClick={() => router.push('/profile/security')}
        />

        {/* Progressive Completion Flow */}
        {useProgressiveFlow ? (
          <ProgressiveCompletionFlow
            profile={profile}
            onStepClick={handleGuidedStepClick}
            onStartGuided={handleStartGuidedWorkflow}
            showCelebration={false}
          />
        ) : (
          <ProfileCompletionTracker
            onSectionClick={handleSectionClick}
            showDetailed={true}
            refreshTrigger={refreshTrigger}
          />
        )}
      </div>

      {/* Enhanced Profile Editor Modal */}
      <EnhancedProfileEditor
        isOpen={showProfileEditor}
        onClose={() => setShowProfileEditor(false)}
        onProfileUpdate={handleProfileUpdate}
      />

      {/* Guided Workflow Manager */}
      <GuidedWorkflowManager
        isOpen={showGuidedWorkflow}
        onClose={() => setShowGuidedWorkflow(false)}
        profile={profile}
        onStepComplete={handleStepComplete}
        onOpenEditor={(tabId, fieldFocus) => {
          if (tabId) {
            sessionStorage.setItem('profileEditorTab', tabId)
          }
          setShowProfileEditor(true)
          setShowGuidedWorkflow(false)
        }}
      />

      {/* Completion Celebration */}
      {completedSection && (
        <CompletionCelebration
          isVisible={showCelebration}
          onClose={() => {
            setShowCelebration(false)
            setCompletedSection(null)
          }}
          completedSection={completedSection}
          newCompletionPercentage={profileStats.completionPercentage}
        />
      )}
    </ProfileLayout>
  )
}
