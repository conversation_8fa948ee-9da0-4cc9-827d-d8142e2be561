'use client'

/**
 * Profile Achievements Page
 * 
 * Displays user achievements, badges, and progress tracking.
 * Shows earned achievements and available achievements to unlock.
 * 
 * Features:
 * - Achievement showcase with categories
 * - Progress tracking for incomplete achievements
 * - Achievement details and requirements
 * - Rarity-based filtering and sorting
 * - Achievement statistics
 * 
 * <AUTHOR> Team
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Trophy,
  Star,
  Award,
  Target,
  Lock,
  CheckCircle,
  Filter,
  Search,
  Calendar,
  TrendingUp
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'
import { AchievementSystem, ACHIEVEMENTS } from '@/lib/achievementSystem'
import { Achievement, UserAchievement, AchievementCategory } from '@/types/profile'

export default function AchievementsPage() {
  const { user, profile } = useUser()
  const [userAchievements, setUserAchievements] = useState<Array<UserAchievement & Achievement>>([])
  const [selectedCategory, setSelectedCategory] = useState<AchievementCategory | 'all'>('all')
  const [selectedRarity, setSelectedRarity] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [loading, setLoading] = useState(true)

  /**
   * Load user achievements
   */
  useEffect(() => {
    if (user) {
      loadAchievements()
    }
  }, [user])

  const loadAchievements = async () => {
    if (!user) return

    try {
      setLoading(true)
      const achievements = await AchievementSystem.getUserAchievements(user.uid)
      setUserAchievements(achievements)
    } catch (error) {
      console.error('Failed to load achievements:', error)
    } finally {
      setLoading(false)
    }
  }

  // Get earned achievement IDs
  const earnedAchievementIds = new Set(userAchievements.map(a => a.achievementId))

  // Filter achievements
  const filteredAchievements = ACHIEVEMENTS.filter(achievement => {
    const matchesCategory = selectedCategory === 'all' || achievement.category === selectedCategory
    const matchesRarity = selectedRarity === 'all' || achievement.rarity === selectedRarity
    const matchesSearch = searchTerm === '' || 
      achievement.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      achievement.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesCategory && matchesRarity && matchesSearch
  })

  // Separate earned and unearned achievements
  const earnedAchievements = filteredAchievements.filter(a => earnedAchievementIds.has(a.id))
  const unearnedAchievements = filteredAchievements.filter(a => !earnedAchievementIds.has(a.id))

  // Achievement statistics
  const totalAchievements = ACHIEVEMENTS.length
  const earnedCount = userAchievements.length
  const completionPercentage = Math.round((earnedCount / totalAchievements) * 100)
  const totalPointsEarned = userAchievements.reduce((sum, a) => sum + (a.points || 0), 0)

  // Category counts
  const categoryStats = ACHIEVEMENTS.reduce((acc, achievement) => {
    acc[achievement.category] = (acc[achievement.category] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-400 border-gray-400'
      case 'rare': return 'text-blue-400 border-blue-400'
      case 'epic': return 'text-purple-400 border-purple-400'
      case 'legendary': return 'text-yellow-400 border-yellow-400'
      default: return 'text-gray-400 border-gray-400'
    }
  }

  const getRarityBg = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-400/10'
      case 'rare': return 'bg-blue-400/10'
      case 'epic': return 'bg-purple-400/10'
      case 'legendary': return 'bg-yellow-400/10'
      default: return 'bg-gray-400/10'
    }
  }

  if (!user) {
    return (
      <ProfileLayout>
        <div className="text-center py-12">
          <Trophy className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">Please log in</h3>
          <p className="text-gray-400">You need to be logged in to view your achievements.</p>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Achievements</h1>
              <p className="text-gray-400 mt-1">
                Track your progress and unlock badges
              </p>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-white">{earnedCount}</div>
              <div className="text-gray-400 text-sm">of {totalAchievements}</div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-400">
              <span>Progress</span>
              <span>{completionPercentage}% Complete</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className="bg-accent-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${completionPercentage}%` }}
              />
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center space-x-3">
              <Trophy className="text-yellow-400" size={24} />
              <div>
                <div className="text-xl font-bold text-white">{earnedCount}</div>
                <div className="text-sm text-gray-400">Earned</div>
              </div>
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center space-x-3">
              <Star className="text-purple-400" size={24} />
              <div>
                <div className="text-xl font-bold text-white">{totalPointsEarned}</div>
                <div className="text-sm text-gray-400">Points</div>
              </div>
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center space-x-3">
              <TrendingUp className="text-green-400" size={24} />
              <div>
                <div className="text-xl font-bold text-white">{completionPercentage}%</div>
                <div className="text-sm text-gray-400">Complete</div>
              </div>
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center space-x-3">
              <Target className="text-blue-400" size={24} />
              <div>
                <div className="text-xl font-bold text-white">{totalAchievements - earnedCount}</div>
                <div className="text-sm text-gray-400">Remaining</div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex flex-wrap gap-4">
              {/* Category Filter */}
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value as AchievementCategory | 'all')}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              >
                <option value="all">All Categories</option>
                <option value="collector">Collector</option>
                <option value="social">Social</option>
                <option value="loyalty">Loyalty</option>
                <option value="special">Special</option>
              </select>

              {/* Rarity Filter */}
              <select
                value={selectedRarity}
                onChange={(e) => setSelectedRarity(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              >
                <option value="all">All Rarities</option>
                <option value="common">Common</option>
                <option value="rare">Rare</option>
                <option value="epic">Epic</option>
                <option value="legendary">Legendary</option>
              </select>
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search achievements..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 w-full lg:w-64"
              />
            </div>
          </div>
        </div>

        {/* Earned Achievements */}
        {earnedAchievements.length > 0 && (
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
              <CheckCircle className="text-green-400" size={24} />
              <span>Earned Achievements ({earnedAchievements.length})</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {earnedAchievements.map((achievement) => {
                const userAchievement = userAchievements.find(ua => ua.achievementId === achievement.id)
                return (
                  <motion.div
                    key={achievement.id}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className={`${getRarityBg(achievement.rarity)} border ${getRarityColor(achievement.rarity)} rounded-lg p-4`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="text-3xl">{achievement.icon}</div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-white">{achievement.name}</h3>
                        <p className="text-sm text-gray-400 mb-2">{achievement.description}</p>
                        <div className="flex items-center justify-between">
                          <span className={`text-xs px-2 py-1 rounded ${getRarityColor(achievement.rarity)} ${getRarityBg(achievement.rarity)}`}>
                            {achievement.rarity}
                          </span>
                          <span className="text-accent-400 text-sm font-medium">
                            +{achievement.points} pts
                          </span>
                        </div>
                        {userAchievement && (
                          <div className="text-xs text-gray-500 mt-2">
                            Earned {new Date(userAchievement.unlockedAt).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </div>
        )}

        {/* Available Achievements */}
        {unearnedAchievements.length > 0 && (
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
              <Lock className="text-gray-400" size={24} />
              <span>Available Achievements ({unearnedAchievements.length})</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {unearnedAchievements.map((achievement) => (
                <motion.div
                  key={achievement.id}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="bg-gray-700/50 border border-gray-600 rounded-lg p-4 opacity-75"
                >
                  <div className="flex items-start space-x-3">
                    <div className="text-3xl grayscale">{achievement.icon}</div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-300">{achievement.name}</h3>
                      <p className="text-sm text-gray-500 mb-2">{achievement.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs px-2 py-1 rounded border border-gray-600 text-gray-400">
                          {achievement.rarity}
                        </span>
                        <span className="text-gray-500 text-sm font-medium">
                          +{achievement.points} pts
                        </span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* No Results */}
        {filteredAchievements.length === 0 && (
          <div className="bg-gray-800 rounded-lg p-12 border border-gray-700 text-center">
            <Search className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No achievements found</h3>
            <p className="text-gray-400">Try adjusting your filters or search terms.</p>
          </div>
        )}
      </div>
    </ProfileLayout>
  )
}
