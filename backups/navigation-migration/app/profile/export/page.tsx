'use client'

/**
 * Data Export & Portability Page
 * 
 * Comprehensive data export interface with privacy controls,
 * multiple export formats, and GDPR compliance features.
 * 
 * Features:
 * - Multiple export formats (JSON, CSV, XML, PDF, Excel)
 * - Privacy-aware data exports
 * - GDPR and CCPA compliance
 * - Export history and tracking
 * - Data deletion capabilities
 * 
 * <AUTHOR> Team
 */

import React from 'react'
import { motion } from 'framer-motion'
import {
  Database,
  Shield,
  Download,
  Globe,
  Lock,
  FileText,
  Users,
  BarChart3
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import DataExportDashboard from '@/components/profile/export/DataExportDashboard'
import { useUser } from '@/lib/useUser'

export default function DataExportPage() {
  const { user, profile } = useUser()

  if (!user || !profile) {
    return (
      <ProfileLayout>
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <Database className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">Please log in</h3>
            <p className="text-gray-400">You need to be logged in to manage your data exports.</p>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white flex items-center space-x-3">
                <Database size={28} />
                <span>Data Export & Portability</span>
              </h1>
              <p className="text-gray-400 mt-2">
                Export your data in multiple formats with full privacy control and GDPR compliance
              </p>
            </div>
            <div className="hidden md:flex items-center space-x-6">
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-full mb-2">
                  <Download size={24} className="text-white" />
                </div>
                <div className="text-sm text-gray-400">Export Data</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-green-600 rounded-full mb-2">
                  <Shield size={24} className="text-white" />
                </div>
                <div className="text-sm text-gray-400">Privacy Protected</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-purple-600 rounded-full mb-2">
                  <Globe size={24} className="text-white" />
                </div>
                <div className="text-sm text-gray-400">GDPR Compliant</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Feature Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          <div className="bg-gradient-to-br from-blue-600 to-cyan-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <FileText size={24} />
              <div className="text-right">
                <div className="text-2xl font-bold">5</div>
                <div className="text-blue-100 text-sm">Formats</div>
              </div>
            </div>
            <p className="text-blue-100 text-sm">
              Export in JSON, CSV, XML, PDF, and Excel formats for maximum compatibility.
            </p>
          </div>

          <div className="bg-gradient-to-br from-green-600 to-emerald-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <Shield size={24} />
              <div className="text-right">
                <div className="text-2xl font-bold">3</div>
                <div className="text-green-100 text-sm">Privacy Levels</div>
              </div>
            </div>
            <p className="text-green-100 text-sm">
              Choose from full, anonymous, or minimal privacy levels for your exports.
            </p>
          </div>

          <div className="bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <Globe size={24} />
              <div className="text-right">
                <div className="text-2xl font-bold">100%</div>
                <div className="text-purple-100 text-sm">Compliant</div>
              </div>
            </div>
            <p className="text-purple-100 text-sm">
              Full GDPR and CCPA compliance with transparent data handling practices.
            </p>
          </div>

          <div className="bg-gradient-to-br from-orange-600 to-red-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <BarChart3 size={24} />
              <div className="text-right">
                <div className="text-2xl font-bold">Real-time</div>
                <div className="text-orange-100 text-sm">Processing</div>
              </div>
            </div>
            <p className="text-orange-100 text-sm">
              Instant export processing with real-time status tracking and notifications.
            </p>
          </div>
        </motion.div>

        {/* Main Export Dashboard */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <DataExportDashboard profile={profile} />
        </motion.div>

        {/* Export Formats Guide */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <h2 className="text-xl font-semibold text-white mb-6">Export Format Guide</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <FileText size={16} className="text-white" />
                </div>
                <h3 className="text-white font-medium">JSON Format</h3>
              </div>
              <p className="text-gray-400 text-sm">
                Machine-readable format perfect for developers and data analysis. 
                Preserves data structure and relationships.
              </p>
              <div className="text-xs text-blue-400">
                Best for: API integration, backup, developer use
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                  <BarChart3 size={16} className="text-white" />
                </div>
                <h3 className="text-white font-medium">CSV Format</h3>
              </div>
              <p className="text-gray-400 text-sm">
                Spreadsheet-compatible format that opens in Excel, Google Sheets, 
                and other data analysis tools.
              </p>
              <div className="text-xs text-green-400">
                Best for: Data analysis, spreadsheets, reporting
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                  <FileText size={16} className="text-white" />
                </div>
                <h3 className="text-white font-medium">XML Format</h3>
              </div>
              <p className="text-gray-400 text-sm">
                Structured markup format with excellent compatibility across 
                different systems and platforms.
              </p>
              <div className="text-xs text-purple-400">
                Best for: System integration, enterprise use
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
                  <FileText size={16} className="text-white" />
                </div>
                <h3 className="text-white font-medium">PDF Format</h3>
              </div>
              <p className="text-gray-400 text-sm">
                Human-readable document format with professional formatting, 
                perfect for records and official documentation.
              </p>
              <div className="text-xs text-red-400">
                Best for: Records, documentation, sharing
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center">
                  <BarChart3 size={16} className="text-white" />
                </div>
                <h3 className="text-white font-medium">Excel Format</h3>
              </div>
              <p className="text-gray-400 text-sm">
                Native Excel format with advanced features like multiple sheets, 
                formulas, and rich formatting options.
              </p>
              <div className="text-xs text-yellow-400">
                Best for: Advanced analysis, business reporting
              </div>
            </div>
          </div>
        </motion.div>

        {/* Privacy Levels Guide */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <h2 className="text-xl font-semibold text-white mb-6">Privacy Protection Levels</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gradient-to-br from-red-600/20 to-orange-600/20 border border-red-600/30 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Lock className="text-red-400" size={24} />
                <h3 className="text-white font-semibold">Full Export</h3>
              </div>
              <p className="text-gray-300 text-sm mb-4">
                Complete data export including all personal information, 
                activity history, and private details.
              </p>
              <div className="space-y-2">
                <div className="text-xs text-red-300">Includes:</div>
                <ul className="text-xs text-gray-400 space-y-1">
                  <li>• Full name and email</li>
                  <li>• Complete activity history</li>
                  <li>• Private messages and data</li>
                  <li>• All analytics and insights</li>
                </ul>
              </div>
            </div>

            <div className="bg-gradient-to-br from-yellow-600/20 to-orange-600/20 border border-yellow-600/30 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Users className="text-yellow-400" size={24} />
                <h3 className="text-white font-semibold">Anonymous Export</h3>
              </div>
              <p className="text-gray-300 text-sm mb-4">
                Anonymized data export that removes personally identifiable 
                information while preserving usage patterns.
              </p>
              <div className="space-y-2">
                <div className="text-xs text-yellow-300">Excludes:</div>
                <ul className="text-xs text-gray-400 space-y-1">
                  <li>• Real names and emails</li>
                  <li>• Location data</li>
                  <li>• Private communications</li>
                  <li>• Identifying metadata</li>
                </ul>
              </div>
            </div>

            <div className="bg-gradient-to-br from-green-600/20 to-emerald-600/20 border border-green-600/30 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Shield className="text-green-400" size={24} />
                <h3 className="text-white font-semibold">Minimal Export</h3>
              </div>
              <p className="text-gray-300 text-sm mb-4">
                Basic data export with only essential information and 
                public-facing content for maximum privacy protection.
              </p>
              <div className="space-y-2">
                <div className="text-xs text-green-300">Limited to:</div>
                <ul className="text-xs text-gray-400 space-y-1">
                  <li>• Public profile information</li>
                  <li>• Basic achievement data</li>
                  <li>• Non-sensitive preferences</li>
                  <li>• General activity summaries</li>
                </ul>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Legal & Compliance */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-6"
        >
          <div className="flex items-start space-x-4">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <Globe size={16} className="text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-blue-300 font-semibold mb-3">Your Data Rights</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-200">
                <div>
                  <strong>Under GDPR, you have the right to:</strong>
                  <ul className="mt-2 space-y-1 text-blue-300">
                    <li>• Access your personal data</li>
                    <li>• Correct inaccurate data</li>
                    <li>• Delete your data (right to erasure)</li>
                    <li>• Data portability</li>
                  </ul>
                </div>
                <div>
                  <strong>Under CCPA, you have the right to:</strong>
                  <ul className="mt-2 space-y-1 text-blue-300">
                    <li>• Know what personal data is collected</li>
                    <li>• Delete personal data</li>
                    <li>• Opt-out of data sales</li>
                    <li>• Non-discrimination</li>
                  </ul>
                </div>
              </div>
              <p className="mt-4 text-sm text-blue-300">
                All exports are processed securely and deleted from our servers after 7 days. 
                Your privacy is our priority, and we are committed to transparent data handling practices.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </ProfileLayout>
  )
}