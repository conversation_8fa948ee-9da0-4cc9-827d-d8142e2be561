'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Activity, Shield, MapPin, Smartphone, Monitor, Filter, Calendar, Eye } from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'
import { UserActivity } from '@/lib/activitySystem'
import { getUserActivities, getUserActivityStats, getRecentLoginActivities } from '@/lib/activitySystem'

const activityTypes = {
  login: { icon: Shield, color: 'text-green-400', bg: 'bg-green-400/10' },
  logout: { icon: Shield, color: 'text-gray-400', bg: 'bg-gray-400/10' },
  login_failed: { icon: Shield, color: 'text-red-400', bg: 'bg-red-400/10' },
  purchase: { icon: Activity, color: 'text-blue-400', bg: 'bg-blue-400/10' },
  profile_update: { icon: Eye, color: 'text-yellow-400', bg: 'bg-yellow-400/10' },
  password_change: { icon: Shield, color: 'text-purple-400', bg: 'bg-purple-400/10' },
  raffle_entry: { icon: Activity, color: 'text-accent-400', bg: 'bg-accent-400/10' },
  points_earned: { icon: Activity, color: 'text-green-400', bg: 'bg-green-400/10' },
  points_redeemed: { icon: Activity, color: 'text-yellow-400', bg: 'bg-yellow-400/10' },
  review: { icon: Eye, color: 'text-blue-400', bg: 'bg-blue-400/10' },
  system: { icon: Activity, color: 'text-gray-400', bg: 'bg-gray-400/10' }
}

export default function ActivityPage() {
  const { user } = useUser()
  const [activities, setActivities] = useState<UserActivity[]>([])
  const [filter, setFilter] = useState<'all' | 'security' | 'purchases' | 'profile'>('all')
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d')
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    uniqueLocations: 0,
    uniqueDevices: 0,
    totalActivities: 0,
    securityEvents: 0
  })
  const [recentLogins, setRecentLogins] = useState<UserActivity[]>([])

  useEffect(() => {
    if (!user) return

    loadActivities()
    loadStats()
    loadRecentLogins()
  }, [user])

  const loadActivities = async () => {
    if (!user?.uid) return
    
    try {
      setLoading(true)
      const userActivities = await getUserActivities(user.uid, {
        timeRange,
        type: filter === 'all' ? undefined :
          filter === 'security' ? ['login', 'logout', 'login_failed', 'password_change'] :
          filter === 'purchases' ? ['purchase', 'raffle_entry'] :
          ['profile_update']
      })
      setActivities(userActivities)
    } catch (error) {
      console.error('Failed to load activities:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    if (!user?.uid) return

    try {
      const userStats = await getUserActivityStats(user.uid)
      setStats(userStats)
    } catch (error) {
      console.error('Failed to load activity stats:', error)
    }
  }

  const loadRecentLogins = async () => {
    if (!user?.uid) return

    try {
      const loginActivities = await getRecentLoginActivities(user.uid)
      setRecentLogins(loginActivities)
    } catch (error) {
      console.error('Failed to load recent logins:', error)
    }
  }

  useEffect(() => {
    if (!user) return
    loadActivities()
  }, [filter, timeRange])

  if (!user) {
    return (
      <ProfileLayout>
        <div className="text-center py-12">
          <p className="text-gray-400">Please log in to view your account activity.</p>
        </div>
      </ProfileLayout>
    )
  }

  const getActivityIcon = (type: string) => {
    const config = activityTypes[type as keyof typeof activityTypes]
    return config || activityTypes.system
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 48) return 'Yesterday'
    return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const getDeviceIcon = (device: string) => {
    if (device.includes('iPhone') || device.includes('Android')) return <Smartphone size={16} />
    return <Monitor size={16} />
  }

  const securityEvents = activities.filter(a => ['login_failed', 'password_change'].includes(a.type))
  const recentLoginsDisplay = activities.filter(a => a.type === 'login').slice(0, 5)

  return (
    <ProfileLayout>
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <Activity className="text-accent-400" size={28} />
            <div>
              <h1 className="text-2xl font-bold text-white">Account Activity</h1>
              <p className="text-gray-400">Monitor your account security and activity</p>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <Filter size={18} className="text-gray-400" />
              <span className="text-gray-400">Filter:</span>
            </div>
            
            <div className="flex space-x-2">
              {[
                { key: 'all', label: 'All Activity' },
                { key: 'security', label: 'Security' },
                { key: 'purchases', label: 'Purchases' },
                { key: 'profile', label: 'Profile' }
              ].map(({ key, label }) => (
                <button
                  key={key}
                  onClick={() => setFilter(key as any)}
                  className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                    filter === key
                      ? 'bg-accent-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {label}
                </button>
              ))}
            </div>

            <div className="flex items-center space-x-2">
              <Calendar size={18} className="text-gray-400" />
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as any)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-1 text-white text-sm focus:outline-none focus:border-accent-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="all">All time</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* Security Summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid md:grid-cols-3 gap-6"
        >
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-2">
              <Shield className="text-green-400" size={20} />
              <h3 className="font-semibold text-white">Security Status</h3>
            </div>
            <p className="text-2xl font-bold text-green-400">Secure</p>
            <p className="text-gray-400 text-sm">No suspicious activity detected</p>
          </div>

          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-2">
              <MapPin className="text-blue-400" size={20} />
              <h3 className="font-semibold text-white">Recent Locations</h3>
            </div>
            <p className="text-2xl font-bold text-blue-400">{stats.uniqueLocations}</p>
            <p className="text-gray-400 text-sm">Unique locations this month</p>
          </div>

          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-2">
              <Activity className="text-accent-400" size={20} />
              <h3 className="font-semibold text-white">Total Activities</h3>
            </div>
            <p className="text-2xl font-bold text-accent-400">{stats.totalActivities}</p>
            <p className="text-gray-400 text-sm">Activities recorded</p>
          </div>
        </motion.div>

        {/* Activity Log */}
        <div className="space-y-4">
          {loading ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-12 bg-gray-800 rounded-lg border border-gray-700"
            >
              <Activity className="mx-auto text-gray-600 animate-spin mb-4" size={48} />
              <h3 className="text-lg font-semibold text-white mb-2">Loading activities...</h3>
            </motion.div>
          ) : activities.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-12 bg-gray-800 rounded-lg border border-gray-700"
            >
              <Activity className="mx-auto text-gray-600 mb-4" size={48} />
              <h3 className="text-lg font-semibold text-white mb-2">No activity found</h3>
              <p className="text-gray-400">No activities match your current filters</p>
            </motion.div>
          ) : (
            activities.map((activity, index) => {
              const config = getActivityIcon(activity.type)
              const Icon = config.icon

              return (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 + index * 0.05 }}
                  className={`bg-gray-800 rounded-lg border border-gray-700 p-6 transition-all hover:border-gray-600 ${
                    !activity.success ? 'border-l-4 border-l-red-500' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`p-2 rounded-lg ${config.bg}`}>
                      <Icon className={config.color} size={20} />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className={`font-semibold ${activity.success ? 'text-white' : 'text-red-400'}`}>
                            {activity.action}
                          </h3>
                          <p className="text-gray-300 mt-1">
                            {activity.description}
                          </p>
                          
                          {activity.metadata && (
                            <div className="mt-2 text-sm text-gray-400">
                              {activity.metadata.orderId && (
                                <span>Order: {activity.metadata.orderId}</span>
                              )}
                              {activity.metadata.amount && (
                                <span className="ml-4">Amount: {activity.metadata.amount}</span>
                              )}
                              {activity.metadata.productName && (
                                <span>Product: {activity.metadata.productName}</span>
                              )}
                            </div>
                          )}
                          
                          <div className="flex items-center space-x-4 mt-3 text-xs text-gray-500">
                            <div className="flex items-center space-x-1">
                              <MapPin size={12} />
                              <span>{activity.location}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              {getDeviceIcon(activity.device)}
                              <span>{activity.device}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <span className="text-xs text-gray-500">
                            {formatDate(activity.timestamp?.toDate?.() || new Date(activity.timestamp))}
                          </span>
                          {!activity.success && (
                            <div className="mt-1">
                              <span className="text-xs bg-red-900 text-red-300 px-2 py-1 rounded">
                                Failed
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )
            })
          )}
        </div>

        {/* Security Recommendations */}
        {securityEvents.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-yellow-900/20 border border-yellow-700 rounded-lg p-6"
          >
            <div className="flex items-center space-x-3 mb-4">
              <Shield className="text-yellow-400" size={24} />
              <h3 className="text-yellow-400 font-semibold">Security Recommendations</h3>
            </div>
            <div className="space-y-2 text-yellow-300">
              <p>• Enable two-factor authentication for enhanced security</p>
              <p>• Review recent login locations and report any suspicious activity</p>
              <p>• Use a strong, unique password for your account</p>
              <p>• Log out from devices you no longer use</p>
            </div>
          </motion.div>
        )}
      </div>
    </ProfileLayout>
  )
}
