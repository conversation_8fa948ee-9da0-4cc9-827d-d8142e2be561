'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Star, 
  TrendingUp, 
  Gift, 
  ShoppingBag, 
  Trophy, 
  Calendar,
  Plus,
  Minus,
  Award,
  Target,
  Filter,
  Download
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import MemberTierDisplay from '@/components/profile/MemberTierDisplay'
import { useUser } from '@/lib/useUser'
import { usePointHistory } from '@/hooks/usePointHistory'
import { useRewards } from '@/hooks/useRewards'
import { getTierInfoByPoints, getTierProgress } from '@/lib/memberTiers'

// Mock data untuk point history
const mockPointHistory = [
  {
    id: 'PT-2024-001',
    date: '2024-01-25',
    type: 'earned',
    source: 'purchase',
    description: 'Order #AC-240101-001 - Dragon Artisan Keycap',
    points: 90,
    balance: 1250,
    details: 'Earned 1 point per $1 spent'
  },
  {
    id: 'PT-2024-002',
    date: '2024-01-24',
    type: 'redeemed',
    source: 'reward',
    description: 'Redeemed: 10% Discount Coupon',
    points: -100,
    balance: 1160,
    details: 'Discount applied to next purchase'
  },
  {
    id: 'PT-2024-003',
    date: '2024-01-20',
    type: 'earned',
    source: 'raffle_win',
    description: 'Raffle Win Bonus - Galaxy Nebula Set',
    points: 200,
    balance: 1260,
    details: 'Bonus points for winning raffle'
  },
  {
    id: 'PT-2024-004',
    date: '2024-01-18',
    type: 'earned',
    source: 'review',
    description: 'Product Review - Cyberpunk Keycap',
    points: 25,
    balance: 1060,
    details: 'Thank you for your detailed review!'
  },
  {
    id: 'PT-2024-005',
    date: '2024-01-15',
    type: 'earned',
    source: 'signup_bonus',
    description: 'Welcome Bonus',
    points: 100,
    balance: 1035,
    details: 'Welcome to ArtisanCaps!'
  },
  {
    id: 'PT-2024-006',
    date: '2024-01-10',
    type: 'earned',
    source: 'purchase',
    description: 'Order #AC-240105-002 - Minimalist Set',
    points: 68,
    balance: 935,
    details: 'Earned 1 point per $1 spent'
  }
]

const pointSourceConfig = {
  purchase: {
    label: 'Purchase',
    icon: ShoppingBag,
    color: 'text-blue-400'
  },
  review: {
    label: 'Review',
    icon: Star,
    color: 'text-yellow-400'
  },
  raffle_win: {
    label: 'Raffle Win',
    icon: Trophy,
    color: 'text-green-400'
  },
  signup_bonus: {
    label: 'Signup Bonus',
    icon: Gift,
    color: 'text-purple-400'
  },
  reward: {
    label: 'Reward Redemption',
    icon: Award,
    color: 'text-red-400'
  },
  referral: {
    label: 'Referral',
    icon: Target,
    color: 'text-indigo-400'
  }
}

const availableRewards = [
  {
    id: 'RW-001',
    name: '5% Discount Coupon',
    points: 50,
    description: 'Get 5% off your next purchase',
    icon: Gift
  },
  {
    id: 'RW-002',
    name: '10% Discount Coupon',
    points: 100,
    description: 'Get 10% off your next purchase',
    icon: Gift
  },
  {
    id: 'RW-003',
    name: 'Free Shipping',
    points: 75,
    description: 'Free shipping on your next order',
    icon: ShoppingBag
  },
  {
    id: 'RW-004',
    name: 'Exclusive Keycap',
    points: 500,
    description: 'Limited edition member-only keycap',
    icon: Star
  }
]

export default function PointHistoryPage() {
  const { user, profile } = useUser()
  const [selectedType, setSelectedType] = useState('all')
  const [selectedPeriod, setSelectedPeriod] = useState('all')

  // Use real-time hooks for Firebase data
  const {
    pointHistory,
    loading: historyLoading,
    currentBalance,
    totalEarned,
    totalRedeemed
  } = usePointHistory(user?.uid || null)

  const {
    rewards: availableRewards,
    loading: rewardsLoading,
    redeemReward: handleRedeemReward
  } = useRewards()

  const loading = historyLoading || rewardsLoading

  if (!user) {
    return (
      <ProfileLayout>
        <div className="text-center py-12">
          <Star className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">Please log in</h3>
          <p className="text-gray-400">You need to be logged in to view your point history.</p>
        </div>
      </ProfileLayout>
    )
  }

  const filteredHistory = pointHistory.filter(entry => {
    const matchesType = selectedType === 'all' || entry.type === selectedType
    // Add period filtering logic here if needed
    return matchesType
  })

  const currentPoints = currentBalance || profile?.points || 0

  // Handle reward redemption
  const handleRewardClick = async (reward: any) => {
    if (!user || currentPoints < reward.pointsCost) return

    const result = await handleRedeemReward(user.uid, reward.id, currentPoints)
    if (result.success) {
      // Show success message - you can add a toast notification here
      console.log('Reward redeemed successfully!')
    } else {
      // Show error message
      console.error('Failed to redeem reward:', result.message)
    }
  }

  if (loading) {
    return (
      <ProfileLayout>
        <div className="space-y-6">
          <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-700 rounded w-1/3 mb-4"></div>
              <div className="h-4 bg-gray-700 rounded w-2/3 mb-6"></div>
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="bg-gray-700/50 rounded-lg p-4">
                    <div className="h-4 bg-gray-600 rounded w-1/2 mb-2"></div>
                    <div className="h-8 bg-gray-600 rounded w-3/4"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout>
      <div className="space-y-6">
        {/* Header & Current Points */}
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-white truncate">
                Point History
              </h1>
              <p className="text-gray-400 mt-1">
                Track your points and redeem rewards
              </p>
            </div>
            <div className="mt-4 lg:mt-0">
              <div className="bg-gradient-to-r from-accent-600 to-accent-700 rounded-lg p-4 text-center">
                <p className="text-accent-100 text-sm">Current Balance</p>
                <p className="text-3xl font-bold text-white">{currentPoints.toLocaleString()}</p>
                <p className="text-accent-200 text-xs">points</p>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="text-green-500" size={20} />
                <span className="text-sm text-gray-400">Total Earned</span>
              </div>
              <p className="text-xl font-bold text-green-400">{totalEarned.toLocaleString()}</p>
            </div>
            <div className="bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Gift className="text-red-500" size={20} />
                <span className="text-sm text-gray-400">Total Redeemed</span>
              </div>
              <p className="text-xl font-bold text-red-400">{totalRedeemed.toLocaleString()}</p>
            </div>
            <div className="bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Award className="text-purple-500" size={20} />
                <span className="text-sm text-gray-400">Lifetime Points</span>
              </div>
              <p className="text-xl font-bold text-purple-400">{(totalEarned).toLocaleString()}</p>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="sm:w-48">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
              >
                <option value="all">All Transactions</option>
                <option value="earned">Points Earned</option>
                <option value="redeemed">Points Redeemed</option>
              </select>
            </div>
            <div className="sm:w-48">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-accent-500"
              >
                <option value="all">All Time</option>
                <option value="30days">Last 30 Days</option>
                <option value="90days">Last 90 Days</option>
                <option value="year">This Year</option>
              </select>
            </div>
            <button className="flex items-center justify-center space-x-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">
              <Download size={16} />
              <span>Export</span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Point History */}
          <div className="lg:col-span-2">
            <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-white mb-4">Transaction History</h2>
              
              <div className="space-y-4">
                {filteredHistory.map((entry) => {
                  const sourceConfig = pointSourceConfig[entry.source] || pointSourceConfig.purchase
                  const Icon = sourceConfig.icon
                  
                  return (
                    <motion.div
                      key={entry.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center space-x-4 p-4 bg-gray-700/30 rounded-lg"
                    >
                      <div className={`p-2 rounded-lg bg-gray-700 ${sourceConfig.color}`}>
                        <Icon size={20} />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="text-sm font-medium text-white truncate">
                            {entry.description}
                          </h3>
                          <div className="flex items-center space-x-2">
                            {entry.type === 'earned' ? (
                              <Plus className="text-green-400" size={16} />
                            ) : (
                              <Minus className="text-red-400" size={16} />
                            )}
                            <span className={`font-bold ${entry.type === 'earned' ? 'text-green-400' : 'text-red-400'}`}>
                              {entry.type === 'earned' ? '+' : ''}{entry.points}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <p className="text-xs text-gray-400">{entry.description}</p>
                          <p className="text-xs text-gray-400">
                            {entry.createdAt?.toDate ? entry.createdAt.toDate().toLocaleDateString() : new Date().toLocaleDateString()} • Balance: {entry.balance}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </div>
          </div>

          {/* Available Rewards */}
          <div className="lg:col-span-1">
            <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-white mb-4">Available Rewards</h2>
              
              <div className="space-y-4">
                {availableRewards.map((reward) => {
                  // Default icon mapping for Firebase rewards
                  const getIcon = (type: string) => {
                    switch (type) {
                      case 'discount': return Gift
                      case 'shipping': return ShoppingBag
                      case 'product': return Star
                      default: return Gift
                    }
                  }
                  const Icon = getIcon(reward.type)
                  const canRedeem = currentPoints >= reward.pointsCost
                  
                  return (
                    <div
                      key={reward.id}
                      className={`p-4 rounded-lg border ${
                        canRedeem 
                          ? 'bg-gray-700/30 border-gray-600' 
                          : 'bg-gray-700/10 border-gray-700'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg ${canRedeem ? 'bg-accent-600' : 'bg-gray-700'}`}>
                          <Icon size={16} className="text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className={`text-sm font-medium mb-1 ${canRedeem ? 'text-white' : 'text-gray-400'}`}>
                            {reward.name}
                          </h3>
                          <p className="text-xs text-gray-400 mb-2">
                            {reward.description}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className={`text-sm font-bold ${canRedeem ? 'text-green-400' : 'text-gray-500'}`}>
                              {reward.pointsCost} points
                            </span>
                            <button
                              disabled={!canRedeem}
                              onClick={() => handleRewardClick(reward)}
                              className={`text-xs px-3 py-1 rounded transition-colors ${
                                canRedeem
                                  ? 'bg-accent-600 hover:bg-accent-700 text-white'
                                  : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                              }`}
                            >
                              {canRedeem ? 'Redeem' : 'Not enough points'}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProfileLayout>
  )
}
