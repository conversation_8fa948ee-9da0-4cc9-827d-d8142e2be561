'use client'

/**
 * Personal Analytics Page
 * 
 * Comprehensive analytics dashboard showing personalized insights,
 * spending patterns, activity trends, and predictive recommendations.
 * 
 * Features:
 * - Personal spending and activity analytics
 * - Interactive charts and visualizations
 * - Predictive insights and recommendations
 * - Community comparisons and rankings
 * - Actionable insights and suggestions
 * 
 * <AUTHOR> Team
 */

import React from 'react'
import { motion } from 'framer-motion'
import {
  BarChart3,
  TrendingUp,
  Users,
  Target,
  Calendar,
  Award,
  DollarSign,
  Activity,
  Brain
} from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import PersonalAnalyticsDashboard from '@/components/profile/PersonalAnalyticsDashboard'
import AdvancedAnalyticsDashboard from '@/components/profile/analytics/AdvancedAnalyticsDashboard'
import EngagementDashboard from '@/components/profile/analytics/EngagementDashboard'
import RecommendationsDashboard from '@/components/profile/recommendations/RecommendationsDashboard'
import { useUser } from '@/lib/useUser'

export default function PersonalAnalyticsPage() {
  const { user, profile } = useUser()
  const [activeTab, setActiveTab] = React.useState<'overview' | 'advanced' | 'engagement' | 'recommendations'>('overview')

  if (!user || !profile) {
    return (
      <ProfileLayout>
        <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6">
          <div className="text-center">
            <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">Please log in</h3>
            <p className="text-gray-400">You need to be logged in to view your analytics.</p>
          </div>
        </div>
      </ProfileLayout>
    )
  }

  return (
    <ProfileLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white flex items-center space-x-3">
                <BarChart3 size={28} />
                <span>Personal Analytics</span>
              </h1>
              <p className="text-gray-400 mt-2">
                Discover insights about your Syndicaps journey, spending patterns, and community engagement
              </p>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-accent-400">{profile.points || 0}</div>
                <div className="text-sm text-gray-400">Total Points</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {profile.profileCompletion?.percentage || 0}%
                </div>
                <div className="text-sm text-gray-400">Profile Complete</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Analytics Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gray-800 rounded-lg p-6 border border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <DollarSign className="text-green-400" size={24} />
              <span className="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded">Lifetime</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">$0.00</div>
            <div className="text-sm text-gray-400">Total Spent</div>
            <div className="mt-2 text-xs text-green-400">+0% from last month</div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gray-800 rounded-lg p-6 border border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <Activity className="text-blue-400" size={24} />
              <span className="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded">30 Days</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">0</div>
            <div className="text-sm text-gray-400">Activities</div>
            <div className="mt-2 text-xs text-blue-400">+0% engagement</div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gray-800 rounded-lg p-6 border border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <Award className="text-yellow-400" size={24} />
              <span className="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded">Total</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">
              {profile.achievements?.length || 0}
            </div>
            <div className="text-sm text-gray-400">Achievements</div>
            <div className="mt-2 text-xs text-yellow-400">Keep earning!</div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gray-800 rounded-lg p-6 border border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <Users className="text-purple-400" size={24} />
              <span className="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded">Rank</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">#--</div>
            <div className="text-sm text-gray-400">Community Rank</div>
            <div className="mt-2 text-xs text-purple-400">Calculating...</div>
          </motion.div>
        </div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden"
        >
          <div className="flex flex-wrap border-b border-gray-700">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'advanced', label: 'Advanced Analytics', icon: TrendingUp },
              { id: 'engagement', label: 'Live Engagement', icon: Activity },
              { id: 'recommendations', label: 'AI Recommendations', icon: Brain }
            ].map((tab) => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`
                    flex items-center space-x-2 px-4 py-3 text-sm font-medium transition-colors
                    ${activeTab === tab.id
                      ? 'bg-accent-500/20 text-accent-400 border-b-2 border-accent-500'
                      : 'text-gray-400 hover:text-white hover:bg-gray-700'
                    }
                  `}
                >
                  <IconComponent size={16} />
                  <span className="hidden sm:inline">{tab.label}</span>
                </button>
              )
            })}
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <PersonalAnalyticsDashboard profile={profile} />
            )}
            {activeTab === 'advanced' && (
              <AdvancedAnalyticsDashboard profile={profile} />
            )}
            {activeTab === 'engagement' && (
              <EngagementDashboard profile={profile} />
            )}
            {activeTab === 'recommendations' && (
              <RecommendationsDashboard profile={profile} />
            )}
          </div>
        </motion.div>

        {/* Quick Insights */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <h2 className="text-xl font-semibold text-white mb-6 flex items-center space-x-2">
            <Target size={24} />
            <span>Quick Insights</span>
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Member Since */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Calendar className="text-blue-400" size={20} />
                <h3 className="text-white font-medium">Member Since</h3>
              </div>
              <div className="text-2xl font-bold text-white">
                {new Date(profile.createdAt instanceof Date ? profile.createdAt : (profile.createdAt as any).toDate()).toLocaleDateString('en-US', {
                  month: 'long',
                  year: 'numeric'
                })}
              </div>
              <p className="text-gray-400 text-sm">
                You've been part of the Syndicaps community for{' '}
                {Math.floor((Date.now() - new Date(profile.createdAt instanceof Date ? profile.createdAt : (profile.createdAt as any).toDate()).getTime()) / (1000 * 60 * 60 * 24))} days
              </p>
            </div>

            {/* Favorite Activity */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <TrendingUp className="text-green-400" size={20} />
                <h3 className="text-white font-medium">Most Active</h3>
              </div>
              <div className="text-2xl font-bold text-white">Profile Building</div>
              <p className="text-gray-400 text-sm">
                You're focused on completing your profile and earning achievements
              </p>
            </div>

            {/* Next Goal */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Target className="text-purple-400" size={20} />
                <h3 className="text-white font-medium">Next Goal</h3>
              </div>
              <div className="text-2xl font-bold text-white">
                {profile.profileCompletion?.percentage === 100 
                  ? 'Make First Purchase' 
                  : 'Complete Profile'
                }
              </div>
              <p className="text-gray-400 text-sm">
                {profile.profileCompletion?.percentage === 100
                  ? 'Browse our shop and make your first keycap purchase'
                  : `${100 - (profile.profileCompletion?.percentage || 0)}% remaining to complete your profile`
                }
              </p>
            </div>
          </div>
        </motion.div>

        {/* Data Export */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-gray-800 rounded-lg p-6 border border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-white mb-2">Data Export</h2>
              <p className="text-gray-400">
                Download your personal data and analytics for your records
              </p>
            </div>
            <button className="btn-secondary">
              Export Data
            </button>
          </div>
        </motion.div>

        {/* Privacy Notice */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-4"
        >
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-bold">i</span>
            </div>
            <div>
              <h3 className="text-blue-300 font-medium mb-1">Privacy & Data</h3>
              <p className="text-blue-200 text-sm">
                Your analytics data is private and only visible to you. We use this information to provide 
                personalized insights and improve your experience. You can export or delete your data at any time.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </ProfileLayout>
  )
}
