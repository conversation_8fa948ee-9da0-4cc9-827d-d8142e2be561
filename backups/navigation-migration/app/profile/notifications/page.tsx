'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, <PERSON>, CheckChe<PERSON>, Trash2, Package, Trophy, Star, AlertCircle, Filter, ExternalLink } from 'lucide-react'
import ProfileLayout from '@/components/profile/ProfileLayout'
import { useUser } from '@/lib/useUser'
import { useNotifications } from '@/lib/useNotifications'
import { useRouter } from 'next/navigation'

const notificationTypes = {
  order_update: { icon: Package, color: 'text-blue-400', bg: 'bg-blue-400/10' },
  raffle_result: { icon: Trophy, color: 'text-yellow-400', bg: 'bg-yellow-400/10' },
  points_earned: { icon: Star, color: 'text-green-400', bg: 'bg-green-400/10' },
  system: { icon: AlertCircle, color: 'text-purple-400', bg: 'bg-purple-400/10' }
}

export default function NotificationsPage() {
  const { user } = useUser()
  const router = useRouter()
  const {
    notifications,
    unreadCount,
    mark<PERSON>Read,
    markAllAsRead,
    removeNotification,
    loading
  } = useNotifications()
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all')
  const [selectedType, setSelectedType] = useState<string | null>(null)

  /**
   * Handle notification click
   */
  const handleNotificationClick = async (notification: any) => {
    // Mark as read if not already read
    if (!notification.read) {
      await markAsRead(notification.id)
    }

    // Handle different notification types
    if (notification.type === 'raffle_result' && notification.data?.won) {
      // Redirect to raffle entries page for winners
      router.push('/profile/raffle-entries')
    } else if (notification.data?.orderId) {
      // Redirect to orders page for order-related notifications
      router.push('/profile/orders')
    }
  }

  if (!user) {
    return (
      <ProfileLayout>
        <div className="text-center py-12">
          <p className="text-gray-400">Please log in to view your notifications.</p>
        </div>
      </ProfileLayout>
    )
  }

  if (loading) {
    return (
      <ProfileLayout>
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading notifications...</p>
        </div>
      </ProfileLayout>
    )
  }

  const filteredNotifications = notifications.filter(notification => {
    const matchesFilter = filter === 'all' ||
      (filter === 'read' && notification.read) ||
      (filter === 'unread' && !notification.read)

    const matchesType = !selectedType || notification.type === selectedType

    return matchesFilter && matchesType
  })

  const getNotificationIcon = (type: string) => {
    const config = notificationTypes[type as keyof typeof notificationTypes]
    return config || notificationTypes.system
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 48) return 'Yesterday'
    return date.toLocaleDateString()
  }

  return (
    <ProfileLayout>
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800 rounded-lg shadow-xl border border-gray-700 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Bell className="text-accent-400" size={28} />
              <div>
                <h1 className="text-2xl font-bold text-white">Notifications</h1>
                <p className="text-gray-400">
                  {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up!'}
                </p>
              </div>
            </div>
            
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="flex items-center space-x-2 bg-accent-600 hover:bg-accent-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <CheckCheck size={18} />
                <span>Mark all read</span>
              </button>
            )}
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <Filter size={18} className="text-gray-400" />
              <span className="text-gray-400">Filter:</span>
            </div>
            
            <div className="flex space-x-2">
              {['all', 'unread', 'read'].map((filterType) => (
                <button
                  key={filterType}
                  onClick={() => setFilter(filterType as any)}
                  className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                    filter === filterType
                      ? 'bg-accent-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
                </button>
              ))}
            </div>

            <div className="flex space-x-2">
              {Object.keys(notificationTypes).map((type) => {
                const config = getNotificationIcon(type)
                const Icon = config.icon
                return (
                  <button
                    key={type}
                    onClick={() => setSelectedType(selectedType === type ? null : type)}
                    className={`flex items-center space-x-1 px-3 py-1 rounded-lg text-sm transition-colors ${
                      selectedType === type
                        ? 'bg-accent-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    <Icon size={14} />
                    <span>{type.replace('_', ' ')}</span>
                  </button>
                )
              })}
            </div>
          </div>
        </motion.div>

        {/* Notifications List */}
        <div className="space-y-4">
          {filteredNotifications.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-12 bg-gray-800 rounded-lg border border-gray-700"
            >
              <Bell className="mx-auto text-gray-600 mb-4" size={48} />
              <h3 className="text-lg font-semibold text-white mb-2">No notifications</h3>
              <p className="text-gray-400">
                {filter === 'unread' ? 'No unread notifications' : 'No notifications to show'}
              </p>
            </motion.div>
          ) : (
            filteredNotifications.map((notification, index) => {
              const config = getNotificationIcon(notification.type)
              const Icon = config.icon
              
              return (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`bg-gray-800 rounded-lg border border-gray-700 p-6 transition-all hover:border-gray-600 cursor-pointer ${
                    !notification.read ? 'border-l-4 border-l-accent-500' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`p-2 rounded-lg ${config.bg}`}>
                      <Icon className={config.color} size={20} />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className={`font-semibold ${notification.read ? 'text-gray-300' : 'text-white'}`}>
                            {notification.title}
                          </h3>
                          <p className={`mt-1 ${notification.read ? 'text-gray-400' : 'text-gray-300'}`}>
                            {notification.message}
                          </p>
                          
                          {/* Additional data */}
                          {notification.data && (
                            <div className="mt-2 text-sm text-gray-500">
                              {notification.data.trackingNumber && (
                                <span>Tracking: {notification.data.trackingNumber}</span>
                              )}
                              {notification.data.points && (
                                <span>+{notification.data.points} points</span>
                              )}
                              {notification.data.paymentDeadline && (
                                <span className="text-yellow-400">
                                  Payment due: {new Date(notification.data.paymentDeadline).toLocaleDateString()}
                                </span>
                              )}
                              {notification.type === 'raffle_result' && notification.data.won && (
                                <div className="flex items-center gap-1 text-accent-400 mt-1">
                                  <ExternalLink size={12} />
                                  <span>Click to view payment options</span>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center space-x-2 ml-4">
                          <span className="text-xs text-gray-500">
                            {formatDate(notification.createdAt)}
                          </span>
                          
                          {!notification.read && (
                            <button
                              onClick={() => markAsRead(notification.id)}
                              className="p-1 text-gray-400 hover:text-accent-400 transition-colors"
                              title="Mark as read"
                            >
                              <Check size={16} />
                            </button>
                          )}
                          
                          <button
                            onClick={() => removeNotification(notification.id)}
                            className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                            title="Delete notification"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )
            })
          )}
        </div>
      </div>
    </ProfileLayout>
  )
}
